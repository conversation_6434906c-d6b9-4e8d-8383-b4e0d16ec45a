{"consumes": ["application/json"], "produces": ["application/json"], "schemes": ["http", "https"], "swagger": "2.0", "info": {"description": "Description: meet_app_api service", "title": "meet_app_api", "version": "0.0.1"}, "host": "localhost:40002", "basePath": "/", "paths": {"/app/v1/appVersion/compareVersion": {"post": {"description": "Compare Version | 对比版本是否需要更新", "tags": ["appversion"], "summary": "Compare Version | 对比版本是否需要更新", "operationId": "CompareVersion", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/AppVersionCompareReq"}}], "responses": {"200": {"description": "AppVersionInfoResp", "schema": {"$ref": "#/definitions/AppVersionInfoResp"}}}}}, "/app/v1/area": {"post": {"description": "Get Area By ID | 通过ID获取区", "tags": ["area"], "summary": "Get Area By ID | 通过ID获取区", "operationId": "GetAreaById", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDReq"}}], "responses": {"200": {"description": "AreaInfoResp", "schema": {"$ref": "#/definitions/AreaInfoResp"}}}}}, "/app/v1/area/list": {"post": {"description": "Get Area List | 获取区列表", "tags": ["area"], "summary": "Get Area List | 获取区列表", "operationId": "GetAreaList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/AreaListReq"}}], "responses": {"200": {"description": "AreaListResp", "schema": {"$ref": "#/definitions/AreaListResp"}}}}}, "/app/v1/article": {"post": {"description": "Get Article By ID | 通过ID获取文章", "tags": ["article"], "summary": "Get Article By ID | 通过ID获取文章", "operationId": "GetArticleById", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDReq"}}], "responses": {"200": {"description": "ArticleInfoResp", "schema": {"$ref": "#/definitions/ArticleInfoResp"}}}}}, "/app/v1/article/list": {"post": {"description": "Get Article List | 获取文章列表", "tags": ["article"], "summary": "Get Article List | 获取文章列表", "operationId": "GetArticleList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/ArticleListReq"}}], "responses": {"200": {"description": "ArticleListResp", "schema": {"$ref": "#/definitions/ArticleListResp"}}}}}, "/app/v1/banner": {"post": {"description": "Get Banner By ID | 通过ID获取轮播图", "tags": ["banner"], "summary": "Get Banner By ID | 通过ID获取轮播图", "operationId": "GetBannerById", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDReq"}}], "responses": {"200": {"description": "BannerInfoResp", "schema": {"$ref": "#/definitions/BannerInfoResp"}}}}}, "/app/v1/banner/list": {"post": {"description": "Get Banner List | 获取轮播图列表", "tags": ["banner"], "summary": "Get Banner List | 获取轮播图列表", "operationId": "GetBannerList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/BannerListReq"}}], "responses": {"200": {"description": "BannerListResp", "schema": {"$ref": "#/definitions/BannerListResp"}}}}}, "/app/v1/city": {"post": {"description": "Get City By ID | 通过ID获取城市", "tags": ["city"], "summary": "Get City By ID | 通过ID获取城市", "operationId": "GetCityById", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDReq"}}], "responses": {"200": {"description": "CityInfoResp", "schema": {"$ref": "#/definitions/CityInfoResp"}}}}}, "/app/v1/city/list": {"post": {"description": "Get City List | 获取城市列表", "tags": ["city"], "summary": "Get City List | 获取城市列表", "operationId": "GetCityList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/CityListReq"}}], "responses": {"200": {"description": "CityListResp", "schema": {"$ref": "#/definitions/CityListResp"}}}}}, "/app/v1/configuration": {"get": {"description": "Get public system configuration | 获取公开系统参数", "tags": ["publicapi"], "summary": "Get public system configuration | 获取公开系统参数", "operationId": "GetPublicSystemConfiguration", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/ConfigurationtReq"}}], "responses": {"200": {"description": "ConfigurationResp", "schema": {"$ref": "#/definitions/ConfigurationResp"}}}}}, "/app/v1/group": {"post": {"description": "Get Group By ID | 获取圈子信息", "tags": ["group"], "summary": "Get Group By ID | 获取圈子信息", "operationId": "GetGroupById", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDReq"}}], "responses": {"200": {"description": "GroupInfoResp", "schema": {"$ref": "#/definitions/GroupInfoResp"}}}}}, "/app/v1/group/list": {"post": {"description": "Get Group List | 获取圈子列表", "tags": ["group"], "summary": "Get Group List | 获取圈子列表", "operationId": "GetGroupList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/GroupListReq"}}], "responses": {"200": {"description": "GroupListResp", "schema": {"$ref": "#/definitions/GroupListResp"}}}}}, "/app/v1/group/userFollowList": {"post": {"description": "Get User Follow List | 获取加入的圈子列表", "tags": ["group"], "summary": "Get User Follow List | 获取加入的圈子列表", "operationId": "GetUserFollowList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/GroupListReq"}}], "responses": {"200": {"description": "GroupListResp", "schema": {"$ref": "#/definitions/GroupListResp"}}}}}, "/app/v1/groupFollow/create": {"post": {"description": "Create Group Follow | 加入圈子", "tags": ["groupfollow"], "summary": "Create Group Follow | 加入圈子", "operationId": "CreateGroupFollow", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/GroupFollowInfo"}}], "responses": {"200": {"description": "GroupFollowInfoResp", "schema": {"$ref": "#/definitions/GroupFollowInfoResp"}}}}}, "/app/v1/groupFollow/update": {"post": {"description": "Update Group Follow | 退出圈子", "tags": ["groupfollow"], "summary": "Update Group Follow | 退出圈子", "operationId": "UpdateGroupFollow", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/GroupFollowInfo"}}], "responses": {"200": {"description": "GroupFollowInfoResp", "schema": {"$ref": "#/definitions/GroupFollowInfoResp"}}}}}, "/app/v1/groupMember/list": {"post": {"description": "Get Group Member List | 获取圈主列表", "tags": ["groupmember"], "summary": "Get Group Member List | 获取圈主列表", "operationId": "GetGroupMemberList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/GroupMemberListReq"}}], "responses": {"200": {"description": "GroupMemberListResp", "schema": {"$ref": "#/definitions/GroupMemberListResp"}}}}}, "/app/v1/license/activate": {"post": {"description": "Activate License | 激活License", "tags": ["malllicense"], "summary": "Activate License | 激活License", "operationId": "ActivateLicense", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/ActivateLicenseReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/app/v1/license/bindDevice": {"post": {"description": "Bind Device | 绑定设备", "tags": ["malllicense"], "summary": "Bind Device | 绑定设备", "operationId": "BindDevice", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/BindDeviceReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/app/v1/license/getMachineCode": {"post": {"description": "Get Machine Code | 获取机器码", "tags": ["malllicense"], "summary": "Get Machine Code | 获取机器码", "operationId": "GetMachineCode", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/GetMachineCodeReq"}}], "responses": {"200": {"description": "GetMachineCodeResp", "schema": {"$ref": "#/definitions/GetMachineCodeResp"}}}}}, "/app/v1/license/getServerStatus": {"post": {"description": "Get Server Status | 获取服务器状态", "tags": ["malllicense"], "summary": "Get Server Status | 获取服务器状态", "operationId": "GetServerStatus", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/EmptyReq"}}], "responses": {"200": {"description": "ServerStatusResp", "schema": {"$ref": "#/definitions/ServerStatusResp"}}}}}, "/app/v1/license/getServerTime": {"post": {"description": "Get Server Timest | 获取服务器时间", "tags": ["malllicense"], "summary": "Get Server Timest | 获取服务器时间", "operationId": "GetServerTime", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/EmptyReq"}}], "responses": {"200": {"description": "ServerTimeResp", "schema": {"$ref": "#/definitions/ServerTimeResp"}}}}}, "/app/v1/license/heartbeat": {"post": {"description": "Heartbeat Request | 心跳请求", "tags": ["malllicense"], "summary": "Heartbeat Request | 心跳请求", "operationId": "Heartbeat", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/HeartbeatReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/app/v1/license/query": {"post": {"description": "Query License | 查询License", "tags": ["malllicense"], "summary": "Query License | 查询License", "operationId": "QueryLicense", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/QueryLicenseReq"}}], "responses": {"200": {"description": "QueryLicenseResp", "schema": {"$ref": "#/definitions/QueryLicenseResp"}}}}}, "/app/v1/license/unbindDevice": {"post": {"description": "Unbind Device | 解绑设备", "tags": ["malllicense"], "summary": "Unbind Device | 解绑设备", "operationId": "UnbindDevice", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/UnbindDeviceReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/app/v1/maintenInfo": {"post": {"description": "Get Mainten Info By ID | 通过ID获取维修单", "tags": ["mainteninfo"], "summary": "Get Mainten Info By ID | 通过ID获取维修单", "operationId": "GetMaintenInfoById", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDReq"}}], "responses": {"200": {"description": "MaintenInfoInfoResp", "schema": {"$ref": "#/definitions/MaintenInfoInfoResp"}}}}}, "/app/v1/maintenInfo/create": {"post": {"description": "Create Mainten Info | 创建维修单", "tags": ["mainteninfo"], "summary": "Create Mainten Info | 创建维修单", "operationId": "CreateMaintenInfo", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/MaintenInfoInfo"}}], "responses": {"200": {"description": "MaintenInfoInfoResp", "schema": {"$ref": "#/definitions/MaintenInfoInfoResp"}}}}}, "/app/v1/maintenInfo/list": {"post": {"description": "Get Mainten Info List | 获取维修单列表", "tags": ["mainteninfo"], "summary": "Get Mainten Info List | 获取维修单列表", "operationId": "GetMaintenInfoList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/MaintenInfoListReq"}}], "responses": {"200": {"description": "MaintenInfoListResp", "schema": {"$ref": "#/definitions/MaintenInfoListResp"}}}}}, "/app/v1/mallOrder": {"post": {"description": "Get mall order by ID | 通过ID获取订单", "tags": ["mallorder"], "summary": "Get mall order by ID | 通过ID获取订单", "operationId": "GetMallOrderById", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDReq"}}], "responses": {"200": {"description": "MallOrderInfoResp", "schema": {"$ref": "#/definitions/MallOrderInfoResp"}}}}}, "/app/v1/mallOrder/anonymous": {"post": {"description": "Get mall order by ID | 通过ID获取匿名订单", "tags": ["anonymous<PERSON><PERSON>er"], "summary": "Get mall order by ID | 通过ID获取匿名订单", "operationId": "GetAnonymousMallOrderById", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/AnonymousMallOrderReq"}}], "responses": {"200": {"description": "MallOrderInfoResp", "schema": {"$ref": "#/definitions/MallOrderInfoResp"}}}}}, "/app/v1/mallOrder/anonymous/create": {"post": {"description": "Create anonymous mall order information | 创建匿名订单", "tags": ["anonymous<PERSON><PERSON>er"], "summary": "Create anonymous mall order information | 创建匿名订单", "operationId": "CreateAnonymousMallOrder", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/CreateAnonymousMallOrderReq"}}], "responses": {"200": {"description": "CreateMallOrderResp", "schema": {"$ref": "#/definitions/CreateMallOrderResp"}}}}}, "/app/v1/mallOrder/anonymous/list": {"post": {"description": "Get anonymous mall order list | 获取匿名订单列表", "tags": ["anonymous<PERSON><PERSON>er"], "summary": "Get anonymous mall order list | 获取匿名订单列表", "operationId": "GetAnonymousMallOrderList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/AnonymousMallOrderListReq"}}], "responses": {"200": {"description": "MallOrderListResp", "schema": {"$ref": "#/definitions/MallOrderListResp"}}}}}, "/app/v1/mallOrder/create": {"post": {"description": "Create mall order information | 创建订单", "tags": ["mallorder"], "summary": "Create mall order information | 创建订单", "operationId": "CreateMallOrder", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/CreateMallOrderReq"}}], "responses": {"200": {"description": "CreateMallOrderResp", "schema": {"$ref": "#/definitions/CreateMallOrderResp"}}}}}, "/app/v1/mallOrder/list": {"post": {"description": "Get mall order list | 获取订单列表", "tags": ["mallorder"], "summary": "Get mall order list | 获取订单列表", "operationId": "GetMallOrderList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/MallOrderListReq"}}], "responses": {"200": {"description": "MallOrderListResp", "schema": {"$ref": "#/definitions/MallOrderListResp"}}}}}, "/app/v1/mallOrder/payment/status": {"post": {"description": "Query payment status | 查询支付状态", "tags": ["payment"], "summary": "Query payment status | 查询支付状态", "operationId": "QueryPaymentStatus", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/QueryPaymentStatusReq"}}], "responses": {"200": {"description": "QueryPaymentStatusResp", "schema": {"$ref": "#/definitions/QueryPaymentStatusResp"}}}}}, "/app/v1/mallProduct": {"post": {"description": "Get mall product by ID | 通过ID获取商品信息", "tags": ["mallproduct"], "summary": "Get mall product by ID | 通过ID获取商品信息", "operationId": "GetMallProductById", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/GetMallProductByDeviceIdAndMachineCodeReq"}}], "responses": {"200": {"description": "MallProductResp", "schema": {"$ref": "#/definitions/MallProductResp"}}}}}, "/app/v1/mallProduct/getByAppPackageName": {"post": {"description": "Get mall product by App package name | 通过应用包名获取商品信息", "tags": ["mallproduct"], "summary": "Get mall product by App package name | 通过应用包名获取商品信息", "operationId": "GetMallProductByAppPackageName", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/AppPackageNameReq"}}], "responses": {"200": {"description": "MallProductResp", "schema": {"$ref": "#/definitions/MallProductResp"}}}}}, "/app/v1/mallProduct/list": {"post": {"description": "Get mall product list | 获取商品信息列表", "tags": ["mallproduct"], "summary": "Get mall product list | 获取商品信息列表", "operationId": "GetMallProductList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/MallProductListReq"}}], "responses": {"200": {"description": "MallProductListResp", "schema": {"$ref": "#/definitions/MallProductListResp"}}}}}, "/app/v1/mallProductSku": {"post": {"description": "Get mall product sku by ID | 通过ID获取商品SKU信息", "tags": ["mallproductsku"], "summary": "Get mall product sku by ID | 通过ID获取商品SKU信息", "operationId": "GetMallProductSkuById", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDReq"}}], "responses": {"200": {"description": "MallProductSkuInfoResp", "schema": {"$ref": "#/definitions/MallProductSkuInfoResp"}}}}}, "/app/v1/mallProductSku/getByDeviceIdAndMachineCode": {"post": {"description": "Get Mall Product By DeviceId and MachineCode | 通过设备ID和机器码获取商品SKU信息", "tags": ["mallproductsku"], "summary": "Get Mall Product By DeviceId and MachineCode | 通过设备ID和机器码获取商品SKU信息", "operationId": "GetMallProductByDeviceIdAndMachineCode", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/GetMallProductByDeviceIdAndMachineCodeReq"}}], "responses": {"200": {"description": "MallProductSkuInfoResp", "schema": {"$ref": "#/definitions/MallProductSkuInfoResp"}}}}}, "/app/v1/mallProductSku/list": {"post": {"description": "Get mall product sku list | 获取商品SKU信息列表", "tags": ["mallproductsku"], "summary": "Get mall product sku list | 获取商品SKU信息列表", "operationId": "GetMallProductSkuList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/MallProductSkuListReq"}}], "responses": {"200": {"description": "MallProductSkuListResp", "schema": {"$ref": "#/definitions/MallProductSkuListResp"}}}}}, "/app/v1/payment/mock/notify/{channelCode}": {"post": {"description": "Mock Payment Notify | 模拟支付回调", "tags": ["payment"], "summary": "Mock Payment Notify | 模拟支付回调", "operationId": "MockPaymentNotify", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/MockNotifyReq"}}], "responses": {"200": {"$ref": "#/responses/string"}}}}, "/app/v1/payment/notify/{channelCode}": {"post": {"description": "Payment Notify | 支付回调", "tags": ["payment"], "summary": "Payment Notify | 支付回调", "operationId": "PaymentNotify", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/NotifyReq"}}], "responses": {"200": {"$ref": "#/responses/string"}}}}, "/app/v1/payment/submit": {"post": {"description": "Submit payment order | 提交支付订单", "tags": ["payment"], "summary": "Submit payment order | 提交支付订单", "operationId": "SubmitPayOrder", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/MallOrderSubmitReq"}}], "responses": {"200": {"description": "MallOrderSubmitResp", "schema": {"$ref": "#/definitions/MallOrderSubmitResp"}}}}}, "/app/v1/payment/success/alipay": {"get": {"description": "Payment Succee | 同步完成支付", "tags": ["payment"], "summary": "Payment Succee | 同步完成支付", "operationId": "AliPaySuccess", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/AlipaySuccessReq"}}], "responses": {"200": {"$ref": "#/responses/string"}}}}, "/app/v1/product": {"post": {"description": "Get Product By ID | 通过ID获取产品信息", "tags": ["product"], "summary": "Get Product By ID | 通过ID获取产品信息", "operationId": "GetProductById", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDReq"}}], "responses": {"200": {"description": "ProductInfoResp", "schema": {"$ref": "#/definitions/ProductInfoResp"}}}}}, "/app/v1/product/list": {"post": {"description": "Get Product List | 获取产品列表", "tags": ["product"], "summary": "Get Product List | 获取产品列表", "operationId": "GetProductList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/ProductListReq"}}], "responses": {"200": {"description": "ProductListResp", "schema": {"$ref": "#/definitions/ProductListResp"}}}}}, "/app/v1/product/update": {"post": {"description": "Update Product | 更新产品", "tags": ["product"], "summary": "Update Product | 更新产品", "operationId": "UpdateProduct", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/ProductInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/app/v1/province": {"post": {"description": "Get Province By ID | 通过ID获取省份", "tags": ["province"], "summary": "Get Province By ID | 通过ID获取省份", "operationId": "GetProvinceById", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDReq"}}], "responses": {"200": {"description": "ProvinceInfoResp", "schema": {"$ref": "#/definitions/ProvinceInfoResp"}}}}}, "/app/v1/province/list": {"post": {"description": "Get Province List | 获取省份列表", "tags": ["province"], "summary": "Get Province List | 获取省份列表", "operationId": "GetProvinceList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/ProvinceListReq"}}], "responses": {"200": {"description": "ProvinceListResp", "schema": {"$ref": "#/definitions/ProvinceListResp"}}}}}, "/app/v1/refund/mock/notify/{channelCode}": {"post": {"description": "Mock Refund Notify | 模拟退款回调", "tags": ["payment"], "summary": "Mock Refund Notify | 模拟退款回调", "operationId": "MockRefundNotify", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/MockNotifyReq"}}], "responses": {"200": {"$ref": "#/responses/string"}}}}, "/app/v1/refund/notify/{channelCode}": {"post": {"description": "Refund Notify | 退款回调", "tags": ["payment"], "summary": "Refund Notify | 退款回调", "operationId": "RefundNotify", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/NotifyReq"}}], "responses": {"200": {"$ref": "#/responses/string"}}}}, "/app/v1/segment": {"post": {"description": "Get Segment By ID | 通过ID获取栏目信息", "tags": ["segment"], "summary": "Get Segment By ID | 通过ID获取栏目信息", "operationId": "GetSegmentById", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDReq"}}], "responses": {"200": {"description": "SegmentInfoResp", "schema": {"$ref": "#/definitions/SegmentInfoResp"}}}}}, "/app/v1/segment/list": {"post": {"description": "Get Segment List | 获取栏目列表", "tags": ["segment"], "summary": "Get Segment List | 获取栏目列表", "operationId": "GetSegmentList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/SegmentListReq"}}], "responses": {"200": {"description": "SegmentListResp", "schema": {"$ref": "#/definitions/SegmentListResp"}}}}}, "/app/v1/segmentArticleRecord": {"post": {"description": "Get segment article record by ID | 通过ID获取SegmentArticleRecord", "tags": ["segmentarticlerecord"], "summary": "Get segment article record by ID | 通过ID获取SegmentArticleRecord", "operationId": "GetSegmentArticleRecordById", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDReq"}}], "responses": {"200": {"description": "SegmentArticleRecordInfoResp", "schema": {"$ref": "#/definitions/SegmentArticleRecordInfoResp"}}}}}, "/app/v1/segmentArticleRecord/list": {"post": {"description": "Get segment article record list | 获取SegmentArticleRecord列表", "tags": ["segmentarticlerecord"], "summary": "Get segment article record list | 获取SegmentArticleRecord列表", "operationId": "GetSegmentArticleRecordList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/SegmentArticleRecordListReq"}}], "responses": {"200": {"description": "SegmentArticleRecordListResp", "schema": {"$ref": "#/definitions/SegmentArticleRecordListResp"}}}}}, "/app/v1/street": {"post": {"description": "Get Street By ID | 通过ID获取街道", "tags": ["street"], "summary": "Get Street By ID | 通过ID获取街道", "operationId": "GetStreetById", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDReq"}}], "responses": {"200": {"description": "StreetInfoResp", "schema": {"$ref": "#/definitions/StreetInfoResp"}}}}}, "/app/v1/street/list": {"post": {"description": "Get Street List | 获取街道列表", "tags": ["street"], "summary": "Get Street List | 获取街道列表", "operationId": "GetStreetList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/StreetListReq"}}], "responses": {"200": {"description": "StreetListResp", "schema": {"$ref": "#/definitions/StreetListResp"}}}}}, "/app/v1/tag": {"post": {"description": "Get Tag By ID | 获取话题信息", "tags": ["tag"], "summary": "Get Tag By ID | 获取话题信息", "operationId": "GetTagById", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDReq"}}], "responses": {"200": {"description": "TagInfoResp", "schema": {"$ref": "#/definitions/TagInfoResp"}}}}}, "/app/v1/tag/create": {"post": {"description": "Create Tag | 创建话题", "tags": ["tag"], "summary": "Create Tag | 创建话题", "operationId": "CreateTag", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/TagInfo"}}], "responses": {"200": {"description": "TagInfoResp", "schema": {"$ref": "#/definitions/TagInfoResp"}}}}}, "/app/v1/tag/list": {"post": {"description": "Get Tag List | 获取话题列表", "tags": ["tag"], "summary": "Get Tag List | 获取话题列表", "operationId": "GetTagList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/TagListReq"}}], "responses": {"200": {"description": "TagListResp", "schema": {"$ref": "#/definitions/TagListResp"}}}}}, "/app/v1/tagFollow/create": {"post": {"description": "Create Tag Follow | 关注话题", "tags": ["tagfollow"], "summary": "Create Tag Follow | 关注话题", "operationId": "CreateTagFollow", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/TagFollowInfo"}}], "responses": {"200": {"description": "TagFollowInfoResp", "schema": {"$ref": "#/definitions/TagFollowInfoResp"}}}}}, "/app/v1/tagFollow/update": {"post": {"description": "Update Tag Follow | 取消关注话题", "tags": ["tagfollow"], "summary": "Update Tag Follow | 取消关注话题", "operationId": "UpdateTagFollow", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/TagFollowInfo"}}], "responses": {"200": {"description": "TagFollowInfoResp", "schema": {"$ref": "#/definitions/TagFollowInfoResp"}}}}}, "/app/v1/userAddres": {"post": {"description": "Get User Addres By ID | 获取用户地址信息", "tags": ["useraddres"], "summary": "Get User Addres By ID | 获取用户地址信息", "operationId": "GetUserAddresById", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDReq"}}], "responses": {"200": {"description": "UserAddresInfoResp", "schema": {"$ref": "#/definitions/UserAddresInfoResp"}}}}}, "/app/v1/userAddres/create": {"post": {"description": "Create User Addres | 创建用户地址", "tags": ["useraddres"], "summary": "Create User Addres | 创建用户地址", "operationId": "CreateUserAddres", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/UserAddresInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/app/v1/userAddres/delete": {"post": {"description": "Delete User Addres | 删除用户地址", "tags": ["useraddres"], "summary": "Delete User Addres | 删除用户地址", "operationId": "DeleteUserAddres", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDsReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/app/v1/userAddres/list": {"post": {"description": "Get User Addres List | 获取用户地址列表", "tags": ["useraddres"], "summary": "Get User Addres List | 获取用户地址列表", "operationId": "GetUserAddresList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/UserAddresListReq"}}], "responses": {"200": {"description": "UserAddresListResp", "schema": {"$ref": "#/definitions/UserAddresListResp"}}}}}, "/app/v1/userAddres/update": {"post": {"description": "Update User Addres | 修改用户地址", "tags": ["useraddres"], "summary": "Update User Addres | 修改用户地址", "operationId": "UpdateUserAddres", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/UserAddresInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/app/v1/userAgreement": {"post": {"description": "Get user agreement by ID | 通过ID获取UserAgreement信息", "tags": ["useragreement"], "summary": "Get user agreement by ID | 通过ID获取UserAgreement信息", "operationId": "GetUserAgreementById", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDReq"}}], "responses": {"200": {"description": "UserAgreementInfoResp", "schema": {"$ref": "#/definitions/UserAgreementInfoResp"}}}}}, "/app/v1/userAgreement/getUserAgreementByKey": {"post": {"description": "Get", "tags": ["useragreement"], "summary": "Get", "operationId": "GetUserAgreementByKey", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/GetUserAgreementByKeyReq"}}], "responses": {"200": {"description": "UserAgreementInfoResp", "schema": {"$ref": "#/definitions/UserAgreementInfoResp"}}}}}, "/bolo_lexicon/list": {"post": {"description": "Get bolo lexicon list | 获取BoloLexicon信息列表", "tags": ["bololexicon"], "summary": "Get bolo lexicon list | 获取BoloLexicon信息列表", "operationId": "GetBoloLexiconList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/BoloLexiconListReq"}}], "responses": {"200": {"description": "BoloLexiconListResp", "schema": {"$ref": "#/definitions/BoloLexiconListResp"}}}}}, "/mall_order_item": {"post": {"description": "Get mall order item by ID | 通过ID获取MallOrderItem信息", "tags": ["mallorderitem"], "summary": "Get mall order item by ID | 通过ID获取MallOrderItem信息", "operationId": "GetMallOrderItemById", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDReq"}}], "responses": {"200": {"description": "MallOrderItemInfoResp", "schema": {"$ref": "#/definitions/MallOrderItemInfoResp"}}}}}, "/mall_order_item/list": {"post": {"description": "Get mall order item list | 获取MallOrderItem信息列表", "tags": ["mallorderitem"], "summary": "Get mall order item list | 获取MallOrderItem信息列表", "operationId": "GetMallOrderItemList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/MallOrderItemListReq"}}], "responses": {"200": {"description": "MallOrderItemListResp", "schema": {"$ref": "#/definitions/MallOrderItemListResp"}}}}}, "/rental_device": {"post": {"description": "Get rental device by ID | 通过ID获取RentalDevice信息", "tags": ["rentaldevice"], "summary": "Get rental device by ID | 通过ID获取RentalDevice信息", "operationId": "GetRentalDeviceById", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDReq"}}], "responses": {"200": {"description": "RentalDeviceInfoResp", "schema": {"$ref": "#/definitions/RentalDeviceInfoResp"}}}}}, "/rental_device/create": {"post": {"description": "Create rental device information | 创建RentalDevice信息", "tags": ["rentaldevice"], "summary": "Create rental device information | 创建RentalDevice信息", "operationId": "CreateRentalDevice", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/RentalDeviceInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/rental_device/delete": {"post": {"description": "Delete rental device information | 删除RentalDevice信息", "tags": ["rentaldevice"], "summary": "Delete rental device information | 删除RentalDevice信息", "operationId": "DeleteRentalDevice", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDsReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/rental_device/list": {"post": {"description": "Get rental device list | 获取RentalDevice信息列表", "tags": ["rentaldevice"], "summary": "Get rental device list | 获取RentalDevice信息列表", "operationId": "GetRentalDeviceList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/RentalDeviceListReq"}}], "responses": {"200": {"description": "RentalDeviceListResp", "schema": {"$ref": "#/definitions/RentalDeviceListResp"}}}}}, "/rental_device/update": {"post": {"description": "Update rental device information | 更新RentalDevice信息", "tags": ["rentaldevice"], "summary": "Update rental device information | 更新RentalDevice信息", "operationId": "UpdateRentalDevice", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/RentalDeviceInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/v1/article/admin/delete": {"delete": {"description": "Admin Delete Article | 管理员删除文章", "tags": ["admin"], "summary": "Admin Delete Article | 管理员删除文章", "operationId": "AdminDeleteArticle", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/AdminArticleDeleteReq"}}], "responses": {"200": {"description": "BaseDataInfo", "schema": {"$ref": "#/definitions/BaseDataInfo"}}}}}, "/v1/article/content/image/auditedCallback": {"post": {"description": "Image Audited Callback | 图片审核结果", "tags": ["article"], "summary": "Image Audited Callback | 图片审核结果", "operationId": "ImageAuditedCallback", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/ImageAuditedCallbackReq"}}], "responses": {"200": {"description": "AuditedCallbackResp", "schema": {"$ref": "#/definitions/AuditedCallbackResp"}}}}}, "/v1/article/content/video/auditedCallback": {"post": {"description": "Video Audited Callback | 视频审核结果", "tags": ["article"], "summary": "Video Audited Callback | 视频审核结果", "operationId": "VideoAuditedCallback", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/VideoAuditedCallbackReq"}}], "responses": {"200": {"description": "AuditedCallbackResp", "schema": {"$ref": "#/definitions/AuditedCallbackResp"}}}}}, "/v1/article/delete": {"delete": {"description": "Delete Article | 删除文章", "tags": ["article"], "summary": "Delete Article | 删除文章", "operationId": "ArticleDelete", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/ArticleDeleteReq"}}], "responses": {"200": {"description": "CommonResp", "schema": {"$ref": "#/definitions/CommonResp"}}}}}, "/v1/article/detail": {"post": {"description": "Get Article Detail | 话题详情", "tags": ["article"], "summary": "Get Article Detail | 话题详情", "operationId": "ArticleDetail", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/ArticleDetailReq"}}], "responses": {"200": {"description": "ArticleDetailResp", "schema": {"$ref": "#/definitions/ArticleDetailResp"}}}}}, "/v1/article/draft/lists": {"post": {"description": "Get Article Draft List | 获取文章草稿列表", "tags": ["article"], "summary": "Get Article Draft List | 获取文章草稿列表", "operationId": "ArticleDraftLists", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/ArticleDraftListsReq"}}], "responses": {"200": {"description": "ArticleDraftListsResp", "schema": {"$ref": "#/definitions/ArticleDraftListsResp"}}}}}, "/v1/article/draft/save": {"post": {"description": "Save Draft | 保存草稿", "tags": ["article"], "summary": "Save Draft | 保存草稿", "operationId": "ArticleSaveDraft", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/ArticlSaveDraftReq"}}], "responses": {"200": {"description": "ArticlSaveDraftResp", "schema": {"$ref": "#/definitions/ArticlSaveDraftResp"}}}}}, "/v1/article/like/lists": {"post": {"description": "Get Article Like List | 文章点赞列表", "tags": ["article"], "summary": "Get Article Like List | 文章点赞列表", "operationId": "ArticleLikeList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/ArticleLikeListReq"}}], "responses": {"200": {"description": "ArticleLikeListResp", "schema": {"$ref": "#/definitions/ArticleLikeListResp"}}}}}, "/v1/article/lists": {"post": {"description": "Get Article List | 话题列表", "tags": ["article"], "summary": "Get Article List | 话题列表", "operationId": "ArticleLists", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/ArticleListsReq"}}], "responses": {"200": {"description": "ArticleListsResp", "schema": {"$ref": "#/definitions/ArticleListsResp"}}}}}, "/v1/article/listsByGroupId": {"post": {"description": "Get Article List By GroupId | 根据群组ID获取话题列表", "tags": ["article"], "summary": "Get Article List By GroupId | 根据群组ID获取话题列表", "operationId": "ArticleListsByGroupId", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/ArticleListsByGroupIdReq"}}], "responses": {"200": {"description": "ArticleListsResp", "schema": {"$ref": "#/definitions/ArticleListsResp"}}}}}, "/v1/article/listsByTagId": {"post": {"description": "Get Article List By TagId | 根据标签ID获取话题列表", "tags": ["article"], "summary": "Get Article List By TagId | 根据标签ID获取话题列表", "operationId": "ArticleListsByTagId", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/ArticleListsByTagIdReq"}}], "responses": {"200": {"description": "ArticleListsResp", "schema": {"$ref": "#/definitions/ArticleListsResp"}}}}}, "/v1/article/publish": {"post": {"description": "Publish Article | 发布话题", "tags": ["article"], "summary": "Publish Article | 发布话题", "operationId": "Publish", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/PublishReq"}}], "responses": {"200": {"description": "PublishResp", "schema": {"$ref": "#/definitions/PublishResp"}}}}}, "/v1/article/reply": {"post": {"description": "Reply Article | 评论文章", "tags": ["article"], "summary": "Reply Article | 评论文章", "operationId": "Reply", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/ReplyReq"}}], "responses": {"200": {"description": "ReplyResp", "schema": {"$ref": "#/definitions/ReplyResp"}}}}}, "/v1/article/reply/admin/delete": {"delete": {"description": "Admin Delete Reply | 管理员删除评论", "tags": ["admin"], "summary": "Admin Delete Reply | 管理员删除评论", "operationId": "AdminDeleteReply", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/AdminDelReplyReq"}}], "responses": {"200": {"description": "BaseDataInfo", "schema": {"$ref": "#/definitions/BaseDataInfo"}}}}}, "/v1/article/reply/delete": {"delete": {"description": "Delete Reply | 删除评论", "tags": ["article"], "summary": "Delete Reply | 删除评论", "operationId": "DeleteReply", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/DelReplyReq"}}], "responses": {"200": {"description": "DelReplyResp", "schema": {"$ref": "#/definitions/DelReplyResp"}}}}}, "/v1/article/reply/lists": {"post": {"description": "Get Reply List | 文章评论列表", "tags": ["article"], "summary": "Get Reply List | 文章评论列表", "operationId": "ReplyList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/ReplyListReq"}}], "responses": {"200": {"description": "ReplyListResp", "schema": {"$ref": "#/definitions/ReplyListResp"}}}}}, "/v1/article/reply/userlists": {"post": {"description": "Get User Reply List | 用户评论列表", "tags": ["article"], "summary": "Get User Reply List | 用户评论列表", "operationId": "UserReplyList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/UserReplyListReq"}}], "responses": {"200": {"description": "ReplyListResp", "schema": {"$ref": "#/definitions/ReplyListResp"}}}}}, "/v1/article/upload/cos/policy": {"post": {"description": "Get Tecent Cloud COS Post Policy | 获取腾讯云COS Post Policy", "tags": ["article"], "summary": "Get Tecent Cloud COS Post Policy | 获取腾讯云COS Post Policy", "operationId": "GetTecentCloudCosPostPolicy", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/TecentCloudUploadPostPolicyReq"}}], "responses": {"200": {"description": "TecentCloudUploadPostPolicyResp", "schema": {"$ref": "#/definitions/TecentCloudUploadPostPolicyResp"}}}}}, "/v1/article/upload/cos/token": {"post": {"description": "Get Tecent Cloud STS Temporary Key | 获取腾讯云STS临时密钥", "tags": ["article"], "summary": "Get Tecent Cloud STS Temporary Key | 获取腾讯云STS临时密钥", "operationId": "GetTecentCloudUploadToken", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/TecentCloudUploadTokenReq"}}], "responses": {"200": {"description": "TecentCloudUploadTokenResp", "schema": {"$ref": "#/definitions/TecentCloudUploadTokenResp"}}}}}, "/v1/article/upload/cover": {"post": {"description": "Upload Cover | 上传封面", "tags": ["article"], "summary": "Upload Cover | 上传封面", "operationId": "UploadCover", "responses": {"200": {"description": "UploadCoverResp", "schema": {"$ref": "#/definitions/UploadCoverResp"}}}}}, "/v1/article/userlists": {"post": {"description": "Get User Article List | 用户话题列表", "tags": ["article"], "summary": "Get User Article List | 用户话题列表", "operationId": "UserArticleLists", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/UserArticleListsReq"}}], "responses": {"200": {"description": "ArticleListsResp", "schema": {"$ref": "#/definitions/ArticleListsResp"}}}}}, "/v1/article/view/add": {"post": {"description": "Add Article View Record | 添加文章阅读记录", "tags": ["article"], "summary": "Add Article View Record | 添加文章阅读记录", "operationId": "AddArticleViewRecord", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/AddArticleViewRecordReq"}}], "responses": {"200": {"description": "AddArticleViewRecordResp", "schema": {"$ref": "#/definitions/AddArticleViewRecordResp"}}}}}, "/v1/geetestVerify": {"post": {"description": "Geetest Verify | 极验验证", "tags": ["user"], "summary": "Geetest Verify | 极验验证", "operationId": "GeetestVerify", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/GeetestVerifyReq"}}], "responses": {"200": {"description": "GeetestVerifyResp", "schema": {"$ref": "#/definitions/GeetestVerifyResp"}}}}}, "/v1/user/admin/ban": {"post": {"description": "Admin Ban User | 管理员封禁用户", "tags": ["admin"], "summary": "Admin Ban User | 管理员封禁用户", "operationId": "AdminBanUser", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/AdminBanUserReq"}}], "responses": {"200": {"description": "BaseDataInfo", "schema": {"$ref": "#/definitions/BaseDataInfo"}}}}}, "/v1/user/admin/mute": {"post": {"description": "Admin Mute User | 管理员禁言用户", "tags": ["admin"], "summary": "Admin Mute User | 管理员禁言用户", "operationId": "AdminMuteUser", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/AdminMuteUserReq"}}], "responses": {"200": {"description": "BaseDataInfo", "schema": {"$ref": "#/definitions/BaseDataInfo"}}}}}, "/v1/user/admin/unban": {"post": {"description": "Admin Unban User | 管理员解封用户", "tags": ["admin"], "summary": "Admin Unban User | 管理员解封用户", "operationId": "AdminUnbanUser", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/AdminUnbanUserReq"}}], "responses": {"200": {"description": "BaseDataInfo", "schema": {"$ref": "#/definitions/BaseDataInfo"}}}}}, "/v1/user/admin/unmute": {"post": {"description": "Admin Unmute User | 管理员解禁用户", "tags": ["admin"], "summary": "Admin Unmute User | 管理员解禁用户", "operationId": "AdminUnmuteUser", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/AdminUnmuteUserReq"}}], "responses": {"200": {"description": "BaseDataInfo", "schema": {"$ref": "#/definitions/BaseDataInfo"}}}}}, "/v1/user/bindWxByMobile": {"post": {"description": "Bind WX By Mobile | 绑定用户微信号", "tags": ["user"], "summary": "Bind WX By Mobile | 绑定用户微信号", "operationId": "BindWxByMobile", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/BindWxByMobileReq"}}], "responses": {"200": {"description": "LoginResp", "schema": {"$ref": "#/definitions/LoginResp"}}}}}, "/v1/user/detail": {"post": {"description": "Get Current Login User Info | 当前登陆用户详情", "tags": ["user"], "summary": "Get Current Login User Info | 当前登陆用户详情", "operationId": "Detail", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/UserInfoReq"}}], "responses": {"200": {"description": "AppletUserInfoResp", "schema": {"$ref": "#/definitions/AppletUserInfoResp"}}}}}, "/v1/user/email/code": {"post": {"description": "Send Email Code | 发送邮箱验证 (用于验证邮箱)", "tags": ["user"], "summary": "Send Email Code | 发送邮箱验证 (用于验证邮箱)", "operationId": "SendEmailCode", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/SendEmailCodeReq"}}], "responses": {"200": {"description": "SendEmailCodeResp", "schema": {"$ref": "#/definitions/SendEmailCodeResp"}}}}}, "/v1/user/email/code/verify": {"post": {"description": "Verify Email Code | 验证邮箱验证码", "tags": ["user"], "summary": "Verify Email Code | 验证邮箱验证码", "operationId": "VerifiedEmailCode", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/VerifyEmailCodeReq"}}], "responses": {"200": {"description": "VerifyEmailCodeResp", "schema": {"$ref": "#/definitions/VerifyEmailCodeResp"}}}}}, "/v1/user/email/set": {"post": {"description": "Get user's permission code | 设置用户邮箱", "tags": ["user"], "summary": "Get user's permission code | 设置用户邮箱", "operationId": "SetUserEmail", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/SetUserEmailReq"}}], "responses": {"200": {"description": "BaseDataInfo", "schema": {"$ref": "#/definitions/BaseDataInfo"}}}}}, "/v1/user/fanslists": {"post": {"description": "Get Fans List | 用户粉丝列表", "tags": ["user"], "summary": "Get Fans List | 用户粉丝列表", "operationId": "FansList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/FansListReq"}}], "responses": {"200": {"description": "FansListResp", "schema": {"$ref": "#/definitions/FansListResp"}}}}}, "/v1/user/follow": {"post": {"description": "Follow User | 用户关注", "tags": ["user"], "summary": "Follow User | 用户关注", "operationId": "Follow", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/FollowReq"}}], "responses": {"200": {"description": "FollowResp", "schema": {"$ref": "#/definitions/FollowResp"}}}}}, "/v1/user/followlists": {"post": {"description": "Get Follow List | 用户关注列表", "tags": ["user"], "summary": "Get Follow List | 用户关注列表", "operationId": "FollowList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/FollowListReq"}}], "responses": {"200": {"description": "FollowListResp", "schema": {"$ref": "#/definitions/FollowListResp"}}}}}, "/v1/user/getUserInfoByMobile": {"post": {"description": "Get User Info By Mobile | 根据手机号获取用户详情", "tags": ["user"], "summary": "Get User Info By Mobile | 根据手机号获取用户详情", "operationId": "GetUserInfoByMobile", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/UserInfoByMobileReq"}}], "responses": {"200": {"description": "UserInfoByUserIdResp", "schema": {"$ref": "#/definitions/UserInfoByUserIdResp"}}}}}, "/v1/user/getUserInfoByName": {"post": {"description": "Get User Info By Name | 根据用户名获取用户详情", "tags": ["user"], "summary": "Get User Info By Name | 根据用户名获取用户详情", "operationId": "GetUserInfoByName", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/UserInfoByNameReq"}}], "responses": {"200": {"description": "AppletUserInfoResp", "schema": {"$ref": "#/definitions/AppletUserInfoResp"}}}}}, "/v1/user/getUserInfoByUserId": {"post": {"description": "Get User Info By UserId | 根据用户ID获取用户详情", "tags": ["user"], "summary": "Get User Info By UserId | 根据用户ID获取用户详情", "operationId": "GetUserInfoByUserId", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/UserInfoByUserIdReq"}}], "responses": {"200": {"description": "UserInfoByUserIdResp", "schema": {"$ref": "#/definitions/UserInfoByUserIdResp"}}}}}, "/v1/user/getcode": {"post": {"description": "Get Send Sms Code | 根据手机号获取已经发送验证码(自动化测试专用)", "tags": ["user"], "summary": "Get Send Sms Code | 根据手机号获取已经发送验证码(自动化测试专用)", "operationId": "GetSendSmsCode", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/GetSendSmsCodeReq"}}], "responses": {"200": {"description": "GetSendSmsCodeResp", "schema": {"$ref": "#/definitions/GetSendSmsCodeResp"}}}}}, "/v1/user/im/getUserSig": {"post": {"description": "Get User Sig | 获取用户签名", "tags": ["user"], "summary": "Get User Sig | 获取用户签名", "operationId": "GetUserSig", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/GetUserSigReq"}}], "responses": {"200": {"description": "GetUserSigResp", "schema": {"$ref": "#/definitions/GetUserSigResp"}}}}}, "/v1/user/isthumbup": {"post": {"description": "Is Thumbup | 用户是否已点赞", "tags": ["user"], "summary": "Is Thumbup | 用户是否已点赞", "operationId": "IsThumbup", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IsThumbupReq"}}], "responses": {"200": {"description": "IsThumbupResp", "schema": {"$ref": "#/definitions/IsThumbupResp"}}}}}, "/v1/user/login": {"post": {"description": "User Login | 用户登录", "tags": ["user"], "summary": "User Login | 用户登录", "operationId": "<PERSON><PERSON>", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/LoginReq"}}], "responses": {"200": {"description": "LoginResp", "schema": {"$ref": "#/definitions/LoginResp"}}}}}, "/v1/user/message/lists": {"post": {"description": "Get Message List | 获取用户消息列表", "tags": ["user"], "summary": "Get Message List | 获取用户消息列表", "operationId": "GetMessageList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/GetMessageListReq"}}], "responses": {"200": {"description": "GetMessageListResp", "schema": {"$ref": "#/definitions/GetMessageListResp"}}}}}, "/v1/user/msessage/read": {"post": {"description": "Read Message | 阅读消息接口", "tags": ["user"], "summary": "Read Message | 阅读消息接口", "operationId": "ReadMessage", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/ReadMessageReq"}}], "responses": {"200": {"description": "ReadMessageResp", "schema": {"$ref": "#/definitions/ReadMessageResp"}}}}}, "/v1/user/perm": {"get": {"description": "Get user's permission code | 获取用户权限码", "tags": ["user"], "summary": "Get user's permission code | 获取用户权限码", "operationId": "GetUserPermCode", "responses": {"200": {"description": "PermCodeResp", "schema": {"$ref": "#/definitions/PermCodeResp"}}}}}, "/v1/user/product/add": {"post": {"description": "Add User Product | 添加产品", "tags": ["user"], "summary": "Add User Product | 添加产品", "operationId": "AddUserProduct", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/AddUserProductReq"}}], "responses": {"200": {"description": "AddUserProductResp", "schema": {"$ref": "#/definitions/AddUserProductResp"}}}}}, "/v1/user/product/lists": {"post": {"description": "Get User Product List | 查询用户产品", "tags": ["user"], "summary": "Get User Product List | 查询用户产品", "operationId": "GetUserProductList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/GetUserProductListReq"}}], "responses": {"200": {"description": "GetUserProductListResp", "schema": {"$ref": "#/definitions/GetUserProductListResp"}}}}}, "/v1/user/realname": {"post": {"description": "RealNameAuthentication | 实名认证", "tags": ["user"], "summary": "RealNameAuthentication | 实名认证", "operationId": "RealNameAuthentication", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/RealNameAuthReq"}}], "responses": {"200": {"description": "RealNameAuthResp", "schema": {"$ref": "#/definitions/RealNameAuthResp"}}}}}, "/v1/user/register": {"post": {"description": "User Register | 用户注册", "tags": ["user"], "summary": "User Register | 用户注册", "operationId": "Register", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/RegisterReq"}}], "responses": {"200": {"description": "RegisterResp", "schema": {"$ref": "#/definitions/RegisterResp"}}}}}, "/v1/user/registerByAuthId": {"post": {"description": "Register By AuthId | 通过授权信息注册用户", "tags": ["user"], "summary": "Register By AuthId | 通过授权信息注册用户", "operationId": "RegisterByAuthId", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/RegisterByAuthIdReq"}}], "responses": {"200": {"description": "LoginResp", "schema": {"$ref": "#/definitions/LoginResp"}}}}}, "/v1/user/session/create": {"post": {"description": " 创建登录session（用于同设备登录）", "tags": ["user"], "summary": " 创建登录session（用于同设备登录）", "operationId": "CreateUserMobileSsoLoginSession", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/CreateUserMobileSsoLoginSessionReq"}}], "responses": {"200": {"description": "CreateUserMobileSsoLoginSessionResp", "schema": {"$ref": "#/definitions/CreateUserMobileSsoLoginSessionResp"}}}}}, "/v1/user/session/delete": {"delete": {"description": " 删除登录session（用于同设备登录）", "tags": ["user"], "summary": " 删除登录session（用于同设备登录）", "operationId": "DeleteUserMobileSsoLoginSession", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/DeleteUserMobileSsoLoginSessionReq"}}], "responses": {"200": {"description": "DeleteUserMobileSsoLoginSessionResp", "schema": {"$ref": "#/definitions/DeleteUserMobileSsoLoginSessionResp"}}}}}, "/v1/user/session/getMobileSSOUserLoginInfo": {"post": {"description": "获取用户登录信息（用于同设备登录）", "tags": ["user"], "summary": "获取用户登录信息（用于同设备登录）", "operationId": "GetUserMobileSsoUserLoginInfo", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/GetUserMobileSsoUserLoginInfoReq"}}], "responses": {"200": {"description": "GetUserMobileSsoUserLoginInfoResp", "schema": {"$ref": "#/definitions/GetUserMobileSsoUserLoginInfoResp"}}}}}, "/v1/user/session/getTicket": {"post": {"description": "获取登录Ticket（用于同设备登录）", "tags": ["user"], "summary": "获取登录Ticket（用于同设备登录）", "operationId": "GetUserMobileSsoTicket", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/GetUserMobileSsoTicketReq"}}], "responses": {"200": {"description": "GetUserMobileSsoTicketResp", "schema": {"$ref": "#/definitions/GetUserMobileSsoTicketResp"}}}}}, "/v1/user/setAvatar": {"post": {"description": "Set Avatar | 修改头像", "tags": ["user"], "summary": "Set Avatar | 修改头像", "operationId": "Set<PERSON><PERSON><PERSON>", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/SetAvatarReq"}}], "responses": {"200": {"description": "SetAvatarResp", "schema": {"$ref": "#/definitions/SetAvatarResp"}}}}}, "/v1/user/setBackgroundImage": {"post": {"description": "Set Background Image | 设置背景图", "tags": ["user"], "summary": "Set Background Image | 设置背景图", "operationId": "SetBackgroundImage", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/SetBackgroundImageReq"}}], "responses": {"200": {"description": "SetBackgroundImageResp", "schema": {"$ref": "#/definitions/SetBackgroundImageResp"}}}}}, "/v1/user/setNickname": {"post": {"description": "Set Nickname | 修改用户名", "tags": ["user"], "summary": "Set Nickname | 修改用户名", "operationId": "SetNickname", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/SetNicknameReq"}}], "responses": {"200": {"description": "SetNicknameResp", "schema": {"$ref": "#/definitions/SetNicknameResp"}}}}}, "/v1/user/setPassword": {"post": {"description": "Set Password | 设置密码", "tags": ["user"], "summary": "Set Password | 设置密码", "operationId": "SetPassword", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/SetPasswordReq"}}], "responses": {"200": {"description": "SetPasswordResp", "schema": {"$ref": "#/definitions/SetPasswordResp"}}}}}, "/v1/user/setUserDescription": {"post": {"description": "Set User Description | 修改用户介绍", "tags": ["user"], "summary": "Set User Description | 修改用户介绍", "operationId": "SetUserDescription", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/SetUserDescriptionReq"}}], "responses": {"200": {"description": "SetUserDescriptionResp", "schema": {"$ref": "#/definitions/SetUserDescriptionResp"}}}}}, "/v1/user/setUserHomePage": {"post": {"description": "Set User Home Page | 修改用户主页", "tags": ["user"], "summary": "Set User Home Page | 修改用户主页", "operationId": "SetUserHomePage", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/SetUserHomePageReq"}}], "responses": {"200": {"description": "SetUserHomePageResp", "schema": {"$ref": "#/definitions/SetUserHomePageResp"}}}}}, "/v1/user/setUserInfo": {"post": {"description": "Set User Info | 修改用户简介", "tags": ["user"], "summary": "Set User Info | 修改用户简介", "operationId": "SetUserInfo", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/SetUserInfoReq"}}], "responses": {"200": {"description": "SetAppletUserInfoResp", "schema": {"$ref": "#/definitions/SetAppletUserInfoResp"}}}}}, "/v1/user/setUserSex": {"post": {"description": "Set User Sex | 修改用户性别", "tags": ["user"], "summary": "Set User Sex | 修改用户性别", "operationId": "SetUserSex", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/SetUserSexReq"}}], "responses": {"200": {"description": "SetUserSexResp", "schema": {"$ref": "#/definitions/SetUserSexResp"}}}}}, "/v1/user/thumbup": {"post": {"description": "Thumbup | 用户点赞", "tags": ["user"], "summary": "Thumbup | 用户点赞", "operationId": "<PERSON><PERSON>bu<PERSON>", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/ThumbupReq"}}], "responses": {"200": {"description": "ThumbupResp", "schema": {"$ref": "#/definitions/ThumbupResp"}}}}}, "/v1/user/unfollow": {"post": {"description": "UnFollow User | 用户取消关注", "tags": ["user"], "summary": "UnFollow User | 用户取消关注", "operationId": "UnFollow", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/UnFollowReq"}}], "responses": {"200": {"description": "UnFollowResp", "schema": {"$ref": "#/definitions/UnFollowResp"}}}}}, "/v1/user/updatePassword": {"post": {"description": "Update Password | 修改密码", "tags": ["user"], "summary": "Update Password | 修改密码", "operationId": "UpdatePassword", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/UpdatePasswordReq"}}], "responses": {"200": {"description": "UpdatePasswordResp", "schema": {"$ref": "#/definitions/UpdatePasswordResp"}}}}}, "/v1/user/verifylogin": {"post": {"description": "Verify Login | 验证码登录", "tags": ["user"], "summary": "Verify Login | 验证码登录", "operationId": "VerifyLogin", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/VerifyLoginReq"}}], "responses": {"200": {"description": "LoginResp", "schema": {"$ref": "#/definitions/LoginResp"}}}}}, "/v1/user/verifyloginByAuthType": {"post": {"description": "Verify Login By AuthType | 验证码登录(AuthType)", "tags": ["user"], "summary": "Verify Login By AuthType | 验证码登录(AuthType)", "operationId": "VerifyLoginByAuthType", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/VerifyLoginByAuthTypeReq"}}], "responses": {"200": {"description": "LoginResp", "schema": {"$ref": "#/definitions/LoginResp"}}}}}, "/v1/user/wxAuth": {"post": {"description": "WX Auth | 微信登录", "tags": ["user"], "summary": "WX Auth | 微信登录", "operationId": "WxAuth", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/WXAuthReq"}}], "responses": {"200": {"description": "WXAuthResp", "schema": {"$ref": "#/definitions/WXAuthResp"}}}}}, "/v1/user/wxMiniAuth": {"post": {"description": "WX Mini Auth | 微信小程序授权登录", "tags": ["user"], "summary": "WX Mini Auth | 微信小程序授权登录", "operationId": "WxMiniAuth", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/WXMiniAuthReq"}}], "responses": {"200": {"description": "WXMiniAuthResp", "schema": {"$ref": "#/definitions/WXMiniAuthResp"}}}}}, "/v1/verification": {"post": {"description": "Sms Verification | 短信验证", "tags": ["user"], "summary": "Sms Verification | 短信验证", "operationId": "Verification", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/VerificationReq"}}], "responses": {"200": {"description": "VerificationResp", "schema": {"$ref": "#/definitions/VerificationResp"}}}}}}, "definitions": {"ActivateLicenseReq": {"description": "Activate License | 激活License", "type": "object", "required": ["licenseKey"], "properties": {"appCode": {"type": "string", "x-go-name": "AppCode"}, "deviceId": {"type": "string", "x-go-name": "DeviceId"}, "deviceType": {"type": "string", "x-go-name": "DeviceType"}, "licenseKey": {"type": "string", "x-go-name": "LicenseKey"}, "machineCode": {"type": "string", "x-go-name": "MachineCode"}, "nonce": {"type": "string", "x-go-name": "<PERSON><PERSON>"}, "signature": {"type": "string", "x-go-name": "Signature"}, "timestamp": {"type": "integer", "format": "int64", "x-go-name": "Timestamp"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ActivateLicenseResp": {"description": "Activate License Response | 激活License响应", "type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/ActivateLicenseRespData"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ActivateLicenseRespData": {"description": "Activate License Response Data | 激活License响应数据", "type": "object", "properties": {"clientExpirationTime": {"description": "Client Expiration Time | 客户端过期时间戳", "type": "integer", "format": "int64", "x-go-name": "ClientExpirationTime"}, "nonce": {"description": "Nonce | 随机字符串", "type": "string", "x-go-name": "<PERSON><PERSON>"}, "serverExpirationTime": {"description": "Server Expiration Time | 服务端过期时间戳", "type": "integer", "format": "int64", "x-go-name": "ServerExpirationTime"}, "serverTime": {"description": "Server Time | 服务器时间戳", "type": "integer", "format": "int64", "x-go-name": "ServerTime"}, "signature": {"description": "Signature | 签名", "type": "string", "x-go-name": "Signature"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "AddArticleViewRecordReq": {"type": "object", "properties": {"bizId": {"type": "string", "x-go-name": "BizId"}, "objId": {"type": "integer", "format": "int64", "x-go-name": "ObjId"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "AddArticleViewRecordResp": {"type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/AddArticleViewRecordRespData"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "AddArticleViewRecordRespData": {"type": "object", "properties": {"bizId": {"type": "string", "x-go-name": "BizId"}, "objId": {"type": "integer", "format": "int64", "x-go-name": "ObjId"}, "viewNum": {"type": "integer", "format": "int64", "x-go-name": "ViewNum"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "AddUserProductReq": {"type": "object", "properties": {"sn": {"type": "string", "x-go-name": "Sn"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "AddUserProductResp": {"type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/AddUserProductRespData"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "AddUserProductRespData": {"type": "object", "properties": {"color": {"type": "string", "x-go-name": "Color"}, "id": {"type": "integer", "format": "int64", "x-go-name": "ID"}, "language": {"type": "string", "x-go-name": "Language"}, "merchant": {"type": "string", "x-go-name": "Merchant"}, "registerTime": {"type": "string", "x-go-name": "RegisterTime"}, "sn": {"type": "string", "x-go-name": "Sn"}, "title": {"type": "string", "x-go-name": "Title"}, "userId": {"type": "integer", "format": "int64", "x-go-name": "UserID"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "AdminArticleDeleteReq": {"description": "管理员删除文章", "type": "object", "required": ["articleId"], "properties": {"articleId": {"type": "integer", "format": "uint64", "x-go-name": "ArticleId"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "AdminBanUserReq": {"description": "管理员封禁用户", "type": "object", "required": ["userId", "time"], "properties": {"time": {"type": "integer", "format": "int64", "x-go-name": "Time"}, "userId": {"type": "integer", "format": "uint64", "x-go-name": "UserId"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "AdminDelReplyReq": {"description": "管理员删除评论", "type": "object", "required": ["replyId"], "properties": {"replyId": {"type": "integer", "format": "uint64", "x-go-name": "ReplyId"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "AdminMuteUserReq": {"description": "管理员禁言用户", "type": "object", "required": ["userId", "time"], "properties": {"time": {"type": "integer", "format": "int64", "x-go-name": "Time"}, "userId": {"type": "integer", "format": "uint64", "x-go-name": "UserId"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "AdminUnbanUserReq": {"description": "管理员解封用户", "type": "object", "required": ["userId"], "properties": {"userId": {"type": "integer", "format": "uint64", "x-go-name": "UserId"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "AdminUnmuteUserReq": {"description": "管理员解禁用户", "type": "object", "required": ["userId"], "properties": {"userId": {"type": "integer", "format": "uint64", "x-go-name": "UserId"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "AdsInfo": {"type": "object", "properties": {"count": {"type": "integer", "format": "int64", "x-go-name": "Count"}, "hit_flag": {"type": "integer", "format": "int64", "x-go-name": "HitFlag"}, "label": {"type": "string", "x-go-name": "Label"}, "score": {"type": "integer", "format": "int64", "x-go-name": "Score"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "AlipaySuccessReq": {"description": "Alipay Success Request | 支付成功请求", "type": "object", "properties": {"AppId": {"description": "应用ID", "type": "string"}, "OutTradeNo": {"description": "订单编号", "type": "string"}, "SellerId": {"description": "卖家ID", "type": "string"}, "Sign": {"description": "签名", "type": "string"}, "SignType": {"description": "签名类型", "type": "string"}, "Timestamp": {"description": "时间戳", "type": "string"}, "TotalAmount": {"description": "总金额", "type": "string"}, "TradeNo": {"description": "支付宝交易号", "type": "string"}, "Version": {"description": "版本", "type": "string"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "AnonymousMallOrderListReq": {"description": "匿名订单列表", "type": "object", "required": ["page", "pageSize"], "properties": {"deviceId": {"description": "Device Id | 设备ID", "type": "string", "x-go-name": "DeviceId"}, "email": {"description": "Email | 邮箱", "type": "string", "x-go-name": "Email"}, "machineCode": {"description": "Machine Code | 机器码", "type": "string", "x-go-name": "MachineCode"}, "page": {"description": "Page number | 第几页", "type": "integer", "format": "uint64", "minimum": 0, "x-go-name": "Page"}, "pageSize": {"description": "Page size | 单页数据行数", "type": "integer", "format": "uint64", "maximum": 100000, "x-go-name": "PageSize"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "AnonymousMallOrderReq": {"description": "匿名订单", "type": "object", "properties": {"deviceId": {"description": "Device Id | 设备ID", "type": "string", "x-go-name": "DeviceId"}, "email": {"description": "Email | 邮箱", "type": "string", "x-go-name": "Email"}, "machineCode": {"description": "Machine Code | 机器码", "type": "string", "x-go-name": "MachineCode"}, "orderNo": {"description": "订单编号", "type": "string", "x-go-name": "OrderNo"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "AppPackageNameReq": {"description": "Get mall product by App package name request | 通过应用包名获取商品信息请求", "type": "object", "required": ["appPackageName"], "properties": {"appPackageName": {"description": "应用包名", "type": "string", "x-go-name": "AppPackageName"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "AppVersionCompareReq": {"description": "AppVersionCompare", "type": "object", "properties": {"appid": {"type": "string", "x-go-name": "App<PERSON>"}, "channelId": {"type": "string", "x-go-name": "ChannelId"}, "editionNumber": {"type": "integer", "format": "int32", "x-go-name": "EditionNumber"}, "platform": {"type": "string", "x-go-name": "Platform"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "AppVersionInfo": {"description": "The response data of app version information | AppVersion信息", "type": "object", "properties": {"createdAt": {"description": "Create date | 创建日期", "type": "integer", "format": "int64", "x-go-name": "CreatedAt"}, "describe": {"description": "版本更新内容 支持<br>自动换行", "type": "string", "x-go-name": "Describe"}, "editionForce": {"description": "是否强制更新 0代表否 1代表是", "type": "integer", "format": "int32", "x-go-name": "EditionForce"}, "editionIssue": {"description": "是否发行  0否 1是 为了控制上架应用市场审核时不能弹出热更新框", "type": "integer", "format": "int32", "x-go-name": "EditionIssue"}, "editionName": {"description": "版本名称 manifest里的版本名称", "type": "string", "x-go-name": "EditionName"}, "editionNumber": {"description": "本号 最重要的manifest里的版本号 （检查更新主要以服务器返回的edition_number版本号是否大于当前app的版本号来实现是否更新）", "type": "integer", "format": "int32", "x-go-name": "EditionNumber"}, "editionSilence": {"description": "是否静默更新 0代表否 1代表是", "type": "integer", "format": "int32", "x-go-name": "EditionSilence"}, "editionUrl": {"description": "apk、wgt包下载地址或者应用市场地址  安卓应用市场 market://details?id=xxxx 苹果store itms-apps://itunes.apple.com/cn/app/xxxxxx", "type": "string", "x-go-name": "EditionUrl"}, "id": {"description": "ID", "type": "integer", "format": "uint64", "x-go-name": "Id"}, "packageType": {"description": "0是整包升级（apk或者appstore或者安卓应用市场） 1是wgt升级", "type": "integer", "format": "int32", "x-go-name": "PackageType"}, "status": {"description": "状态", "type": "integer", "format": "uint32", "x-go-name": "Status"}, "updatedAt": {"description": "Update date | 更新日期", "type": "integer", "format": "int64", "x-go-name": "UpdatedAt"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "AppVersionInfoReq": {"type": "object", "properties": {"createdAt": {"description": "Create date | 创建日期", "type": "integer", "format": "int64", "x-go-name": "CreatedAt"}, "describe": {"description": "版本更新内容 支持<br>自动换行", "type": "string", "x-go-name": "Describe"}, "editionForce": {"description": "是否强制更新 0代表否 1代表是", "type": "integer", "format": "int32", "x-go-name": "EditionForce"}, "editionIssue": {"description": "是否发行  0否 1是 为了控制上架应用市场审核时不能弹出热更新框", "type": "integer", "format": "int32", "x-go-name": "EditionIssue"}, "editionName": {"description": "版本名称 manifest里的版本名称", "type": "string", "x-go-name": "EditionName"}, "editionNumber": {"description": "本号 最重要的manifest里的版本号 （检查更新主要以服务器返回的edition_number版本号是否大于当前app的版本号来实现是否更新）", "type": "integer", "format": "int32", "x-go-name": "EditionNumber"}, "editionSilence": {"description": "是否静默更新 0代表否 1代表是", "type": "integer", "format": "int32", "x-go-name": "EditionSilence"}, "editionUrl": {"description": "apk、wgt包下载地址或者应用市场地址  安卓应用市场 market://details?id=xxxx 苹果store itms-apps://itunes.apple.com/cn/app/xxxxxx", "type": "string", "x-go-name": "EditionUrl"}, "id": {"description": "ID", "type": "integer", "format": "uint64", "x-go-name": "Id"}, "packageType": {"description": "0是整包升级（apk或者appstore或者安卓应用市场） 1是wgt升级", "type": "integer", "format": "int32", "x-go-name": "PackageType"}, "status": {"description": "状态", "type": "integer", "format": "uint32", "x-go-name": "Status"}, "updatedAt": {"description": "Update date | 更新日期", "type": "integer", "format": "int64", "x-go-name": "UpdatedAt"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "AppVersionInfoResp": {"description": "AppVersion information response | AppVersion信息返回体", "type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/AppVersionInfo"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "AppVersionListInfo": {"description": "AppVersion list data | AppVersion列表数据", "type": "object", "properties": {"data": {"description": "The API list data | AppVersion列表数据", "type": "array", "items": {"$ref": "#/definitions/AppVersionInfo"}, "x-go-name": "Data"}, "total": {"description": "The total number of data | 数据总数", "type": "integer", "format": "uint64", "x-go-name": "Total"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "AppVersionListReq": {"description": "Get app version list request params | AppVersion列表请求参数", "type": "object", "required": ["page", "pageSize"], "properties": {"page": {"description": "Page number | 第几页", "type": "integer", "format": "uint64", "minimum": 0, "x-go-name": "Page"}, "pageSize": {"description": "Page size | 单页数据行数", "type": "integer", "format": "uint64", "maximum": 100000, "x-go-name": "PageSize"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "AppVersionListResp": {"description": "The response data of app version list | AppVersion列表数据", "type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/AppVersionListInfo"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "AppletUserInfoResp": {"type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/AppletUserInfoRespData"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "AppletUserInfoRespData": {"type": "object", "properties": {"userInfo": {"$ref": "#/definitions/User"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "AreaInfo": {"description": "The response data of area information | Area信息", "type": "object", "properties": {"cityCode": {"description": "CityCode", "type": "string", "x-go-name": "CityCode"}, "code": {"description": "Code", "type": "string", "x-go-name": "Code"}, "geocode": {"description": "Geocode", "type": "string", "x-go-name": "Geocode"}, "id": {"type": "integer", "format": "uint64", "x-go-name": "Id"}, "latitude": {"description": "Latitude", "type": "string", "x-go-name": "Latitude"}, "level": {"description": "Level", "type": "integer", "format": "int32", "x-go-name": "Level"}, "longitude": {"description": "Longitude", "type": "string", "x-go-name": "Longitude"}, "name": {"description": "Name", "type": "string", "x-go-name": "Name"}, "parentCode": {"description": "ParentCode", "type": "string", "x-go-name": "ParentCode"}, "provinceCode": {"description": "ProvinceCode", "type": "string", "x-go-name": "ProvinceCode"}, "sort": {"description": "Sort", "type": "integer", "format": "int32", "x-go-name": "Sort"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "AreaInfoResp": {"description": "Area information response | Area信息返回体", "type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/AreaInfo"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "AreaListInfo": {"description": "Area list data | Area列表数据", "type": "object", "properties": {"data": {"description": "The API list data | Area列表数据", "type": "array", "items": {"$ref": "#/definitions/AreaInfo"}, "x-go-name": "Data"}, "total": {"description": "The total number of data | 数据总数", "type": "integer", "format": "uint64", "x-go-name": "Total"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "AreaListReq": {"description": "Get area list request params | Area列表请求参数", "type": "object", "required": ["page", "pageSize"], "properties": {"cityCode": {"description": "CityCode", "type": "string", "x-go-name": "CityCode"}, "code": {"description": "Code", "type": "string", "x-go-name": "Code"}, "geocode": {"description": "Geocode", "type": "string", "x-go-name": "Geocode"}, "latitude": {"description": "Latitude", "type": "string", "x-go-name": "Latitude"}, "longitude": {"description": "Longitude", "type": "string", "x-go-name": "Longitude"}, "name": {"description": "Name", "type": "string", "x-go-name": "Name"}, "page": {"description": "Page number | 第几页", "type": "integer", "format": "uint64", "minimum": 0, "x-go-name": "Page"}, "pageSize": {"description": "Page size | 单页数据行数", "type": "integer", "format": "uint64", "maximum": 100000, "x-go-name": "PageSize"}, "parentCode": {"description": "ParentCode", "type": "string", "x-go-name": "ParentCode"}, "provinceCode": {"description": "ProvinceCode", "type": "string", "x-go-name": "ProvinceCode"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "AreaListResp": {"description": "The response data of area list | Area列表数据", "type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/AreaListInfo"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ArticlSaveDraftReq": {"type": "object", "properties": {"content": {"type": "string", "x-go-name": "Content"}, "cover": {"type": "string", "x-go-name": "Cover"}, "description": {"type": "string", "x-go-name": "Description"}, "groupId": {"type": "integer", "format": "int64", "x-go-name": "GroupId"}, "id": {"type": "integer", "format": "int64", "x-go-name": "Id"}, "tagIds": {"type": "string", "x-go-name": "TagIds"}, "title": {"type": "string", "x-go-name": "Title"}, "type": {"type": "integer", "format": "int64", "x-go-name": "Type"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ArticlSaveDraftResp": {"type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/ArticleItem"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ArticleDeleteReq": {"type": "object", "required": ["articleId"], "properties": {"articleId": {"type": "integer", "format": "int64", "x-go-name": "ArticleId"}, "userId": {"type": "integer", "format": "int64", "x-go-name": "UserId"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ArticleDetailReq": {"type": "object", "required": ["articleId"], "properties": {"articleId": {"type": "integer", "format": "int64", "x-go-name": "ArticleId"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ArticleDetailResp": {"type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/ArticleItemData"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ArticleDraftListsReq": {"type": "object", "properties": {"cursor": {"type": "integer", "format": "int64", "x-go-name": "<PERSON><PERSON><PERSON>"}, "lastId": {"type": "integer", "format": "int64", "x-go-name": "LastId"}, "pageSize": {"type": "integer", "format": "int64", "x-go-name": "PageSize"}, "sortType": {"type": "integer", "format": "int32", "x-go-name": "SortType"}, "type": {"type": "integer", "format": "int32", "x-go-name": "Type"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ArticleDraftListsResp": {"type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/ArticleDraftListsRespData"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ArticleDraftListsRespData": {"type": "object", "properties": {"cursor": {"type": "integer", "format": "int64", "x-go-name": "<PERSON><PERSON><PERSON>"}, "data": {"type": "array", "items": {"$ref": "#/definitions/ArticleItem"}, "x-go-name": "Data"}, "isEnd": {"type": "boolean", "x-go-name": "IsEnd"}, "lastId": {"type": "integer", "format": "int64", "x-go-name": "LastId"}, "pageSize": {"type": "integer", "format": "int64", "x-go-name": "PageSize"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ArticleInfo": {"description": "The response data of article information | Article信息", "type": "object", "properties": {"authorId": {"description": "作者ID", "type": "integer", "format": "uint64", "x-go-name": "AuthorId"}, "authorInfo": {"$ref": "#/definitions/AuthorInfo"}, "collectNum": {"description": "收藏数", "type": "integer", "format": "int64", "x-go-name": "CollectNum"}, "commentNum": {"description": "评论数", "type": "integer", "format": "int64", "x-go-name": "CommentNum"}, "content": {"description": "内容", "type": "string", "x-go-name": "Content"}, "cover": {"description": "封面", "type": "string", "x-go-name": "Cover"}, "createTime": {"description": "创建时间", "type": "integer", "format": "int64", "x-go-name": "CreateTime"}, "description": {"description": "描述", "type": "string", "x-go-name": "Description"}, "groupId": {"description": "圈子ID", "type": "integer", "format": "int64", "x-go-name": "GroupId"}, "id": {"type": "integer", "format": "uint64", "x-go-name": "Id"}, "likeNum": {"description": "点赞数", "type": "integer", "format": "int64", "x-go-name": "LikeNum"}, "publishTime": {"description": "发布时间", "type": "integer", "format": "int64", "x-go-name": "PublishTime"}, "shareNum": {"description": "分享数", "type": "integer", "format": "int64", "x-go-name": "ShareNum"}, "status": {"description": "状态 0:待审核 1:审核不通过 2:可见 3:用户删除 4:草稿", "type": "integer", "format": "uint32", "x-go-name": "Status"}, "tagIds": {"description": "标签ID", "type": "string", "x-go-name": "TagIds"}, "title": {"description": "标题", "type": "string", "x-go-name": "Title"}, "topStatus": {"description": "置顶状态", "type": "integer", "format": "int32", "x-go-name": "TopStatus"}, "type": {"description": "类型 1、文章 2、动态", "type": "integer", "format": "int32", "x-go-name": "Type"}, "updateTime": {"description": "最后修改时间", "type": "integer", "format": "int64", "x-go-name": "UpdateTime"}, "viewNum": {"description": "浏览数", "type": "integer", "format": "int64", "x-go-name": "ViewNum"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ArticleInfoResp": {"description": "Article information response | Article信息返回体", "type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/ArticleInfo"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ArticleItem": {"type": "object", "properties": {"authorId": {"type": "integer", "format": "int64", "x-go-name": "AuthorId"}, "authorInfo": {"$ref": "#/definitions/AuthorInfo"}, "collectCount": {"type": "integer", "format": "int64", "x-go-name": "CollectCount"}, "commentCount": {"type": "integer", "format": "int64", "x-go-name": "CommentCount"}, "content": {"type": "string", "x-go-name": "Content"}, "cover": {"type": "string", "x-go-name": "Cover"}, "createTime": {"type": "integer", "format": "int64", "x-go-name": "CreateTime"}, "description": {"type": "string", "x-go-name": "Description"}, "groupId": {"type": "integer", "format": "int64", "x-go-name": "GroupId"}, "groupInfo": {"$ref": "#/definitions/GroupInfo"}, "id": {"type": "integer", "format": "int64", "x-go-name": "Id"}, "isLike": {"type": "integer", "format": "int64", "x-go-name": "IsLike"}, "likeCount": {"type": "integer", "format": "int64", "x-go-name": "LikeCount"}, "likeInfo": {"type": "array", "items": {"$ref": "#/definitions/LikeRecordItem"}, "x-go-name": "LikeInfo"}, "likeType": {"type": "integer", "format": "int64", "x-go-name": "LikeType"}, "publishTime": {"type": "integer", "format": "int64", "x-go-name": "PublishTime"}, "shareCount": {"type": "integer", "format": "int64", "x-go-name": "ShareCount"}, "status": {"type": "integer", "format": "int64", "x-go-name": "Status"}, "tagIds": {"type": "string", "x-go-name": "TagIds"}, "tagsList": {"type": "array", "items": {"$ref": "#/definitions/TagInfo"}, "x-go-name": "TagList"}, "title": {"type": "string", "x-go-name": "Title"}, "topStatus": {"type": "integer", "format": "int64", "x-go-name": "TopStatus"}, "type": {"type": "integer", "format": "int64", "x-go-name": "Type"}, "updateTime": {"type": "integer", "format": "int64", "x-go-name": "UpdateTime"}, "videos": {"type": "string", "x-go-name": "Videos"}, "viewCount": {"type": "integer", "format": "int64", "x-go-name": "ViewCount"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ArticleItemData": {"type": "object", "properties": {"data": {"$ref": "#/definitions/ArticleItem"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ArticleLikeListReq": {"type": "object", "properties": {"bizId": {"type": "string", "x-go-name": "BizId"}, "cursor": {"type": "integer", "format": "int64", "x-go-name": "<PERSON><PERSON><PERSON>"}, "objId": {"type": "integer", "format": "int64", "x-go-name": "ObjId"}, "pageSize": {"type": "integer", "format": "int64", "x-go-name": "PageSize"}, "sortType": {"type": "integer", "format": "int64", "x-go-name": "SortType"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ArticleLikeListResp": {"type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/ArticleLikeListRespData"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ArticleLikeListRespData": {"type": "object", "properties": {"cursor": {"type": "integer", "format": "int64", "x-go-name": "<PERSON><PERSON><PERSON>"}, "isEnd": {"type": "boolean", "x-go-name": "IsEnd"}, "items": {"type": "array", "items": {"$ref": "#/definitions/LikeRecordItem"}, "x-go-name": "Items"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ArticleListInfo": {"description": "Article list data | Article列表数据", "type": "object", "properties": {"data": {"description": "The API list data | Article列表数据", "type": "array", "items": {"$ref": "#/definitions/ArticleInfo"}, "x-go-name": "Data"}, "total": {"description": "The total number of data | 数据总数", "type": "integer", "format": "uint64", "x-go-name": "Total"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ArticleListReq": {"description": "Get article list request params | Article列表请求参数", "type": "object", "required": ["page", "pageSize"], "properties": {"content": {"description": "Content", "type": "string", "x-go-name": "Content"}, "cover": {"description": "Cover", "type": "string", "x-go-name": "Cover"}, "page": {"description": "Page number | 第几页", "type": "integer", "format": "uint64", "minimum": 0, "x-go-name": "Page"}, "pageSize": {"description": "Page size | 单页数据行数", "type": "integer", "format": "uint64", "maximum": 100000, "x-go-name": "PageSize"}, "title": {"description": "Title", "type": "string", "x-go-name": "Title"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ArticleListResp": {"description": "The response data of article list | Article列表数据", "type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/ArticleListInfo"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ArticleListsByGroupIdReq": {"type": "object", "properties": {"articleId": {"type": "integer", "format": "int64", "x-go-name": "ArticleId"}, "cursor": {"type": "integer", "format": "int64", "x-go-name": "<PERSON><PERSON><PERSON>"}, "groupId": {"type": "integer", "format": "int64", "x-go-name": "GroupId"}, "pageSize": {"type": "integer", "format": "int64", "x-go-name": "PageSize"}, "sortType": {"type": "integer", "format": "int32", "x-go-name": "SortType"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ArticleListsByTagIdReq": {"type": "object", "properties": {"articleId": {"type": "integer", "format": "int64", "x-go-name": "ArticleId"}, "cursor": {"type": "integer", "format": "int64", "x-go-name": "<PERSON><PERSON><PERSON>"}, "pageSize": {"type": "integer", "format": "int64", "x-go-name": "PageSize"}, "sortType": {"type": "integer", "format": "int32", "x-go-name": "SortType"}, "tagId": {"type": "integer", "format": "int64", "x-go-name": "TagId"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ArticleListsReq": {"type": "object", "properties": {"articleId": {"type": "integer", "format": "int64", "x-go-name": "ArticleId"}, "cursor": {"type": "integer", "format": "int64", "x-go-name": "<PERSON><PERSON><PERSON>"}, "pageSize": {"type": "integer", "format": "int64", "x-go-name": "PageSize"}, "sortType": {"type": "integer", "format": "int32", "x-go-name": "SortType"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ArticleListsResp": {"type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/ArticleListsRespData"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ArticleListsRespData": {"type": "object", "properties": {"articleId": {"type": "integer", "format": "int64", "x-go-name": "ArticleId"}, "articles": {"type": "array", "items": {"$ref": "#/definitions/ArticleItem"}, "x-go-name": "Articles"}, "cursor": {"type": "integer", "format": "int64", "x-go-name": "<PERSON><PERSON><PERSON>"}, "isEnd": {"type": "boolean", "x-go-name": "IsEnd"}, "topArticles": {"type": "array", "items": {"$ref": "#/definitions/ArticleItem"}, "x-go-name": "TopArticles"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "AuditedCallbackResp": {"type": "object", "properties": {"msg": {"type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "AuthTokenData": {"type": "object", "properties": {"accessExpire": {"type": "integer", "format": "int64", "x-go-name": "AccessExpire"}, "accessToken": {"type": "string", "x-go-name": "AccessToken"}, "refreshAfter": {"type": "integer", "format": "int64", "x-go-name": "RefreshAfter"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "AuthorInfo": {"type": "object", "properties": {"avatar": {"type": "string", "x-go-name": "Avatar"}, "backgroundImage": {"type": "string", "x-go-name": "BackgroundImage"}, "commentCount": {"type": "integer", "format": "int64", "x-go-name": "CommentCount"}, "delState": {"type": "integer", "format": "int64", "x-go-name": "DelState"}, "description": {"type": "string", "x-go-name": "Description"}, "fansCount": {"type": "integer", "format": "int64", "x-go-name": "FansCount"}, "followCount": {"type": "integer", "format": "int64", "x-go-name": "FollowCount"}, "forbiddenEndTime": {"type": "integer", "format": "int64", "x-go-name": "ForbiddenEndTime"}, "homePage": {"type": "string", "x-go-name": "HomePage"}, "id": {"type": "integer", "format": "int64", "x-go-name": "Id"}, "info": {"type": "string", "x-go-name": "Info"}, "isFollow": {"type": "boolean", "x-go-name": "IsFollow"}, "mobile": {"type": "string", "x-go-name": "Mobile"}, "mutedEndTime": {"type": "integer", "format": "int64", "x-go-name": "MutedEndTime"}, "nickName": {"type": "string", "x-go-name": "<PERSON><PERSON><PERSON>"}, "nickname": {"type": "string", "x-go-name": "Nickname"}, "password": {"type": "string", "x-go-name": "Password"}, "roles": {"type": "string", "x-go-name": "Roles"}, "score": {"type": "integer", "format": "int64", "x-go-name": "Score"}, "sex": {"type": "integer", "format": "int64", "x-go-name": "Sex"}, "topicCount": {"type": "integer", "format": "int64", "x-go-name": "TopicCount"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "BannerInfo": {"description": "The response data of banner information | Banner信息", "type": "object", "properties": {"baseColor": {"description": "底色类型", "type": "integer", "format": "int64", "x-go-name": "BaseColor"}, "coverUrl": {"description": "轮播图地址", "type": "string", "x-go-name": "CoverUrl"}, "createTime": {"description": "创建时间", "type": "integer", "format": "int64", "x-go-name": "CreateTime"}, "duration": {"description": "轮播图持续时间", "type": "integer", "format": "int64", "x-go-name": "Duration"}, "id": {"type": "integer", "format": "uint64", "x-go-name": "Id"}, "interval": {"description": "轮播图间隔时间", "type": "integer", "format": "int64", "x-go-name": "Interval"}, "jumpLink": {"description": "跳转链接", "type": "string", "x-go-name": "JumpLink"}, "module": {"description": "所属模块（1、社区模块 2、服务模块）", "type": "integer", "format": "int32", "x-go-name": "<PERSON><PERSON><PERSON>"}, "sort": {"description": "排序", "type": "integer", "format": "int32", "x-go-name": "Sort"}, "subTitle": {"description": "副标题", "type": "string", "x-go-name": "SubTitle"}, "title": {"description": "标题", "type": "string", "x-go-name": "Title"}, "type": {"description": "类型（1、文章 2、商品 3、外链）", "type": "integer", "format": "int32", "x-go-name": "Type"}, "updateTime": {"description": "更新时间", "type": "integer", "format": "int64", "x-go-name": "UpdateTime"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "BannerInfoResp": {"description": "Banner information response | Banner信息返回体", "type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/BannerInfo"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "BannerListInfo": {"description": "Banner list data | Banner列表数据", "type": "object", "properties": {"data": {"description": "The API list data | Banner列表数据", "type": "array", "items": {"$ref": "#/definitions/BannerInfo"}, "x-go-name": "Data"}, "total": {"description": "The total number of data | 数据总数", "type": "integer", "format": "uint64", "x-go-name": "Total"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "BannerListReq": {"description": "Get banner list request params | Banner列表请求参数", "type": "object", "required": ["page", "pageSize"], "properties": {"coverUrl": {"description": "CoverUrl", "type": "string", "x-go-name": "CoverUrl"}, "module": {"description": "所属模块（1、社区模块 2、服务模块）", "type": "integer", "format": "int32", "x-go-name": "<PERSON><PERSON><PERSON>"}, "page": {"description": "Page number | 第几页", "type": "integer", "format": "uint64", "minimum": 0, "x-go-name": "Page"}, "pageSize": {"description": "Page size | 单页数据行数", "type": "integer", "format": "uint64", "maximum": 100000, "x-go-name": "PageSize"}, "subTitle": {"description": "SubTitle", "type": "string", "x-go-name": "SubTitle"}, "title": {"description": "Title", "type": "string", "x-go-name": "Title"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "BannerListResp": {"description": "The response data of banner list | Banner列表数据", "type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/BannerListInfo"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "BaseDataInfo": {"description": "The basic response with data | 基础带数据信息", "type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "BaseIDInfo": {"description": "The base ID response data | 基础ID信息", "type": "object", "properties": {"createdAt": {"description": "Create date | 创建日期", "type": "integer", "format": "int64", "x-go-name": "CreatedAt"}, "id": {"description": "ID", "type": "integer", "format": "uint64", "x-go-name": "Id"}, "updatedAt": {"description": "Update date | 更新日期", "type": "integer", "format": "int64", "x-go-name": "UpdatedAt"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "BaseIDInt32Info": {"description": "The base ID response data (int32) | 基础ID信息 (int32)", "type": "object", "properties": {"createdAt": {"description": "Create date | 创建日期", "type": "integer", "format": "int64", "x-go-name": "CreatedAt"}, "id": {"description": "ID", "type": "integer", "format": "int32", "x-go-name": "Id"}, "updatedAt": {"description": "Update date | 更新日期", "type": "integer", "format": "int64", "x-go-name": "UpdatedAt"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "BaseIDInt64Info": {"description": "The base ID response data (int64) | 基础ID信息 (int64)", "type": "object", "properties": {"createdAt": {"description": "Create date | 创建日期", "type": "integer", "format": "int64", "x-go-name": "CreatedAt"}, "id": {"description": "ID", "type": "integer", "format": "int64", "x-go-name": "Id"}, "updatedAt": {"description": "Update date | 更新日期", "type": "integer", "format": "int64", "x-go-name": "UpdatedAt"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "BaseIDUint32Info": {"description": "The base ID response data (uint32) | 基础ID信息 (uint32)", "type": "object", "properties": {"createdAt": {"description": "Create date | 创建日期", "type": "integer", "format": "int64", "x-go-name": "CreatedAt"}, "id": {"description": "ID", "type": "integer", "format": "uint32", "x-go-name": "Id"}, "updatedAt": {"description": "Update date | 更新日期", "type": "integer", "format": "int64", "x-go-name": "UpdatedAt"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "BaseListInfo": {"description": "The basic response with data | 基础带数据信息", "type": "object", "properties": {"data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data"}, "total": {"description": "The total number of data | 数据总数", "type": "integer", "format": "uint64", "x-go-name": "Total"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "BaseMsgResp": {"description": "The basic response without data | 基础不带数据信息", "type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "BaseUUIDInfo": {"description": "The base UUID response data | 基础UUID信息", "type": "object", "properties": {"createdAt": {"description": "Create date | 创建日期", "type": "integer", "format": "int64", "x-go-name": "CreatedAt"}, "id": {"description": "ID", "type": "string", "x-go-name": "Id"}, "updatedAt": {"description": "Update date | 更新日期", "type": "integer", "format": "int64", "x-go-name": "UpdatedAt"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "BindDeviceReq": {"description": "Bind Device | 绑定设备", "type": "object", "properties": {"appCode": {"type": "string", "x-go-name": "AppCode"}, "deviceId": {"type": "string", "x-go-name": "DeviceId"}, "deviceType": {"type": "string", "x-go-name": "DeviceType"}, "machineCode": {"type": "string", "x-go-name": "MachineCode"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "BindWxByMobileReq": {"type": "object", "properties": {"authKey": {"type": "string", "x-go-name": "<PERSON><PERSON><PERSON><PERSON>"}, "authType": {"type": "string", "x-go-name": "AuthType"}, "mobile": {"type": "string", "x-go-name": "Mobile"}, "verifyCode": {"type": "string", "x-go-name": "VerifyCode"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "BoloLexiconInfo": {"description": "The response data of bolo lexicon information | BoloLexicon信息", "type": "object", "properties": {"createdAt": {"description": "Create date | 创建日期", "type": "integer", "format": "int64", "x-go-name": "CreatedAt"}, "creatorId": {"description": "创建人ID", "type": "integer", "format": "uint64", "x-go-name": "CreatorId"}, "definition": {"description": "词汇释义", "type": "string", "x-go-name": "Definition"}, "example": {"description": "示例用法", "type": "string", "x-go-name": "Example"}, "id": {"description": "ID", "type": "integer", "format": "uint64", "x-go-name": "Id"}, "pinyin": {"description": "拼音标注", "type": "string", "x-go-name": "<PERSON><PERSON><PERSON>"}, "status": {"description": "状态 1=启用 0=禁用", "type": "integer", "format": "uint32", "x-go-name": "Status"}, "tags": {"description": "标签（逗号分隔）", "type": "string", "x-go-name": "Tags"}, "updatedAt": {"description": "Update date | 更新日期", "type": "integer", "format": "int64", "x-go-name": "UpdatedAt"}, "weight": {"description": "权重（用于排序）", "type": "integer", "format": "int32", "x-go-name": "Weight"}, "word": {"description": "词汇内容", "type": "string", "x-go-name": "Word"}, "wordType": {"description": "词汇类型", "type": "string", "x-go-name": "WordType"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "BoloLexiconInfoResp": {"description": "The bolo lexicon information response | BoloLexicon信息返回体", "type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/BoloLexiconInfo"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "BoloLexiconListInfo": {"description": "The bolo lexicon list data | BoloLexicon信息列表数据", "type": "object", "properties": {"data": {"description": "The bolo lexicon list data | BoloLexicon信息列表数据", "type": "array", "items": {"$ref": "#/definitions/BoloLexiconInfo"}, "x-go-name": "Data"}, "total": {"description": "The total number of data | 数据总数", "type": "integer", "format": "uint64", "x-go-name": "Total"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "BoloLexiconListReq": {"description": "Get bolo lexicon list request params | BoloLexicon列表请求参数", "type": "object", "required": ["page", "pageSize"], "properties": {"createdAt": {"description": "CreatedAt", "type": "integer", "format": "int64", "x-go-name": "CreatedAt"}, "creatorId": {"description": "CreatorId", "type": "integer", "format": "uint64", "x-go-name": "CreatorId"}, "definition": {"description": "Definition", "type": "string", "x-go-name": "Definition"}, "deletedAt": {"description": "DeletedAt", "type": "integer", "format": "int64", "x-go-name": "DeletedAt"}, "example": {"description": "Example", "type": "string", "x-go-name": "Example"}, "page": {"description": "Page number | 第几页", "type": "integer", "format": "uint64", "minimum": 0, "x-go-name": "Page"}, "pageSize": {"description": "Page size | 单页数据行数", "type": "integer", "format": "uint64", "maximum": 100000, "x-go-name": "PageSize"}, "pinyin": {"description": "<PERSON><PERSON><PERSON>", "type": "string", "x-go-name": "<PERSON><PERSON><PERSON>"}, "status": {"description": "Status", "type": "integer", "format": "uint32", "x-go-name": "Status"}, "tags": {"description": "Tags", "type": "string", "x-go-name": "Tags"}, "updatedAt": {"description": "UpdatedAt", "type": "integer", "format": "int64", "x-go-name": "UpdatedAt"}, "weight": {"description": "Weight", "type": "integer", "format": "int32", "x-go-name": "Weight"}, "word": {"description": "Word", "type": "string", "x-go-name": "Word"}, "wordType": {"description": "WordType", "type": "string", "x-go-name": "WordType"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "BoloLexiconListResp": {"description": "The response data of bolo lexicon list | BoloLexicon信息列表数据", "type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/BoloLexiconListInfo"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "CityInfo": {"description": "The response data of city information | City信息", "type": "object", "properties": {"code": {"description": "Code", "type": "string", "x-go-name": "Code"}, "geocode": {"description": "Geocode", "type": "string", "x-go-name": "Geocode"}, "id": {"type": "integer", "format": "uint64", "x-go-name": "Id"}, "latitude": {"description": "Latitude", "type": "string", "x-go-name": "Latitude"}, "level": {"description": "Level", "type": "integer", "format": "int32", "x-go-name": "Level"}, "longitude": {"description": "Longitude", "type": "string", "x-go-name": "Longitude"}, "name": {"description": "Name", "type": "string", "x-go-name": "Name"}, "parentCode": {"description": "ParentCode", "type": "string", "x-go-name": "ParentCode"}, "provinceCode": {"description": "ProvinceCode", "type": "string", "x-go-name": "ProvinceCode"}, "sort": {"description": "Sort", "type": "integer", "format": "int32", "x-go-name": "Sort"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "CityInfoResp": {"description": "City information response | City信息返回体", "type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/CityInfo"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "CityListInfo": {"description": "City list data | City列表数据", "type": "object", "properties": {"data": {"description": "The API list data | City列表数据", "type": "array", "items": {"$ref": "#/definitions/CityInfo"}, "x-go-name": "Data"}, "total": {"description": "The total number of data | 数据总数", "type": "integer", "format": "uint64", "x-go-name": "Total"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "CityListReq": {"description": "Get city list request params | City列表请求参数", "type": "object", "required": ["page", "pageSize"], "properties": {"code": {"description": "Code", "type": "string", "x-go-name": "Code"}, "geocode": {"description": "Geocode", "type": "string", "x-go-name": "Geocode"}, "latitude": {"description": "Latitude", "type": "string", "x-go-name": "Latitude"}, "longitude": {"description": "Longitude", "type": "string", "x-go-name": "Longitude"}, "name": {"description": "Name", "type": "string", "x-go-name": "Name"}, "page": {"description": "Page number | 第几页", "type": "integer", "format": "uint64", "minimum": 0, "x-go-name": "Page"}, "pageSize": {"description": "Page size | 单页数据行数", "type": "integer", "format": "uint64", "maximum": 100000, "x-go-name": "PageSize"}, "parentCode": {"description": "ParentCode", "type": "string", "x-go-name": "ParentCode"}, "provinceCode": {"description": "ProvinceCode", "type": "string", "x-go-name": "ProvinceCode"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "CityListResp": {"description": "The response data of city list | City列表数据", "type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/CityListInfo"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "CommonResp": {"type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/CommonRespData"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "CommonRespData": {"type": "object", "properties": {"message": {"type": "string", "x-go-name": "Message"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ConfigurationInfo": {"description": "The response data of configuration information | 参数配置信息", "type": "object", "properties": {"category": {"description": "Configuration category | 配置的分类", "type": "string", "x-go-name": "Category"}, "createdAt": {"description": "Create date | 创建日期", "type": "integer", "format": "int64", "x-go-name": "CreatedAt"}, "id": {"description": "ID", "type": "integer", "format": "uint64", "x-go-name": "Id"}, "key": {"description": "Configuration key | 配置的键名", "type": "string", "x-go-name": "Key"}, "name": {"description": "Configurarion name | 配置名称", "type": "string", "x-go-name": "Name"}, "remark": {"description": "Remark | 备注", "type": "string", "x-go-name": "Remark"}, "sort": {"description": "Sort Number | 排序编号", "type": "integer", "format": "uint32", "x-go-name": "Sort"}, "state": {"description": "State true: normal false: ban | 状态 true 正常 false 禁用", "type": "boolean", "x-go-name": "State"}, "updatedAt": {"description": "Update date | 更新日期", "type": "integer", "format": "int64", "x-go-name": "UpdatedAt"}, "value": {"description": "Configuraion value | 配置的值", "type": "string", "x-go-name": "Value"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ConfigurationInfoResp": {"description": "Configuration information response | 参数配置信息返回体", "type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/ConfigurationInfo"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ConfigurationListInfo": {"description": "Configuration list data | 参数配置列表数据", "type": "object", "properties": {"data": {"description": "The API list data | Configuration列表数据", "type": "array", "items": {"$ref": "#/definitions/ConfigurationInfo"}, "x-go-name": "Data"}, "total": {"description": "The total number of data | 数据总数", "type": "integer", "format": "uint64", "x-go-name": "Total"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ConfigurationListReq": {"description": "Get configuration list request params | 参数配置列表请求参数", "type": "object", "required": ["page", "pageSize"], "properties": {"category": {"description": "Category", "type": "string", "x-go-name": "Category"}, "key": {"description": "Key", "type": "string", "x-go-name": "Key"}, "name": {"description": "Name", "type": "string", "x-go-name": "Name"}, "page": {"description": "Page number | 第几页", "type": "integer", "format": "uint64", "minimum": 0, "x-go-name": "Page"}, "pageSize": {"description": "Page size | 单页数据行数", "type": "integer", "format": "uint64", "maximum": 100000, "x-go-name": "PageSize"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ConfigurationListResp": {"description": "The response data of configuration list | 参数配置列表数据", "type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/ConfigurationListInfo"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ConfigurationResp": {"description": "Get public system configuration response | 获取公开系统参数返回体", "type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/ConfigurationInfo"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ConfigurationtReq": {"description": "Get public system configuration request params | 获取公开系统参数请求参数", "type": "object", "properties": {"name": {"description": "Name", "type": "string", "x-go-name": "Name"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "CreateAnonymousMallOrderReq": {"description": "匿名下单请求", "type": "object", "required": ["items", "currency", "orderType"], "properties": {"currency": {"description": "货币类型", "type": "string", "x-go-name": "<PERSON><PERSON><PERSON><PERSON>"}, "deviceId": {"description": "设备ID", "type": "string", "x-go-name": "DeviceId"}, "items": {"description": "商品列表", "type": "array", "minLength": 1, "items": {"$ref": "#/definitions/CreateOrderItem"}, "x-go-name": "Items"}, "machineCode": {"description": "机器码", "type": "string", "x-go-name": "MachineCode"}, "orderType": {"description": "订单类型 2虚拟订单", "type": "integer", "format": "int32", "x-go-name": "OrderType"}, "receiver": {"description": "Receiver | 接收人", "type": "string", "x-go-name": "Receiver"}, "remark": {"description": "订单备注", "type": "string", "x-go-name": "Remark"}, "userAddressId": {"description": "收货人信息", "type": "integer", "format": "uint64", "x-go-name": "UserAddressId"}, "verificationCode": {"description": "Verfication Code | 验证码", "type": "string", "x-go-name": "VerificationCode"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "CreateMallOrderReq": {"description": "创建订单", "type": "object", "required": ["items", "currency", "orderType"], "properties": {"currency": {"description": "货币类型", "type": "string", "x-go-name": "<PERSON><PERSON><PERSON><PERSON>"}, "deviceId": {"description": "设备ID", "type": "string", "x-go-name": "DeviceId"}, "items": {"description": "商品列表", "type": "array", "minLength": 1, "items": {"$ref": "#/definitions/CreateOrderItem"}, "x-go-name": "Items"}, "machineCode": {"description": "机器码", "type": "string", "x-go-name": "MachineCode"}, "orderType": {"description": "订单类型 1实物订单,2虚拟订单,3混合订单", "type": "integer", "format": "int32", "x-go-name": "OrderType"}, "remark": {"description": "订单备注", "type": "string", "x-go-name": "Remark"}, "userAddressId": {"description": "收货人信息", "type": "integer", "format": "uint64", "x-go-name": "UserAddressId"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "CreateMallOrderResp": {"description": "创建订单响应", "type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/CreateOrderRespData"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "CreateOrderItem": {"description": "创建订单商品项", "type": "object", "required": ["productId", "skuId", "quantity"], "properties": {"productId": {"description": "商品ID", "type": "integer", "format": "uint64", "x-go-name": "ProductId"}, "quantity": {"description": "购买数量", "type": "integer", "format": "int64", "minimum": 0, "x-go-name": "Quantity"}, "skuId": {"description": "SKU ID", "type": "integer", "format": "uint64", "x-go-name": "SkuId"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "CreateOrderRespData": {"description": "创建订单响应数据", "type": "object", "properties": {"currency": {"description": "币种", "type": "string", "x-go-name": "<PERSON><PERSON><PERSON><PERSON>"}, "orderNo": {"description": "订单编号", "type": "string", "x-go-name": "OrderNo"}, "payAmount": {"description": "支付金额(分)", "type": "integer", "format": "int64", "x-go-name": "PayAmount"}, "totalAmount": {"description": "订单金额(分)", "type": "integer", "format": "int64", "x-go-name": "TotalAmount"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "CreateUserMobileSsoLoginSessionReq": {"description": "创建用户移动端sso登录session", "type": "object", "properties": {"deviceId": {"type": "string", "x-go-name": "DeviceId"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "CreateUserMobileSsoLoginSessionResp": {"type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/CreateUserMobileSsoLoginSessionRespData"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "CreateUserMobileSsoLoginSessionRespData": {"type": "object", "properties": {"sessionId": {"type": "string", "x-go-name": "SessionId"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "DelReplyReq": {"type": "object", "properties": {"replyId": {"type": "integer", "format": "int64", "x-go-name": "ReplyId"}, "userId": {"type": "integer", "format": "int64", "x-go-name": "UserId"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "DelReplyResp": {"type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/DelReplyRespData"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "DelReplyRespData": {"type": "object", "properties": {"replyId": {"type": "integer", "format": "int64", "x-go-name": "ReplyId"}, "status": {"type": "integer", "format": "int64", "x-go-name": "Status"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "DeleteUserMobileSsoLoginSessionReq": {"description": "删除用户移动端sso登录session", "type": "object", "properties": {"appId": {"type": "string", "x-go-name": "AppId"}, "appPackageName": {"type": "string", "x-go-name": "AppPackageName"}, "deleteAll": {"type": "integer", "format": "int64", "x-go-name": "DeleteAll"}, "deviceId": {"type": "string", "x-go-name": "DeviceId"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "DeleteUserMobileSsoLoginSessionResp": {"type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "EmptyReq": {"description": "Empty request | 无参数请求", "type": "object", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "FansItem": {"type": "object", "properties": {"createTime": {"type": "integer", "format": "int64", "x-go-name": "CreateTime"}, "fansCount": {"type": "integer", "format": "int64", "x-go-name": "FansCount"}, "fansUserId": {"type": "integer", "format": "int64", "x-go-name": "FansUserId"}, "fansUserInfo": {"$ref": "#/definitions/FansUserInfo"}, "id": {"type": "integer", "format": "int64", "x-go-name": "Id"}, "userId": {"type": "integer", "format": "int64", "x-go-name": "UserId"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "FansListReq": {"type": "object", "properties": {"cursor": {"type": "integer", "format": "int64", "x-go-name": "<PERSON><PERSON><PERSON>"}, "id": {"type": "integer", "format": "int64", "x-go-name": "Id"}, "pageSize": {"type": "integer", "format": "int64", "x-go-name": "PageSize"}, "userId": {"type": "integer", "format": "int64", "x-go-name": "UserId"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "FansListResp": {"type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/FansListRespData"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "FansListRespData": {"type": "object", "properties": {"cursor": {"type": "integer", "format": "int64", "x-go-name": "<PERSON><PERSON><PERSON>"}, "fansItem": {"type": "array", "items": {"$ref": "#/definitions/FansItem"}, "x-go-name": "Items"}, "isEnd": {"type": "boolean", "x-go-name": "IsEnd"}, "userId": {"type": "integer", "format": "int64", "x-go-name": "Id"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "FansUserInfo": {"type": "object", "properties": {"avatar": {"type": "string", "x-go-name": "Avatar"}, "id": {"type": "integer", "format": "int64", "x-go-name": "Id"}, "isFans": {"type": "boolean", "x-go-name": "IsFans"}, "isFollow": {"type": "boolean", "x-go-name": "IsFollow"}, "nickName": {"type": "string", "x-go-name": "<PERSON><PERSON><PERSON>"}, "userId": {"type": "integer", "format": "int64", "x-go-name": "UserId"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "FileUrl": {"type": "object", "properties": {"url": {"type": "string", "x-go-name": "Url"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "FollowItem": {"type": "object", "properties": {"createTime": {"type": "integer", "format": "int64", "x-go-name": "CreateTime"}, "fansCount": {"type": "integer", "format": "int64", "x-go-name": "FansCount"}, "followUserInfo": {"$ref": "#/definitions/FollowUserInfo"}, "followedUserId": {"type": "integer", "format": "int64", "x-go-name": "FollowedUserId"}, "id": {"type": "integer", "format": "int64", "x-go-name": "Id"}, "userId": {"type": "integer", "format": "int64", "x-go-name": "UserId"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "FollowListReq": {"type": "object", "properties": {"cursor": {"type": "integer", "format": "int64", "x-go-name": "<PERSON><PERSON><PERSON>"}, "id": {"type": "integer", "format": "int64", "x-go-name": "Id"}, "pageSize": {"type": "integer", "format": "int64", "x-go-name": "PageSize"}, "userId": {"type": "integer", "format": "int64", "x-go-name": "UserId"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "FollowListResp": {"type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/FollowListRespData"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "FollowListRespData": {"type": "object", "properties": {"cursor": {"type": "integer", "format": "int64", "x-go-name": "<PERSON><PERSON><PERSON>"}, "followItem": {"type": "array", "items": {"$ref": "#/definitions/FollowItem"}, "x-go-name": "Items"}, "isEnd": {"type": "boolean", "x-go-name": "IsEnd"}, "userId": {"type": "integer", "format": "int64", "x-go-name": "Id"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "FollowReq": {"type": "object", "properties": {"followedUserId": {"type": "integer", "format": "int64", "x-go-name": "FollowedUserId"}, "userId": {"type": "integer", "format": "int64", "x-go-name": "UserId"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "FollowResp": {"description": "添加 BaseDataInfo", "type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/FollowRespData"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "FollowRespData": {"type": "object", "properties": {"fansCount": {"type": "integer", "format": "int64", "x-go-name": "FansCount"}, "followCount": {"type": "integer", "format": "int64", "x-go-name": "FollowCount"}, "userId": {"type": "integer", "format": "int64", "x-go-name": "UserId"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "FollowUserInfo": {"type": "object", "properties": {"avatar": {"type": "string", "x-go-name": "Avatar"}, "id": {"type": "integer", "format": "int64", "x-go-name": "Id"}, "isFans": {"type": "boolean", "x-go-name": "IsFans"}, "isFollow": {"type": "boolean", "x-go-name": "IsFollow"}, "nickName": {"type": "string", "x-go-name": "<PERSON><PERSON><PERSON>"}, "userId": {"type": "integer", "format": "int64", "x-go-name": "UserId"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "FromUserInfo": {"type": "object", "properties": {"avatar": {"type": "string", "x-go-name": "Avatar"}, "backgroundImage": {"type": "string", "x-go-name": "BackgroundImage"}, "description": {"type": "string", "x-go-name": "Description"}, "homePage": {"type": "string", "x-go-name": "HomePage"}, "id": {"type": "integer", "format": "int64", "x-go-name": "Id"}, "info": {"type": "string", "x-go-name": "Info"}, "mobile": {"type": "string", "x-go-name": "Mobile"}, "nickname": {"type": "string", "x-go-name": "Nickname"}, "sex": {"type": "integer", "format": "int64", "x-go-name": "Sex"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "GeetestVerifyReq": {"type": "object", "properties": {"captchaOutput": {"type": "string", "x-go-name": "CaptchaOutput"}, "genTime": {"type": "string", "x-go-name": "GenTime"}, "lotNumber": {"type": "string", "x-go-name": "LotNumber"}, "passToken": {"type": "string", "x-go-name": "PassToken"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "GeetestVerifyResp": {"type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/GeetestVerifyRespData"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "GeetestVerifyRespData": {"type": "object", "properties": {"success": {"type": "boolean", "x-go-name": "Success"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "GetMachineCodeReq": {"description": "Get Machine Code | 获取机器码", "type": "object", "properties": {"appCode": {"type": "string", "x-go-name": "AppCode"}, "deviceId": {"type": "string", "x-go-name": "DeviceId"}, "deviceType": {"type": "string", "x-go-name": "DeviceType"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "GetMachineCodeResp": {"description": "Get Machine Code Response | 获取机器码响应", "type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/GetMachineCodeRespData"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "GetMachineCodeRespData": {"description": "Get Machine Code Response Data | 获取机器码响应数据", "type": "object", "properties": {"appCode": {"type": "string", "x-go-name": "AppCode"}, "deviceId": {"type": "string", "x-go-name": "DeviceId"}, "deviceType": {"type": "string", "x-go-name": "DeviceType"}, "machineCode": {"description": "机器码", "type": "string", "x-go-name": "MachineCode"}, "publicKey": {"description": "公钥", "type": "string", "x-go-name": "PublicKey"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "GetMallProductByDeviceIdAndMachineCodeReq": {"description": "Get Mall Product By DeviceId and MachineCode | 通过设备ID和机器码获取商品SKU信息", "type": "object", "required": ["id"], "properties": {"deviceId": {"description": "设备ID", "type": "string", "x-go-name": "DeviceId"}, "id": {"description": "ID", "type": "integer", "format": "uint64", "x-go-name": "Id"}, "machineCode": {"description": "机器码", "type": "string", "x-go-name": "MachineCode"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "GetMessageListReq": {"type": "object", "properties": {"cursor": {"type": "integer", "format": "int64", "x-go-name": "<PERSON><PERSON><PERSON>"}, "msgType": {"type": "integer", "format": "int64", "x-go-name": "MsgType"}, "pageSize": {"type": "integer", "format": "int64", "x-go-name": "PageSize"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "GetMessageListResp": {"type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/GetMessageListRespData"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "GetMessageListRespData": {"type": "object", "properties": {"cursor": {"type": "integer", "format": "int64", "x-go-name": "<PERSON><PERSON><PERSON>"}, "isEnd": {"type": "boolean", "x-go-name": "IsEnd"}, "items": {"type": "array", "items": {"$ref": "#/definitions/MessageItem"}, "x-go-name": "Items"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "GetSendSmsCodeReq": {"type": "object", "properties": {"mobile": {"type": "string", "x-go-name": "Mobile"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "GetSendSmsCodeResp": {"type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/GetSendSmsCodeRespData"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "GetSendSmsCodeRespData": {"type": "object", "properties": {"code": {"type": "string", "x-go-name": "Code"}, "mobile": {"type": "string", "x-go-name": "Mobile"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "GetUserAgreementByKeyReq": {"type": "object", "properties": {"key": {"type": "string", "x-go-name": "Key"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "GetUserMobileSsoTicketReq": {"description": "获取用户移动端sso登录ticket", "type": "object", "properties": {"appId": {"type": "string", "x-go-name": "AppId"}, "appPackageName": {"type": "string", "x-go-name": "AppPackageName"}, "deviceId": {"type": "string", "x-go-name": "DeviceId"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "GetUserMobileSsoTicketResp": {"type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/GetUserMobileSsoTicketRespData"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "GetUserMobileSsoTicketRespData": {"type": "object", "properties": {"avatar": {"type": "string", "x-go-name": "Avatar"}, "nickname": {"type": "string", "x-go-name": "Nickname"}, "ticket": {"type": "string", "x-go-name": "Ticket"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "GetUserMobileSsoUserLoginInfoReq": {"description": "获取用户移动端sso登录用户信息", "type": "object", "properties": {"appId": {"type": "string", "x-go-name": "AppId"}, "appPackageName": {"type": "string", "x-go-name": "AppPackageName"}, "deviceId": {"type": "string", "x-go-name": "DeviceId"}, "ticket": {"type": "string", "x-go-name": "Ticket"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "GetUserMobileSsoUserLoginInfoResp": {"type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/AuthTokenData"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "GetUserProductListReq": {"type": "object", "properties": {"cursor": {"type": "integer", "format": "int64", "x-go-name": "<PERSON><PERSON><PERSON>"}, "lastId": {"type": "integer", "format": "int64", "x-go-name": "LastId"}, "pageSize": {"type": "integer", "format": "int64", "x-go-name": "PageSize"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "GetUserProductListResp": {"type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/GetUserProductListRespData"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "GetUserProductListRespData": {"type": "object", "properties": {"cursor": {"type": "integer", "format": "int64", "x-go-name": "<PERSON><PERSON><PERSON>"}, "isEnd": {"type": "boolean", "x-go-name": "IsEnd"}, "items": {"type": "array", "items": {"$ref": "#/definitions/ProductItem"}, "x-go-name": "Items"}, "lastId": {"type": "integer", "format": "int64", "x-go-name": "LastId"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "GetUserSigReq": {"type": "object", "properties": {"userId": {"type": "string", "x-go-name": "UserId"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "GetUserSigResp": {"type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/GetUserSigRespData"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "GetUserSigRespData": {"type": "object", "properties": {"userSig": {"type": "string", "x-go-name": "UserSig"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "GroupFollowInfo": {"description": "The response data of group follow information | GroupFollow信息", "type": "object", "properties": {"createTime": {"description": "创建时间", "type": "integer", "format": "int64", "x-go-name": "CreateTime"}, "groupId": {"description": "圈子ID", "type": "integer", "format": "uint64", "x-go-name": "GroupId"}, "id": {"type": "integer", "format": "uint64", "x-go-name": "Id"}, "status": {"description": "状态（0、退出 1、加入）", "type": "integer", "format": "uint32", "x-go-name": "Status"}, "updateTime": {"description": "最后修改时间", "type": "integer", "format": "int64", "x-go-name": "UpdateTime"}, "userId": {"description": "用户ID", "type": "integer", "format": "uint64", "x-go-name": "UserId"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "GroupFollowInfoResp": {"description": "GroupFollow information response | GroupFollow信息返回体", "type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/GroupFollowInfo"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "GroupFollowListInfo": {"description": "GroupFollow list data | GroupFollow列表数据", "type": "object", "properties": {"data": {"description": "The API list data | GroupFollow列表数据", "type": "array", "items": {"$ref": "#/definitions/GroupFollowInfo"}, "x-go-name": "Data"}, "total": {"description": "The total number of data | 数据总数", "type": "integer", "format": "uint64", "x-go-name": "Total"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "GroupFollowListReq": {"description": "Get group follow list request params | GroupFollow列表请求参数", "type": "object", "required": ["page", "pageSize"], "properties": {"page": {"description": "Page number | 第几页", "type": "integer", "format": "uint64", "minimum": 0, "x-go-name": "Page"}, "pageSize": {"description": "Page size | 单页数据行数", "type": "integer", "format": "uint64", "maximum": 100000, "x-go-name": "PageSize"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "GroupFollowListResp": {"description": "The response data of group follow list | GroupFollow列表数据", "type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/GroupFollowListInfo"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "GroupInfo": {"description": "The response data of group information | Group信息", "type": "object", "properties": {"coverUrl": {"description": "圈子背景图", "type": "string", "x-go-name": "CoverUrl"}, "createTime": {"description": "创建时间", "type": "integer", "format": "int64", "x-go-name": "CreateTime"}, "desc": {"description": "圈子描述", "type": "string", "x-go-name": "Desc"}, "followNum": {"description": "关注数", "type": "integer", "format": "int64", "x-go-name": "FollowNum"}, "iconUrl": {"description": "圈子图标", "type": "string", "x-go-name": "IconUrl"}, "id": {"type": "integer", "format": "uint64", "x-go-name": "Id"}, "isFollow": {"type": "boolean", "x-go-name": "IsFollow"}, "isMember": {"type": "boolean", "x-go-name": "IsMember"}, "memberNum": {"type": "integer", "format": "int64", "x-go-name": "MemberNum"}, "name": {"description": "圈子名称", "type": "string", "x-go-name": "Name"}, "tags": {"description": "tags", "type": "array", "items": {"$ref": "#/definitions/TagInfo"}, "x-go-name": "Tags"}, "topicNum": {"description": "主题数", "type": "integer", "format": "int64", "x-go-name": "TopicNum"}, "updateTime": {"description": "最后修改时间", "type": "integer", "format": "int64", "x-go-name": "UpdateTime"}, "userId": {"description": "用户ID", "type": "integer", "format": "uint64", "x-go-name": "UserId"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "GroupInfoResp": {"description": "Group information response | Group信息返回体", "type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/GroupInfo"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "GroupListInfo": {"description": "Group list data | Group列表数据", "type": "object", "properties": {"data": {"description": "The API list data | Group列表数据", "type": "array", "items": {"$ref": "#/definitions/GroupInfo"}, "x-go-name": "Data"}, "total": {"description": "The total number of data | 数据总数", "type": "integer", "format": "uint64", "x-go-name": "Total"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "GroupListReq": {"description": "Get group list request params | Group列表请求参数", "type": "object", "required": ["page", "pageSize"], "properties": {"name": {"description": "Name", "type": "string", "x-go-name": "Name"}, "page": {"description": "Page number | 第几页", "type": "integer", "format": "uint64", "minimum": 0, "x-go-name": "Page"}, "pageSize": {"description": "Page size | 单页数据行数", "type": "integer", "format": "uint64", "maximum": 100000, "x-go-name": "PageSize"}, "userId": {"description": "用户ID", "type": "integer", "format": "uint64", "x-go-name": "UserId"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "GroupListResp": {"description": "The response data of group list | Group列表数据", "type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/GroupListInfo"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "GroupMemberInfo": {"description": "The response data of group member information | GroupMember信息", "type": "object", "properties": {"authorInfo": {"$ref": "#/definitions/AuthorInfo"}, "createTime": {"description": "创建时间", "type": "integer", "format": "int64", "x-go-name": "CreateTime"}, "groupId": {"description": "圈子ID", "type": "integer", "format": "int64", "x-go-name": "GroupId"}, "id": {"type": "integer", "format": "uint64", "x-go-name": "Id"}, "status": {"description": "状态（0、退出 1、加入）", "type": "integer", "format": "uint32", "x-go-name": "Status"}, "updateTime": {"description": "最后修改时间", "type": "integer", "format": "int64", "x-go-name": "UpdateTime"}, "userId": {"description": "用户ID", "type": "integer", "format": "int64", "x-go-name": "UserId"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "GroupMemberInfoResp": {"description": "GroupMember information response | GroupMember信息返回体", "type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/GroupMemberInfo"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "GroupMemberListInfo": {"description": "GroupMember list data | GroupMember列表数据", "type": "object", "properties": {"data": {"description": "The API list data | GroupMember列表数据", "type": "array", "items": {"$ref": "#/definitions/GroupMemberInfo"}, "x-go-name": "Data"}, "total": {"description": "The total number of data | 数据总数", "type": "integer", "format": "uint64", "x-go-name": "Total"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "GroupMemberListReq": {"description": "Get group member list request params | GroupMember列表请求参数", "type": "object", "required": ["page", "pageSize"], "properties": {"groupId": {"type": "integer", "format": "uint64", "x-go-name": "GroupId"}, "page": {"description": "Page number | 第几页", "type": "integer", "format": "uint64", "minimum": 0, "x-go-name": "Page"}, "pageSize": {"description": "Page size | 单页数据行数", "type": "integer", "format": "uint64", "maximum": 100000, "x-go-name": "PageSize"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "GroupMemberListResp": {"description": "The response data of group member list | GroupMember列表数据", "type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/GroupMemberListInfo"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "HeartbeatReq": {"description": "Heartbeat Request | 心跳请求", "type": "object", "properties": {"appCode": {"type": "string", "x-go-name": "AppCode"}, "deviceId": {"type": "string", "x-go-name": "DeviceId"}, "deviceType": {"type": "string", "x-go-name": "DeviceType"}, "machineCode": {"type": "string", "x-go-name": "MachineCode"}, "nonce": {"type": "string", "x-go-name": "<PERSON><PERSON>"}, "signature": {"type": "string", "x-go-name": "Signature"}, "timestamp": {"type": "integer", "format": "int64", "x-go-name": "Timestamp"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "HeartbeatResp": {"description": "Heartbeat Response | 心跳响应", "type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/HeartbeatRespData"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "HeartbeatRespData": {"description": "Heartbeat Response Data | 心跳响应数据", "type": "object", "properties": {"clientExpirationTime": {"description": "Client Expiration Time | 客户端过期时间戳", "type": "integer", "format": "int64", "x-go-name": "ClientExpirationTime"}, "nonce": {"description": "Nonce | 随机字符串", "type": "string", "x-go-name": "<PERSON><PERSON>"}, "serverExpirationTime": {"description": "Server Expiration Time | 服务端过期时间戳", "type": "integer", "format": "int64", "x-go-name": "ServerExpirationTime"}, "serverTime": {"description": "Server Time | 服务器时间戳", "type": "integer", "format": "int64", "x-go-name": "ServerTime"}, "signature": {"description": "Signature | 签名", "type": "string", "x-go-name": "Signature"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "IDInt32PathReq": {"description": "Basic ID request (int32) | 基础ID地址参数请求 (int32)", "type": "object", "required": ["Id"], "properties": {"Id": {"description": "ID", "type": "integer", "format": "int32"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "IDInt32Req": {"description": "Basic ID request (int32) | 基础ID参数请求 (int32)", "type": "object", "required": ["id"], "properties": {"id": {"description": "ID", "type": "integer", "format": "int32", "x-go-name": "Id"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "IDInt64PathReq": {"description": "Basic ID request (int64) | 基础ID地址参数请求 (int64)", "type": "object", "required": ["Id"], "properties": {"Id": {"description": "ID", "type": "integer", "format": "int64"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "IDInt64Req": {"description": "Basic ID request (int64) | 基础ID参数请求 (int64)", "type": "object", "required": ["id"], "properties": {"id": {"description": "ID", "type": "integer", "format": "int64", "x-go-name": "Id"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "IDPathReq": {"description": "Basic ID request | 基础ID地址参数请求", "type": "object", "required": ["Id"], "properties": {"Id": {"description": "ID", "type": "integer", "format": "uint64"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "IDReq": {"description": "Basic ID request | 基础ID参数请求", "type": "object", "required": ["id"], "properties": {"id": {"description": "ID", "type": "integer", "format": "uint64", "x-go-name": "Id"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "IDUint32PathReq": {"description": "Basic ID request (uint32) | 基础ID地址参数请求 (uint32)", "type": "object", "required": ["Id"], "properties": {"Id": {"description": "ID", "type": "integer", "format": "uint32"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "IDUint32Req": {"description": "Basic ID request (uint32) | 基础ID参数请求 (uint32)", "type": "object", "required": ["id"], "properties": {"id": {"description": "ID", "type": "integer", "format": "uint32", "x-go-name": "Id"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "IDsInt32Req": {"description": "Basic IDs request (int32) | 基础ID数组参数请求 (int32)", "type": "object", "required": ["ids"], "properties": {"ids": {"description": "IDs", "type": "array", "items": {"type": "integer", "format": "int32"}, "x-go-name": "Ids"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "IDsInt64Req": {"description": "Basic IDs request (int64) | 基础ID数组参数请求 (int64)", "type": "object", "required": ["ids"], "properties": {"ids": {"description": "IDs", "type": "array", "items": {"type": "integer", "format": "int64"}, "x-go-name": "Ids"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "IDsReq": {"description": "Basic IDs request | 基础ID数组参数请求", "type": "object", "required": ["ids"], "properties": {"ids": {"description": "IDs", "type": "array", "items": {"type": "integer", "format": "uint64"}, "x-go-name": "Ids"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "IDsUint32Req": {"description": "Basic IDs request (uint32) | 基础ID数组参数请求 (uint32)", "type": "object", "required": ["ids"], "properties": {"ids": {"description": "IDs", "type": "array", "items": {"type": "integer", "format": "uint32"}, "x-go-name": "Ids"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ImageAuditedCallbackReq": {"type": "object", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "IsThumbupReq": {"type": "object", "properties": {"bizId": {"type": "string", "x-go-name": "BizId"}, "targetId": {"type": "integer", "format": "int64", "x-go-name": "TargetId"}, "userId": {"type": "integer", "format": "int64", "x-go-name": "UserId"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "IsThumbupResp": {"type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/IsThumbupRespData"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "IsThumbupRespData": {"type": "object", "properties": {"userThumbup": {"$ref": "#/definitions/UserThumbup"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "LicenseInfo": {"description": "License | License信息", "type": "object", "properties": {"activationStatus": {"type": "integer", "format": "int32", "x-go-name": "ActivationStatus"}, "activationTime": {"type": "string", "x-go-name": "ActivationTime"}, "licenseKey": {"type": "string", "x-go-name": "LicenseKey"}, "maxDevices": {"type": "integer", "format": "int32", "x-go-name": "MaxDevices"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "LikeRecordItem": {"type": "object", "properties": {"bizId": {"type": "string", "x-go-name": "BizId"}, "createTime": {"type": "integer", "format": "int64", "x-go-name": "CreateTime"}, "id": {"type": "integer", "format": "int64", "x-go-name": "Id"}, "objId": {"type": "integer", "format": "int64", "x-go-name": "ObjId"}, "updateTime": {"type": "integer", "format": "int64", "x-go-name": "UpdateTime"}, "userId": {"type": "integer", "format": "int64", "x-go-name": "UserId"}, "userInfo": {"$ref": "#/definitions/UserInfo"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "LoginReq": {"type": "object", "properties": {"mobile": {"type": "string", "x-go-name": "Mobile"}, "password": {"type": "string", "x-go-name": "Password"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "LoginResp": {"type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/AuthTokenData"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "MaintenInfoInfo": {"description": "The response data of mainten info information | MaintenInfo信息", "type": "object", "properties": {"createTime": {"description": "CreateTime", "type": "integer", "format": "int64", "x-go-name": "CreateTime"}, "deleteTime": {"description": "DeleteTime", "type": "integer", "format": "int64", "x-go-name": "DeleteTime"}, "expressType": {"description": "类型 0:快递 1:邮寄", "type": "integer", "format": "int32", "x-go-name": "ExpressType"}, "faultType": {"description": "故障类型", "type": "integer", "format": "int32", "x-go-name": "FaultType"}, "id": {"type": "integer", "format": "uint64", "x-go-name": "Id"}, "malfuncRemark": {"description": "故障描述", "type": "string", "x-go-name": "MalfuncRemark"}, "malfuncType": {"description": "维修类型", "type": "integer", "format": "int32", "x-go-name": "MalfuncType"}, "mobile": {"description": "联系电话", "type": "string", "x-go-name": "Mobile"}, "orderNo": {"description": "维修订单号", "type": "string", "x-go-name": "OrderNo"}, "pickupAddress": {"description": "取件地址", "type": "string", "x-go-name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "productId": {"description": "关联产品ID", "type": "integer", "format": "uint64", "x-go-name": "ProductId"}, "returnAddress": {"description": "回寄地址", "type": "string", "x-go-name": "<PERSON><PERSON><PERSON><PERSON>"}, "sn": {"description": "sn号", "type": "string", "x-go-name": "Sn"}, "updateTime": {"description": "UpdateTime", "type": "integer", "format": "int64", "x-go-name": "UpdateTime"}, "userId": {"description": "下单用户ID", "type": "integer", "format": "uint64", "x-go-name": "UserId"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "MaintenInfoInfoResp": {"description": "MaintenInfo information response | MaintenInfo信息返回体", "type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/MaintenInfoInfo"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "MaintenInfoListInfo": {"description": "MaintenInfo list data | MaintenInfo列表数据", "type": "object", "properties": {"data": {"description": "The API list data | MaintenInfo列表数据", "type": "array", "items": {"$ref": "#/definitions/MaintenInfoInfo"}, "x-go-name": "Data"}, "total": {"description": "The total number of data | 数据总数", "type": "integer", "format": "uint64", "x-go-name": "Total"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "MaintenInfoListReq": {"description": "Get mainten info list request params | MaintenInfo列表请求参数", "type": "object", "required": ["page", "pageSize"], "properties": {"malfuncRemark": {"description": "MalfuncRemark", "type": "string", "x-go-name": "MalfuncRemark"}, "page": {"description": "Page number | 第几页", "type": "integer", "format": "uint64", "minimum": 0, "x-go-name": "Page"}, "pageSize": {"description": "Page size | 单页数据行数", "type": "integer", "format": "uint64", "maximum": 100000, "x-go-name": "PageSize"}, "pickupAddress": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "string", "x-go-name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "returnAddress": {"description": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "x-go-name": "<PERSON><PERSON><PERSON><PERSON>"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "MaintenInfoListResp": {"description": "The response data of mainten info list | MaintenInfo列表数据", "type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/MaintenInfoListInfo"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "MallOrderInfo": {"description": "The response data of mall order information | 订单信息", "type": "object", "properties": {"createdAt": {"description": "Create date | 创建日期", "type": "integer", "format": "int64", "x-go-name": "CreatedAt"}, "currency": {"description": "货币类型", "type": "string", "x-go-name": "<PERSON><PERSON><PERSON><PERSON>"}, "id": {"description": "ID", "type": "integer", "format": "uint64", "x-go-name": "Id"}, "items": {"description": "订单Item", "type": "array", "items": {"$ref": "#/definitions/MallOrderItemInfo"}, "x-go-name": "Items"}, "orderNo": {"description": "订单编号", "type": "string", "x-go-name": "OrderNo"}, "orderType": {"description": "订单类型:1实物订单,2虚拟订单", "type": "integer", "format": "int32", "x-go-name": "OrderType"}, "payAmount": {"description": "支付金额(分)", "type": "integer", "format": "int64", "x-go-name": "PayAmount"}, "payChannel": {"description": "支付渠道", "type": "integer", "format": "int32", "x-go-name": "PayChannel"}, "payChannelOrderNo": {"description": "支付渠道订单号", "type": "string", "x-go-name": "PayChannelOrderNo"}, "payTime": {"description": "支付时间", "type": "integer", "format": "int64", "x-go-name": "PayTime"}, "remark": {"description": "备注", "type": "string", "x-go-name": "Remark"}, "sn": {"description": "设备序列号", "type": "string", "x-go-name": "Sn"}, "status": {"description": "状态:0待支付,1已支付,2已发货,3已完成,4已取消,5已退款,6部分退款", "type": "integer", "format": "uint32", "x-go-name": "Status"}, "totalAmount": {"description": "总金额(分)", "type": "integer", "format": "int64", "x-go-name": "TotalAmount"}, "updatedAt": {"description": "Update date | 更新日期", "type": "integer", "format": "int64", "x-go-name": "UpdatedAt"}, "userId": {"description": "用户ID", "type": "integer", "format": "uint64", "x-go-name": "UserId"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "MallOrderInfoResp": {"description": "The mall order information response | MallOrder信息返回体", "type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/MallOrderInfo"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "MallOrderItemInfo": {"description": "The response data of mall order item information | MallOrderItem信息", "type": "object", "properties": {"createdAt": {"description": "Create date | 创建日期", "type": "integer", "format": "int64", "x-go-name": "CreatedAt"}, "id": {"description": "ID", "type": "integer", "format": "uint64", "x-go-name": "Id"}, "itemNo": {"description": "关联订单商品编号", "type": "string", "x-go-name": "ItemNo"}, "orderNo": {"description": "订单编号", "type": "string", "x-go-name": "OrderNo"}, "price": {"description": "单价(分)", "type": "integer", "format": "int64", "x-go-name": "Price"}, "productBrief": {"description": "商品简介(下单时)", "type": "string", "x-go-name": "ProductBrief"}, "productCoverImage": {"description": "商品封面图(下单时)", "type": "string", "x-go-name": "ProductCoverImage"}, "productCurrency": {"description": "货币类型(下单时)", "type": "string", "x-go-name": "ProductCurrency"}, "productDescription": {"description": "商品详细描述(下单时)", "type": "string", "x-go-name": "ProductDescription"}, "productId": {"description": "商品ID", "type": "integer", "format": "uint64", "x-go-name": "ProductId"}, "productImages": {"description": "商品图片(下单时)", "type": "string", "x-go-name": "ProductImages"}, "productName": {"description": "商品名称", "type": "string", "x-go-name": "ProductName"}, "productOriginalPrice": {"description": "商品原价(分)(下单时)", "type": "integer", "format": "int64", "x-go-name": "ProductOriginalPrice"}, "productSellingPrice": {"description": "商品售价(分)(下单时)", "type": "integer", "format": "int64", "x-go-name": "ProductSellingPrice"}, "productType": {"description": "商品类型:1实物,2虚拟(下单时)", "type": "integer", "format": "int32", "x-go-name": "ProductType"}, "quantity": {"description": "数量", "type": "integer", "format": "int64", "x-go-name": "Quantity"}, "quotaAmount": {"description": "购买的配额数量", "type": "integer", "format": "int64", "x-go-name": "QuotaAmount"}, "quotaType": {"description": "配额类型:1 api_calls-API调用次数,2 tokens-Token数量", "type": "integer", "format": "int32", "x-go-name": "QuotaType"}, "skuAttributesSnapshot": {"description": "SKU属性快照JSON", "type": "string", "x-go-name": "SkuAttributesSnapshot"}, "skuCode": {"description": "商品SKU CODE", "type": "string", "x-go-name": "SkuCode"}, "skuId": {"description": "SKU ID", "type": "integer", "format": "uint64", "x-go-name": "SkuId"}, "skuName": {"description": "SKU名称", "type": "string", "x-go-name": "SkuName"}, "totalAmount": {"description": "总金额(分)", "type": "integer", "format": "int64", "x-go-name": "TotalAmount"}, "unitPrice": {"description": "单价(分/次或分/token)", "type": "integer", "format": "int64", "x-go-name": "UnitPrice"}, "updatedAt": {"description": "Update date | 更新日期", "type": "integer", "format": "int64", "x-go-name": "UpdatedAt"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "MallOrderItemInfoResp": {"description": "The mall order item information response | MallOrderItem信息返回体", "type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/MallOrderItemInfo"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "MallOrderItemListInfo": {"description": "The mall order item list data | MallOrderItem信息列表数据", "type": "object", "properties": {"data": {"description": "The mall order item list data | MallOrderItem信息列表数据", "type": "array", "items": {"$ref": "#/definitions/MallOrderItemInfo"}, "x-go-name": "Data"}, "total": {"description": "The total number of data | 数据总数", "type": "integer", "format": "uint64", "x-go-name": "Total"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "MallOrderItemListReq": {"description": "Get mall order item list request params | MallOrderItem列表请求参数", "type": "object", "required": ["page", "pageSize"], "properties": {"deletedAt": {"description": "DeletedAt", "type": "integer", "format": "int64", "x-go-name": "DeletedAt"}, "orderNo": {"description": "OrderNo", "type": "string", "x-go-name": "OrderNo"}, "page": {"description": "Page number | 第几页", "type": "integer", "format": "uint64", "minimum": 0, "x-go-name": "Page"}, "pageSize": {"description": "Page size | 单页数据行数", "type": "integer", "format": "uint64", "maximum": 100000, "x-go-name": "PageSize"}, "price": {"description": "Price", "type": "integer", "format": "int64", "x-go-name": "Price"}, "productBrief": {"description": "ProductBrief", "type": "string", "x-go-name": "ProductBrief"}, "productCoverImage": {"description": "ProductCoverImage", "type": "string", "x-go-name": "ProductCoverImage"}, "productCurrency": {"description": "ProductCurrency", "type": "string", "x-go-name": "ProductCurrency"}, "productDescription": {"description": "ProductDescription", "type": "string", "x-go-name": "ProductDescription"}, "productId": {"description": "ProductId", "type": "integer", "format": "uint64", "x-go-name": "ProductId"}, "productImages": {"description": "ProductImages", "type": "string", "x-go-name": "ProductImages"}, "productName": {"description": "ProductName", "type": "string", "x-go-name": "ProductName"}, "productOriginalPrice": {"description": "ProductOriginalPrice", "type": "integer", "format": "int64", "x-go-name": "ProductOriginalPrice"}, "productSellingPrice": {"description": "ProductSellingPrice", "type": "integer", "format": "int64", "x-go-name": "ProductSellingPrice"}, "productType": {"description": "ProductType", "type": "integer", "format": "int32", "x-go-name": "ProductType"}, "quantity": {"description": "Quantity", "type": "integer", "format": "int64", "x-go-name": "Quantity"}, "quotaAmount": {"description": "QuotaAmount", "type": "integer", "format": "int64", "x-go-name": "QuotaAmount"}, "quotaType": {"description": "QuotaType", "type": "integer", "format": "int32", "x-go-name": "QuotaType"}, "skuAttributesSnapshot": {"description": "SkuAttributesSnapshot", "type": "string", "x-go-name": "SkuAttributesSnapshot"}, "skuCode": {"description": "SkuCode", "type": "string", "x-go-name": "SkuCode"}, "skuId": {"description": "SkuId", "type": "integer", "format": "uint64", "x-go-name": "SkuId"}, "skuName": {"description": "SkuName", "type": "string", "x-go-name": "SkuName"}, "totalAmount": {"description": "TotalAmount", "type": "integer", "format": "int64", "x-go-name": "TotalAmount"}, "unitPrice": {"description": "UnitPrice", "type": "integer", "format": "int64", "x-go-name": "UnitPrice"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "MallOrderItemListResp": {"description": "The response data of mall order item list | MallOrderItem信息列表数据", "type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/MallOrderItemListInfo"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "MallOrderListInfo": {"description": "The mall order list data | 订单列表数据", "type": "object", "properties": {"data": {"description": "The mall order list data | 订单列表数据", "type": "array", "items": {"$ref": "#/definitions/MallOrderInfo"}, "x-go-name": "Data"}, "total": {"description": "The total number of data | 数据总数", "type": "integer", "format": "uint64", "x-go-name": "Total"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "MallOrderListReq": {"description": "Get mall order list request params | 订单列表请求参数", "type": "object", "required": ["page", "pageSize"], "properties": {"currency": {"description": "货币类型", "type": "string", "x-go-name": "<PERSON><PERSON><PERSON><PERSON>"}, "deviceId": {"description": "设备序列号", "type": "string", "x-go-name": "DeviceId"}, "fromStatus": {"description": "状态:0待支付,1已支付,2已发货,3已完成,4已取消,5已退款,6部分退款", "type": "integer", "format": "int32", "x-go-name": "FromStatus"}, "machineCode": {"description": "机器码", "type": "string", "x-go-name": "MachineCode"}, "operatorId": {"description": "操作员ID", "type": "integer", "format": "uint64", "x-go-name": "OperatorId"}, "operatorName": {"description": "操作员名称", "type": "string", "x-go-name": "OperatorName"}, "orderId": {"description": "订单ID", "type": "integer", "format": "uint64", "x-go-name": "OrderId"}, "orderNo": {"description": "订单编号", "type": "string", "x-go-name": "OrderNo"}, "orderType": {"description": "订单类型:1实物订单,2虚拟订单", "type": "integer", "format": "int32", "x-go-name": "OrderType"}, "page": {"description": "Page number | 第几页", "type": "integer", "format": "uint64", "minimum": 0, "x-go-name": "Page"}, "pageSize": {"description": "Page size | 单页数据行数", "type": "integer", "format": "uint64", "maximum": 100000, "x-go-name": "PageSize"}, "payAmount": {"description": "支付金额(分)", "type": "integer", "format": "int64", "x-go-name": "PayAmount"}, "payChannel": {"description": "支付渠道", "type": "integer", "format": "int32", "x-go-name": "PayChannel"}, "payChannelOrderNo": {"description": "支付渠道订单号", "type": "string", "x-go-name": "PayChannelOrderNo"}, "payTime": {"description": "支付时间", "type": "integer", "format": "int64", "x-go-name": "PayTime"}, "price": {"description": "单价", "type": "integer", "format": "int64", "x-go-name": "Price"}, "productId": {"description": "商品ID", "type": "integer", "format": "uint64", "x-go-name": "ProductId"}, "productName": {"description": "商品名称", "type": "string", "x-go-name": "ProductName"}, "quantity": {"description": "购买数量", "type": "integer", "format": "int32", "x-go-name": "Quantity"}, "quotaAmount": {"description": "配额金额", "type": "integer", "format": "int64", "x-go-name": "QuotaAmount"}, "quotaType": {"description": "配额类型", "type": "integer", "format": "int32", "x-go-name": "QuotaType"}, "remark": {"description": "备注", "type": "string", "x-go-name": "Remark"}, "skuId": {"description": "SKU ID", "type": "integer", "format": "uint64", "x-go-name": "SkuId"}, "skuName": {"description": "SKU名称", "type": "string", "x-go-name": "SkuName"}, "status": {"description": "状态:0待支付,1已支付,2已发货,3已完成,4已取消,5已退款,6部分退款", "type": "integer", "format": "uint32", "x-go-name": "Status"}, "toStatus": {"description": "状态:0待支付,1已支付,2已发货,3已完成,4已取消,5已退款,6部分退款", "type": "integer", "format": "int32", "x-go-name": "ToStatus"}, "totalAmount": {"description": "总金额", "type": "integer", "format": "int64", "x-go-name": "TotalAmount"}, "unitPrice": {"description": "单价", "type": "integer", "format": "int64", "x-go-name": "UnitPrice"}, "userId": {"description": "用户ID", "type": "integer", "format": "uint64", "x-go-name": "UserId"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "MallOrderListResp": {"description": "The response data of mall order list | 订单列表数据", "type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/MallOrderListInfo"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "MallOrderSubmitReq": {"type": "object", "required": ["orderNo", "payChannel"], "properties": {"channelExtras": {"description": "支付渠道额外参数", "type": "object", "additionalProperties": {"type": "string"}, "x-go-name": "ChannelExtras"}, "currency": {"description": "货币类型", "type": "string", "x-go-name": "<PERSON><PERSON><PERSON><PERSON>"}, "orderNo": {"description": "订单编号", "type": "string", "x-go-name": "OrderNo"}, "payChannel": {"description": "支付渠道", "type": "integer", "format": "int32", "x-go-name": "PayChannel"}, "tradeType": {"description": "交易类型", "type": "string", "x-go-name": "TradeType"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "MallOrderSubmitResp": {"type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/MallOrderSubmitRespData"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "MallOrderSubmitRespData": {"type": "object", "properties": {"orderNo": {"description": "订单编号", "type": "string", "x-go-name": "OrderNo"}, "payParams": {"description": "支付参数", "type": "string", "x-go-name": "PayParams"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "MallProductInfo": {"description": "The response data of mall product information | 商品信息", "type": "object", "properties": {"brief": {"description": "商品简介", "type": "string", "x-go-name": "Brief"}, "categoryId": {"description": "分类ID", "type": "integer", "format": "uint64", "x-go-name": "CategoryId"}, "coverImage": {"description": "封面图片", "type": "string", "x-go-name": "CoverImage"}, "createdAt": {"description": "Create date | 创建日期", "type": "integer", "format": "int64", "x-go-name": "CreatedAt"}, "currency": {"description": "货币类型", "type": "string", "x-go-name": "<PERSON><PERSON><PERSON><PERSON>"}, "description": {"description": "商品详细描述", "type": "string", "x-go-name": "Description"}, "id": {"description": "ID", "type": "integer", "format": "uint64", "x-go-name": "Id"}, "images": {"description": "商品图片", "type": "string", "x-go-name": "Images"}, "name": {"description": "商品名称", "type": "string", "x-go-name": "Name"}, "productCode": {"description": "商品编码", "type": "string", "x-go-name": "ProductCode"}, "status": {"description": "状态:0下架,1上架,2预售,3缺货", "type": "integer", "format": "uint32", "x-go-name": "Status"}, "totalStock": {"description": "总库存(所有SKU库存之和)", "type": "integer", "format": "int64", "x-go-name": "TotalStock"}, "type": {"description": "商品类型:1实物,2虚拟", "type": "integer", "format": "int32", "x-go-name": "Type"}, "updatedAt": {"description": "Update date | 更新日期", "type": "integer", "format": "int64", "x-go-name": "UpdatedAt"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "MallProductInfoResp": {"description": "The mall product information response | 商品信息返回体", "type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/MallProductInfo"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "MallProductListInfo": {"description": "The mall product list data | 商品信息列表数据", "type": "object", "properties": {"data": {"description": "The mall product list data | 商品信息列表数据", "type": "array", "items": {"$ref": "#/definitions/MallProductInfo"}, "x-go-name": "Data"}, "total": {"description": "The total number of data | 数据总数", "type": "integer", "format": "uint64", "x-go-name": "Total"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "MallProductListReq": {"description": "Get mall product list request params | 商品列表请求参数", "type": "object", "required": ["page", "pageSize"], "properties": {"attributes": {"description": "属性", "type": "string", "x-go-name": "Attributes"}, "brief": {"description": "商品简介", "type": "string", "x-go-name": "Brief"}, "categoryId": {"description": "分类ID", "type": "integer", "format": "uint64", "x-go-name": "CategoryId"}, "coverImage": {"description": "封面图片", "type": "string", "x-go-name": "CoverImage"}, "currency": {"description": "货币类型", "type": "string", "x-go-name": "<PERSON><PERSON><PERSON><PERSON>"}, "description": {"description": "商品描述", "type": "string", "x-go-name": "Description"}, "images": {"description": "商品图片", "type": "string", "x-go-name": "Images"}, "maxPurchase": {"description": "最大购买量", "type": "integer", "format": "int64", "x-go-name": "MaxPurchase"}, "minPurchase": {"description": "最小购买量", "type": "integer", "format": "int64", "x-go-name": "MinPurchase"}, "name": {"description": "商品名称", "type": "string", "x-go-name": "Name"}, "page": {"description": "Page number | 第几页", "type": "integer", "format": "uint64", "minimum": 0, "x-go-name": "Page"}, "pageSize": {"description": "Page size | 单页数据行数", "type": "integer", "format": "uint64", "maximum": 100000, "x-go-name": "PageSize"}, "price": {"description": "价格", "type": "integer", "format": "int64", "x-go-name": "Price"}, "productCode": {"description": "商品编码", "type": "string", "x-go-name": "ProductCode"}, "productId": {"description": "商品ID", "type": "integer", "format": "uint64", "x-go-name": "ProductId"}, "quotaType": {"description": "配额类型", "type": "integer", "format": "int32", "x-go-name": "QuotaType"}, "quotaValue": {"description": "配额值", "type": "integer", "format": "int64", "x-go-name": "Quo<PERSON><PERSON><PERSON><PERSON>"}, "skuCode": {"description": "SKU编码", "type": "string", "x-go-name": "SkuCode"}, "status": {"description": "状态:0下架,1上架,2预售,3缺货", "type": "integer", "format": "uint32", "x-go-name": "Status"}, "totalStock": {"description": "总库存", "type": "integer", "format": "int64", "x-go-name": "TotalStock"}, "type": {"description": "商品类型:1实物,2虚拟", "type": "integer", "format": "int32", "x-go-name": "Type"}, "unitPrice": {"description": "单价", "type": "integer", "format": "int64", "x-go-name": "UnitPrice"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "MallProductListResp": {"description": "The response data of mall product list | 商品信息列表数据", "type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/MallProductListInfo"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "MallProductResp": {"description": "The product information response | 商品信息返回体", "type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/MallProductRespData"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "MallProductRespData": {"description": "The product information response data | 商品信息返回体数据", "type": "object", "properties": {"appId": {"description": "APP_ID", "type": "integer", "format": "uint64", "x-go-name": "AppId"}, "appLogo": {"description": "APP_LOGO", "type": "string", "x-go-name": "AppLogo"}, "appName": {"description": "APP_NAME", "type": "string", "x-go-name": "AppName"}, "brief": {"description": "商品简介", "type": "string", "x-go-name": "Brief"}, "coverImage": {"description": "封面图片", "type": "string", "x-go-name": "CoverImage"}, "currency": {"description": "货币类型", "type": "string", "x-go-name": "<PERSON><PERSON><PERSON><PERSON>"}, "description": {"description": "商品详细描述", "type": "string", "x-go-name": "Description"}, "images": {"description": "商品图片", "type": "string", "x-go-name": "Images"}, "name": {"description": "商品名称", "type": "string", "x-go-name": "Name"}, "productCode": {"description": "商品编码", "type": "string", "x-go-name": "ProductCode"}, "productId": {"description": "商品ID", "type": "integer", "format": "uint64", "x-go-name": "ProductId"}, "skuList": {"description": "SKU列表", "type": "array", "items": {"$ref": "#/definitions/MallProductSkuInfo"}, "x-go-name": "SkuList"}, "type": {"description": "商品类型:1实物,2虚拟", "type": "integer", "format": "int32", "x-go-name": "Type"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "MallProductSkuInfo": {"description": "The response data of mall product sku information | 商品SKU信息", "type": "object", "properties": {"attributes": {"description": "属性JSON", "type": "string", "x-go-name": "Attributes"}, "createdAt": {"description": "Create date | 创建日期", "type": "integer", "format": "int64", "x-go-name": "CreatedAt"}, "height": {"description": "高度(cm)", "type": "integer", "format": "int32", "x-go-name": "Height"}, "id": {"description": "ID", "type": "integer", "format": "uint64", "x-go-name": "Id"}, "image": {"description": "图片", "type": "string", "x-go-name": "Image"}, "isOnSpecial": {"description": "是否特价:0否,1是", "type": "integer", "format": "int32", "x-go-name": "IsOnSpecial"}, "length": {"description": "长度(cm)", "type": "integer", "format": "int32", "x-go-name": "Length"}, "name": {"description": "SKU 名称", "type": "string", "x-go-name": "Name"}, "originalPrice": {"description": "原价(分)", "type": "integer", "format": "int64", "x-go-name": "OriginalPrice"}, "productId": {"description": "商品ID", "type": "integer", "format": "uint64", "x-go-name": "ProductId"}, "productType": {"description": "商品类型:0、普通商品,1、license商品", "type": "integer", "format": "int32", "x-go-name": "ProductType"}, "sellOutRestoreOrigin": {"description": "特价商品卖完恢复原价:0、不恢复 1、恢复", "type": "integer", "format": "int32", "x-go-name": "SellOutRestoreOrigin"}, "sellingPrice": {"description": "售价(分)", "type": "integer", "format": "int64", "x-go-name": "SellingPrice"}, "skuCode": {"description": "SKU编码", "type": "string", "x-go-name": "SkuCode"}, "specialPrice": {"description": "特价", "type": "integer", "format": "int64", "x-go-name": "SpecialPrice"}, "specialPriceEndTime": {"description": "特价结束时间", "type": "integer", "format": "int64", "x-go-name": "SpecialPriceEndTime"}, "specialPriceStartTime": {"description": "特价开始时间", "type": "integer", "format": "int64", "x-go-name": "SpecialPriceStartTime"}, "specialSaleLimit": {"description": "限购数量(单个用户/设备)", "type": "integer", "format": "int64", "x-go-name": "SpecialSaleLimit"}, "specialSaleType": {"description": "特价类型:0无,1新用户特价,2新设备特价,3限时特价", "type": "integer", "format": "int32", "x-go-name": "SpecialSaleType"}, "status": {"description": "状态:0禁用,1启用", "type": "integer", "format": "uint32", "x-go-name": "Status"}, "stock": {"description": "库存", "type": "integer", "format": "int64", "x-go-name": "Stock"}, "updatedAt": {"description": "Update date | 更新日期", "type": "integer", "format": "int64", "x-go-name": "UpdatedAt"}, "usageCount": {"description": "授权天数/用量", "type": "integer", "format": "int64", "x-go-name": "UsageCount"}, "weight": {"description": "重量(g)", "type": "integer", "format": "int32", "x-go-name": "Weight"}, "width": {"description": "宽度(cm)", "type": "integer", "format": "int32", "x-go-name": "<PERSON><PERSON><PERSON>"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "MallProductSkuInfoResp": {"description": "The mall product sku information response | 商品SKU信息返回体", "type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/MallProductSkuInfo"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "MallProductSkuListInfo": {"description": "The mall product sku list data | 商品SKU信息列表数据", "type": "object", "properties": {"data": {"description": "The mall product sku list data | 商品SKU信息列表数据", "type": "array", "items": {"$ref": "#/definitions/MallProductSkuInfo"}, "x-go-name": "Data"}, "total": {"description": "The total number of data | 数据总数", "type": "integer", "format": "uint64", "x-go-name": "Total"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "MallProductSkuListReq": {"description": "Get mall product sku list request params | MallProductSku列表请求参数", "type": "object", "required": ["page", "pageSize"], "properties": {"isOnSpecial": {"description": "是否特价:0否,1是", "type": "integer", "format": "int32", "x-go-name": "IsOnSpecial"}, "page": {"description": "Page number | 第几页", "type": "integer", "format": "uint64", "minimum": 0, "x-go-name": "Page"}, "pageSize": {"description": "Page size | 单页数据行数", "type": "integer", "format": "uint64", "maximum": 100000, "x-go-name": "PageSize"}, "productId": {"description": "DeletedAt\nDeletedAt  *int64 `json:\"deletedAt,optional\"`\nProductId", "type": "integer", "format": "uint64", "x-go-name": "ProductId"}, "productType": {"description": "商品类型:0、普通商品,1、license商品", "type": "integer", "format": "int32", "x-go-name": "ProductType"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "MallProductSkuListResp": {"description": "The response data of mall product sku list | 商品SKU信息列表数据", "type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/MallProductSkuListInfo"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "MessageItem": {"type": "object", "properties": {"content": {"type": "string", "x-go-name": "Content"}, "createTime": {"type": "integer", "format": "int64", "x-go-name": "CreateTime"}, "extraData": {"type": "string", "x-go-name": "ExtraData"}, "fromId": {"type": "integer", "format": "int64", "x-go-name": "FromId"}, "fromUserInfo": {"$ref": "#/definitions/FromUserInfo"}, "id": {"type": "integer", "format": "int64", "x-go-name": "Id"}, "quoteContent": {"type": "string", "x-go-name": "<PERSON>uo<PERSON><PERSON><PERSON><PERSON>"}, "status": {"type": "integer", "format": "int64", "x-go-name": "Status"}, "title": {"type": "string", "x-go-name": "Title"}, "type": {"type": "integer", "format": "int64", "x-go-name": "Type"}, "updateTime": {"type": "integer", "format": "int64", "x-go-name": "UpdateTime"}, "userId": {"type": "integer", "format": "int64", "x-go-name": "UserId"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "MockNotifyReq": {"description": "Mock Payment Notify | 模拟支付回调", "type": "object", "properties": {"ChannelCode": {"description": "渠道编码", "type": "string"}, "orderNo": {"description": "订单编号", "type": "string", "x-go-name": "OrderNo"}, "result": {"description": "支付结果:SUCCESS,FAIL", "type": "string", "x-go-name": "Result"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "NotifyReq": {"description": "Payment Notify | 支付回调", "type": "object", "properties": {"ChannelCode": {"description": "渠道编码", "type": "string"}, "headers": {"description": "HTTP头信息", "type": "string", "x-go-name": "Headers"}, "r": {"description": "回调数据", "type": "array", "items": {"type": "integer", "format": "uint8"}, "x-go-name": "R"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "PageInfo": {"description": "The page request parameters | 列表请求参数", "type": "object", "required": ["page", "pageSize"], "properties": {"page": {"description": "Page number | 第几页", "type": "integer", "format": "uint64", "minimum": 0, "x-go-name": "Page"}, "pageSize": {"description": "Page size | 单页数据行数", "type": "integer", "format": "uint64", "maximum": 100000, "x-go-name": "PageSize"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "PermCodeResp": {"description": "The permission code for front end permission control | 权限码： 用于前端权限控制", "type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Permission code data | 权限码数据", "type": "array", "items": {"type": "string"}, "x-go-name": "Data"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "PoliticsInfo": {"type": "object", "properties": {"count": {"type": "integer", "format": "int64", "x-go-name": "Count"}, "hit_flag": {"type": "integer", "format": "int64", "x-go-name": "HitFlag"}, "label": {"type": "string", "x-go-name": "Label"}, "score": {"type": "integer", "format": "int64", "x-go-name": "Score"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "PornInfo": {"type": "object", "properties": {"count": {"type": "integer", "format": "int64", "x-go-name": "Count"}, "hit_flag": {"type": "integer", "format": "int64", "x-go-name": "HitFlag"}, "label": {"type": "string", "x-go-name": "Label"}, "score": {"type": "integer", "format": "int64", "x-go-name": "Score"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ProductInfo": {"description": "The response data of product information | Product信息", "type": "object", "properties": {"color": {"description": "Color", "type": "string", "x-go-name": "Color"}, "createTime": {"description": "CreateTime", "type": "integer", "format": "int64", "x-go-name": "CreateTime"}, "id": {"type": "integer", "format": "uint64", "x-go-name": "Id"}, "language": {"description": "Language", "type": "string", "x-go-name": "Language"}, "merchant": {"description": "Merchant", "type": "string", "x-go-name": "Merchant"}, "registerTime": {"description": "RegisterTime", "type": "integer", "format": "int64", "x-go-name": "RegisterTime"}, "sn": {"description": "Sn", "type": "string", "x-go-name": "Sn"}, "title": {"description": "Title", "type": "string", "x-go-name": "Title"}, "updateTime": {"description": "UpdateTime", "type": "integer", "format": "int64", "x-go-name": "UpdateTime"}, "userId": {"description": "UserId", "type": "integer", "format": "uint64", "x-go-name": "UserId"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ProductInfoResp": {"description": "Product information response | Product信息返回体", "type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/ProductInfo"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ProductItem": {"type": "object", "properties": {"color": {"type": "string", "x-go-name": "Color"}, "id": {"type": "integer", "format": "int64", "x-go-name": "ID"}, "language": {"type": "string", "x-go-name": "Language"}, "merchant": {"type": "string", "x-go-name": "Merchant"}, "registerTime": {"type": "string", "x-go-name": "RegisterTime"}, "sn": {"type": "string", "x-go-name": "Sn"}, "title": {"type": "string", "x-go-name": "Title"}, "userId": {"type": "integer", "format": "int64", "x-go-name": "UserID"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ProductListInfo": {"description": "Product list data | Product列表数据", "type": "object", "properties": {"data": {"description": "The API list data | Product列表数据", "type": "array", "items": {"$ref": "#/definitions/ProductInfo"}, "x-go-name": "Data"}, "total": {"description": "The total number of data | 数据总数", "type": "integer", "format": "uint64", "x-go-name": "Total"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ProductListReq": {"description": "Get product list request params | Product列表请求参数", "type": "object", "required": ["page", "pageSize"], "properties": {"merchant": {"description": "Merchant", "type": "string", "x-go-name": "Merchant"}, "page": {"description": "Page number | 第几页", "type": "integer", "format": "uint64", "minimum": 0, "x-go-name": "Page"}, "pageSize": {"description": "Page size | 单页数据行数", "type": "integer", "format": "uint64", "maximum": 100000, "x-go-name": "PageSize"}, "sn": {"description": "Sn", "type": "string", "x-go-name": "Sn"}, "title": {"description": "Title", "type": "string", "x-go-name": "Title"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ProductListResp": {"description": "The response data of product list | Product列表数据", "type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/ProductListInfo"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ProvinceInfo": {"description": "The response data of province information | Province信息", "type": "object", "properties": {"code": {"description": "Code", "type": "string", "x-go-name": "Code"}, "geocode": {"description": "Geocode", "type": "string", "x-go-name": "Geocode"}, "id": {"type": "integer", "format": "uint64", "x-go-name": "Id"}, "latitude": {"description": "Latitude", "type": "string", "x-go-name": "Latitude"}, "level": {"description": "Level", "type": "integer", "format": "int32", "x-go-name": "Level"}, "longitude": {"description": "Longitude", "type": "string", "x-go-name": "Longitude"}, "name": {"description": "Name", "type": "string", "x-go-name": "Name"}, "parentCode": {"description": "ParentCode", "type": "string", "x-go-name": "ParentCode"}, "sort": {"description": "Sort", "type": "integer", "format": "int32", "x-go-name": "Sort"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ProvinceInfoResp": {"description": "Province information response | Province信息返回体", "type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/ProvinceInfo"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ProvinceListInfo": {"description": "Province list data | Province列表数据", "type": "object", "properties": {"data": {"description": "The API list data | Province列表数据", "type": "array", "items": {"$ref": "#/definitions/ProvinceInfo"}, "x-go-name": "Data"}, "total": {"description": "The total number of data | 数据总数", "type": "integer", "format": "uint64", "x-go-name": "Total"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ProvinceListReq": {"description": "Get province list request params | Province列表请求参数", "type": "object", "required": ["page", "pageSize"], "properties": {"code": {"description": "Code", "type": "string", "x-go-name": "Code"}, "geocode": {"description": "Geocode", "type": "string", "x-go-name": "Geocode"}, "latitude": {"description": "Latitude", "type": "string", "x-go-name": "Latitude"}, "longitude": {"description": "Longitude", "type": "string", "x-go-name": "Longitude"}, "name": {"description": "Name", "type": "string", "x-go-name": "Name"}, "page": {"description": "Page number | 第几页", "type": "integer", "format": "uint64", "minimum": 0, "x-go-name": "Page"}, "pageSize": {"description": "Page size | 单页数据行数", "type": "integer", "format": "uint64", "maximum": 100000, "x-go-name": "PageSize"}, "parentCode": {"description": "ParentCode", "type": "string", "x-go-name": "ParentCode"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ProvinceListResp": {"description": "The response data of province list | Province列表数据", "type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/ProvinceListInfo"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "PublishReq": {"type": "object", "required": ["title", "content", "description", "cover", "videos", "groupId", "type"], "properties": {"content": {"type": "string", "x-go-name": "Content"}, "cover": {"type": "string", "x-go-name": "Cover"}, "description": {"type": "string", "x-go-name": "Description"}, "groupId": {"type": "integer", "format": "int64", "x-go-name": "GroupId"}, "tagIds": {"type": "string", "x-go-name": "TagIds"}, "title": {"type": "string", "x-go-name": "Title"}, "type": {"type": "integer", "format": "int64", "x-go-name": "Type"}, "videos": {"type": "string", "x-go-name": "Videos"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "PublishResp": {"type": "object", "properties": {"articleId": {"type": "integer", "format": "int64", "x-go-name": "ArticleId"}, "code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/PublishRespData"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "PublishRespData": {"type": "object", "properties": {"data": {"$ref": "#/definitions/ArticleItem"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "QueryLicenseReq": {"description": "Query License | 查询License", "type": "object", "properties": {"orderNo": {"type": "string", "x-go-name": "OrderNo"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "QueryLicenseResp": {"description": "Query License Response | 查询License响应", "type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/LicenseInfo"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "QueryPaymentStatusReq": {"description": "查询支付状态请求", "type": "object", "properties": {"orderNo": {"type": "string", "x-go-name": "OrderNo"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "QueryPaymentStatusResp": {"description": "查询支付状态响应", "type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/QueryPaymentStatusRespData"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "QueryPaymentStatusRespData": {"description": "查询支付状态响应数据", "type": "object", "properties": {"licenseKey": {"type": "string", "x-go-name": "LicenseKey"}, "payTime": {"type": "string", "x-go-name": "PayTime"}, "tradeState": {"type": "integer", "format": "uint32", "x-go-name": "TradeState"}, "tradeStateDesc": {"type": "string", "x-go-name": "TradeStateDesc"}, "transactionId": {"type": "string", "x-go-name": "TransactionId"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ReadMessageReq": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "x-go-name": "Id"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ReadMessageResp": {"type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/ReadMessageRespData"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ReadMessageRespData": {"type": "object", "properties": {"status": {"type": "boolean", "x-go-name": "Status"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "RealNameAuthReq": {"type": "object", "properties": {"idCard": {"type": "string", "x-go-name": "IdCard"}, "realName": {"type": "string", "x-go-name": "RealName"}, "userId": {"type": "integer", "format": "int64", "x-go-name": "UserId"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "RealNameAuthResp": {"type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/RealNameAuthRespData"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "RealNameAuthRespData": {"type": "object", "properties": {"RealNameVerified": {"type": "integer", "format": "int32"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "RegisterByAuthIdReq": {"type": "object", "properties": {"authKey": {"type": "string", "x-go-name": "<PERSON><PERSON><PERSON><PERSON>"}, "authType": {"type": "string", "x-go-name": "AuthType"}, "avatar": {"type": "string", "x-go-name": "Avatar"}, "mobile": {"type": "string", "x-go-name": "Mobile"}, "nickname": {"type": "string", "x-go-name": "<PERSON><PERSON><PERSON>"}, "password": {"type": "string", "x-go-name": "Password"}, "verifyCode": {"type": "string", "x-go-name": "VerifyCode"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "RegisterReq": {"type": "object", "properties": {"mobile": {"type": "string", "x-go-name": "Mobile"}, "password": {"type": "string", "x-go-name": "Password"}, "verifyCode": {"type": "string", "x-go-name": "VerifyCode"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "RegisterResp": {"type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/AuthTokenData"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "RentalDeviceInfo": {"description": "The response data of rental device information | RentalDevice信息", "type": "object", "properties": {"createdAt": {"description": "Create date | 创建日期", "type": "integer", "format": "int64", "x-go-name": "CreatedAt"}, "description": {"description": "设备描述", "type": "string", "x-go-name": "Description"}, "deviceStatus": {"description": "设备状态:0禁用,1启用", "type": "integer", "format": "uint32", "x-go-name": "DeviceStatus"}, "extraInfo": {"description": "额外信息JSON", "type": "string", "x-go-name": "ExtraInfo"}, "id": {"description": "ID", "type": "integer", "format": "uint64", "x-go-name": "Id"}, "serialNumber": {"description": "设备序列号SN", "type": "string", "x-go-name": "SerialNumber"}, "updatedAt": {"description": "Update date | 更新日期", "type": "integer", "format": "int64", "x-go-name": "UpdatedAt"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "RentalDeviceInfoResp": {"description": "The rental device information response | RentalDevice信息返回体", "type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/RentalDeviceInfo"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "RentalDeviceListInfo": {"description": "The rental device list data | RentalDevice信息列表数据", "type": "object", "properties": {"data": {"description": "The rental device list data | RentalDevice信息列表数据", "type": "array", "items": {"$ref": "#/definitions/RentalDeviceInfo"}, "x-go-name": "Data"}, "total": {"description": "The total number of data | 数据总数", "type": "integer", "format": "uint64", "x-go-name": "Total"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "RentalDeviceListReq": {"description": "Get rental device list request params | RentalDevice列表请求参数", "type": "object", "required": ["page", "pageSize"], "properties": {"createdAt": {"description": "CreatedAt", "type": "integer", "format": "int64", "x-go-name": "CreatedAt"}, "deletedAt": {"description": "DeletedAt", "type": "integer", "format": "int64", "x-go-name": "DeletedAt"}, "description": {"description": "Description", "type": "string", "x-go-name": "Description"}, "deviceStatus": {"description": "DeviceStatus", "type": "integer", "format": "uint32", "x-go-name": "DeviceStatus"}, "extraInfo": {"description": "ExtraInfo", "type": "string", "x-go-name": "ExtraInfo"}, "page": {"description": "Page number | 第几页", "type": "integer", "format": "uint64", "minimum": 0, "x-go-name": "Page"}, "pageSize": {"description": "Page size | 单页数据行数", "type": "integer", "format": "uint64", "maximum": 100000, "x-go-name": "PageSize"}, "serialNumber": {"description": "SerialNumber", "type": "string", "x-go-name": "SerialNumber"}, "updatedAt": {"description": "UpdatedAt", "type": "integer", "format": "int64", "x-go-name": "UpdatedAt"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "RentalDeviceListResp": {"description": "The response data of rental device list | RentalDevice信息列表数据", "type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/RentalDeviceListInfo"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ReplyArticleInfo": {"type": "object", "properties": {"cover": {"type": "string", "x-go-name": "Cover"}, "id": {"type": "integer", "format": "int64", "x-go-name": "Id"}, "title": {"type": "string", "x-go-name": "Title"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ReplyItem": {"type": "object", "properties": {"beReplyUserId": {"type": "integer", "format": "int64", "x-go-name": "BeReplyUserId"}, "bizId": {"type": "string", "x-go-name": "BizId"}, "content": {"type": "string", "x-go-name": "Content"}, "createTime": {"type": "integer", "format": "int64", "x-go-name": "CreateTime"}, "id": {"type": "integer", "format": "int64", "x-go-name": "Id"}, "isLike": {"type": "integer", "format": "int64", "x-go-name": "IsLike"}, "likeNum": {"type": "integer", "format": "int64", "x-go-name": "LikeNum"}, "parentId": {"type": "integer", "format": "int64", "x-go-name": "ParentId"}, "replyArticleInfo": {"$ref": "#/definitions/ReplyArticleInfo"}, "replyUserId": {"type": "integer", "format": "int64", "x-go-name": "ReplyUserId"}, "replyUserInfo": {"$ref": "#/definitions/ReplyUserInfo"}, "status": {"type": "integer", "format": "int64", "x-go-name": "Status"}, "targetId": {"type": "integer", "format": "int64", "x-go-name": "TargetId"}, "updateTime": {"type": "integer", "format": "int64", "x-go-name": "UpdateTime"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ReplyListReq": {"type": "object", "properties": {"bizId": {"type": "string", "x-go-name": "BizId"}, "cursor": {"type": "integer", "format": "int64", "x-go-name": "<PERSON><PERSON><PERSON>"}, "pageSize": {"type": "integer", "format": "int64", "x-go-name": "PageSize"}, "sortType": {"type": "integer", "format": "int64", "x-go-name": "SortType"}, "targetId": {"type": "integer", "format": "int64", "x-go-name": "TargetId"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ReplyListResp": {"type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/ReplyListRespData"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ReplyListRespData": {"type": "object", "properties": {"cursor": {"type": "integer", "format": "int64", "x-go-name": "<PERSON><PERSON><PERSON>"}, "id": {"type": "integer", "format": "int64", "x-go-name": "Id"}, "isEnd": {"type": "boolean", "x-go-name": "IsEnd"}, "items": {"type": "array", "items": {"$ref": "#/definitions/ReplyItem"}, "x-go-name": "Items"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ReplyReq": {"type": "object", "properties": {"beReplyUserId": {"type": "integer", "format": "int64", "x-go-name": "BeReplyUserId"}, "bizId": {"type": "string", "x-go-name": "BizId"}, "content": {"type": "string", "x-go-name": "Content"}, "parentId": {"type": "integer", "format": "int64", "x-go-name": "ParentId"}, "replyUserId": {"type": "integer", "format": "int64", "x-go-name": "ReplyUserId"}, "targetId": {"type": "integer", "format": "int64", "x-go-name": "TargetId"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ReplyResp": {"type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/ReplyRespData"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ReplyRespData": {"type": "object", "properties": {"item": {"$ref": "#/definitions/ReplyItem"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ReplyUserInfo": {"type": "object", "properties": {"avatar": {"type": "string", "x-go-name": "Avatar"}, "backgroundImage": {"type": "string", "x-go-name": "BackgroundImage"}, "description": {"type": "string", "x-go-name": "Description"}, "homePage": {"type": "string", "x-go-name": "HomePage"}, "id": {"type": "integer", "format": "int64", "x-go-name": "Id"}, "info": {"type": "string", "x-go-name": "Info"}, "mobile": {"type": "string", "x-go-name": "Mobile"}, "nickname": {"type": "string", "x-go-name": "Nickname"}, "sex": {"type": "integer", "format": "int64", "x-go-name": "Sex"}, "userId": {"type": "integer", "format": "int64", "x-go-name": "UserId"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "SegmentArticleRecordInfo": {"description": "The response data of segment article record information | SegmentArticleRecord信息", "type": "object", "properties": {"article": {"$ref": "#/definitions/ArticleInfo"}, "articleId": {"description": "文章ID", "type": "integer", "format": "uint64", "x-go-name": "ArticleId"}, "createTime": {"description": "创建时间", "type": "integer", "format": "int64", "x-go-name": "CreateTime"}, "id": {"type": "integer", "format": "uint64", "x-go-name": "Id"}, "segmentId": {"description": "栏目ID", "type": "integer", "format": "uint64", "x-go-name": "SegmentId"}, "type": {"description": "类型", "type": "integer", "format": "int32", "x-go-name": "Type"}, "updateTime": {"description": "更新时间", "type": "integer", "format": "int64", "x-go-name": "UpdateTime"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "SegmentArticleRecordInfoResp": {"description": "SegmentArticleRecord information response | SegmentArticleRecord信息返回体", "type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/SegmentArticleRecordInfo"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "SegmentArticleRecordListInfo": {"description": "SegmentArticleRecord list data | SegmentArticleRecord列表数据", "type": "object", "properties": {"data": {"description": "The API list data | SegmentArticleRecord列表数据", "type": "array", "items": {"$ref": "#/definitions/SegmentArticleRecordInfo"}, "x-go-name": "Data"}, "total": {"description": "The total number of data | 数据总数", "type": "integer", "format": "uint64", "x-go-name": "Total"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "SegmentArticleRecordListReq": {"description": "Get segment article record list request params | SegmentArticleRecord列表请求参数", "type": "object", "required": ["page", "pageSize"], "properties": {"page": {"description": "Page number | 第几页", "type": "integer", "format": "uint64", "minimum": 0, "x-go-name": "Page"}, "pageSize": {"description": "Page size | 单页数据行数", "type": "integer", "format": "uint64", "maximum": 100000, "x-go-name": "PageSize"}, "segmentId": {"description": "SegmentId", "type": "integer", "format": "uint64", "x-go-name": "SegmentId"}, "type": {"description": "Type", "type": "integer", "format": "int32", "x-go-name": "Type"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "SegmentArticleRecordListResp": {"description": "The response data of segment article record list | SegmentArticleRecord列表数据", "type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/SegmentArticleRecordListInfo"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "SegmentInfo": {"description": "The response data of segment information | Segment信息", "type": "object", "properties": {"createTime": {"description": "创建时间", "type": "integer", "format": "int64", "x-go-name": "CreateTime"}, "id": {"type": "integer", "format": "uint64", "x-go-name": "Id"}, "name": {"description": "栏目名称", "type": "string", "x-go-name": "Name"}, "sequence": {"description": "排序", "type": "integer", "format": "int32", "x-go-name": "Sequence"}, "updateTime": {"description": "更新时间", "type": "integer", "format": "int64", "x-go-name": "UpdateTime"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "SegmentInfoResp": {"description": "Segment information response | Segment信息返回体", "type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/SegmentInfo"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "SegmentListInfo": {"description": "Segment list data | Segment列表数据", "type": "object", "properties": {"data": {"description": "The API list data | Segment列表数据", "type": "array", "items": {"$ref": "#/definitions/SegmentInfo"}, "x-go-name": "Data"}, "total": {"description": "The total number of data | 数据总数", "type": "integer", "format": "uint64", "x-go-name": "Total"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "SegmentListReq": {"description": "Get segment list request params | Segment列表请求参数", "type": "object", "required": ["page", "pageSize"], "properties": {"name": {"description": "Name", "type": "string", "x-go-name": "Name"}, "page": {"description": "Page number | 第几页", "type": "integer", "format": "uint64", "minimum": 0, "x-go-name": "Page"}, "pageSize": {"description": "Page size | 单页数据行数", "type": "integer", "format": "uint64", "maximum": 100000, "x-go-name": "PageSize"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "SegmentListResp": {"description": "The response data of segment list | Segment列表数据", "type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/SegmentListInfo"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "SendEmailCodeData": {"type": "object", "properties": {"status": {"type": "boolean", "x-go-name": "Status"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "SendEmailCodeReq": {"type": "object", "properties": {"email": {"type": "string", "x-go-name": "Email"}, "productId": {"type": "integer", "format": "uint64", "x-go-name": "ProductId"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "SendEmailCodeResp": {"type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/SendEmailCodeData"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ServerStatusReq": {"type": "object", "properties": {"appCode": {"type": "string", "x-go-name": "AppCode"}, "deviceId": {"type": "string", "x-go-name": "DeviceId"}, "deviceType": {"type": "string", "x-go-name": "DeviceType"}, "machineCode": {"type": "string", "x-go-name": "MachineCode"}, "nonce": {"type": "string", "x-go-name": "<PERSON><PERSON>"}, "signature": {"type": "string", "x-go-name": "Signature"}, "timestamp": {"type": "integer", "format": "int64", "x-go-name": "Timestamp"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ServerStatusResp": {"type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/ServerStatusRespData"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ServerStatusRespData": {"type": "object", "properties": {"nonce": {"description": "Nonce | 随机字符串", "type": "string", "x-go-name": "<PERSON><PERSON>"}, "serverTime": {"description": "Server Time | 服务器时间戳", "type": "integer", "format": "int64", "x-go-name": "ServerTime"}, "signature": {"description": "Signature | 签名", "type": "string", "x-go-name": "Signature"}, "status": {"description": "服务器状态", "type": "integer", "format": "int32", "x-go-name": "Status"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ServerTimeResp": {"type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/ServerTimeRespData"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ServerTimeRespData": {"type": "object", "properties": {"serverTime": {"description": "Server Time | 服务器时间戳", "type": "integer", "format": "int64", "x-go-name": "ServerTime"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "SetAppletUserInfoResp": {"type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/SetAppletUserInfoRespData"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "SetAppletUserInfoRespData": {"type": "object", "properties": {"info": {"type": "string", "x-go-name": "Info"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "SetAvatarReq": {"type": "object", "properties": {"avatar": {"type": "string", "x-go-name": "Avatar"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "SetAvatarResp": {"type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/SetAvatarRespData"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "SetAvatarRespData": {"type": "object", "properties": {"avatar": {"type": "string", "x-go-name": "Avatar"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "SetBackgroundImageReq": {"type": "object", "properties": {"backgroundImage": {"type": "string", "x-go-name": "BackgroundImage"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "SetBackgroundImageResp": {"type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/SetBackgroundImageRespData"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "SetBackgroundImageRespData": {"type": "object", "properties": {"backgroundImage": {"type": "string", "x-go-name": "BackgroundImage"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "SetNicknameReq": {"type": "object", "properties": {"nickname": {"type": "string", "x-go-name": "Nickname"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "SetNicknameResp": {"type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/SetNicknameRespData"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "SetNicknameRespData": {"type": "object", "properties": {"nickname": {"type": "string", "x-go-name": "Nickname"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "SetPasswordReq": {"type": "object", "properties": {"password": {"type": "string", "x-go-name": "Password"}, "rePassword": {"type": "string", "x-go-name": "RePassword"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "SetPasswordResp": {"type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/AuthTokenData"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "SetUserDescriptionReq": {"type": "object", "properties": {"description": {"type": "string", "x-go-name": "Description"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "SetUserDescriptionResp": {"type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/SetUserDescriptionRespData"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "SetUserDescriptionRespData": {"type": "object", "properties": {"description": {"type": "string", "x-go-name": "Description"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "SetUserEmailReq": {"type": "object", "properties": {"code": {"type": "string", "x-go-name": "Code"}, "email": {"type": "string", "x-go-name": "Email"}, "userId": {"type": "integer", "format": "int64", "x-go-name": "UserId"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "SetUserEmailResp": {"type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/SetUserEmailRespData"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "SetUserEmailRespData": {"type": "object", "properties": {"email": {"type": "string", "x-go-name": "Email"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "SetUserHomePageReq": {"type": "object", "properties": {"homePage": {"type": "string", "x-go-name": "HomePage"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "SetUserHomePageResp": {"type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/SetUserHomePageRespData"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "SetUserHomePageRespData": {"type": "object", "properties": {"homePage": {"type": "string", "x-go-name": "HomePage"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "SetUserInfoReq": {"type": "object", "properties": {"info": {"type": "string", "x-go-name": "Info"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "SetUserSexReq": {"type": "object", "properties": {"sex": {"type": "integer", "format": "int64", "x-go-name": "Sex"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "SetUserSexResp": {"type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/SetUserSexRespData"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "SetUserSexRespData": {"type": "object", "properties": {"sex": {"type": "integer", "format": "int64", "x-go-name": "Sex"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "StreetInfo": {"description": "The response data of street information | Street信息", "type": "object", "properties": {"areasCode": {"description": "AreasCode", "type": "string", "x-go-name": "AreasCode"}, "code": {"description": "Code", "type": "string", "x-go-name": "Code"}, "geocode": {"description": "Geocode", "type": "string", "x-go-name": "Geocode"}, "id": {"type": "integer", "format": "uint64", "x-go-name": "Id"}, "latitude": {"description": "Latitude", "type": "string", "x-go-name": "Latitude"}, "level": {"description": "Level", "type": "integer", "format": "int32", "x-go-name": "Level"}, "longitude": {"description": "Longitude", "type": "string", "x-go-name": "Longitude"}, "name": {"description": "Name", "type": "string", "x-go-name": "Name"}, "parentCode": {"description": "ParentCode", "type": "string", "x-go-name": "ParentCode"}, "provinceCode": {"description": "ProvinceCode", "type": "string", "x-go-name": "ProvinceCode"}, "sort": {"description": "Sort", "type": "integer", "format": "int32", "x-go-name": "Sort"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "StreetInfoResp": {"description": "Street information response | Street信息返回体", "type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/StreetInfo"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "StreetListInfo": {"description": "Street list data | Street列表数据", "type": "object", "properties": {"data": {"description": "The API list data | Street列表数据", "type": "array", "items": {"$ref": "#/definitions/StreetInfo"}, "x-go-name": "Data"}, "total": {"description": "The total number of data | 数据总数", "type": "integer", "format": "uint64", "x-go-name": "Total"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "StreetListReq": {"description": "Get street list request params | Street列表请求参数", "type": "object", "required": ["page", "pageSize"], "properties": {"areasCode": {"description": "AreasCode", "type": "string", "x-go-name": "AreasCode"}, "code": {"description": "Code", "type": "string", "x-go-name": "Code"}, "geocode": {"description": "Geocode", "type": "string", "x-go-name": "Geocode"}, "latitude": {"description": "Latitude", "type": "string", "x-go-name": "Latitude"}, "longitude": {"description": "Longitude", "type": "string", "x-go-name": "Longitude"}, "name": {"description": "Name", "type": "string", "x-go-name": "Name"}, "page": {"description": "Page number | 第几页", "type": "integer", "format": "uint64", "minimum": 0, "x-go-name": "Page"}, "pageSize": {"description": "Page size | 单页数据行数", "type": "integer", "format": "uint64", "maximum": 100000, "x-go-name": "PageSize"}, "parentCode": {"description": "ParentCode", "type": "string", "x-go-name": "ParentCode"}, "provinceCode": {"description": "ProvinceCode", "type": "string", "x-go-name": "ProvinceCode"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "StreetListResp": {"description": "The response data of street list | Street列表数据", "type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/StreetListInfo"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "TagFollowInfo": {"description": "The response data of tag follow information | TagFollow信息", "type": "object", "properties": {"createTime": {"description": "创建时间", "type": "integer", "format": "int64", "x-go-name": "CreateTime"}, "id": {"type": "integer", "format": "uint64", "x-go-name": "Id"}, "status": {"description": "状态（0、退出 1、加入）", "type": "integer", "format": "uint32", "x-go-name": "Status"}, "tagId": {"description": "圈子ID", "type": "integer", "format": "uint64", "x-go-name": "TagId"}, "updateTime": {"description": "最后修改时间", "type": "integer", "format": "int64", "x-go-name": "UpdateTime"}, "userId": {"description": "用户ID", "type": "integer", "format": "uint64", "x-go-name": "UserId"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "TagFollowInfoResp": {"description": "TagFollow information response | TagFollow信息返回体", "type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/IDReq"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "TagFollowListInfo": {"description": "TagFollow list data | TagFollow列表数据", "type": "object", "properties": {"data": {"description": "The API list data | TagFollow列表数据", "type": "array", "items": {"$ref": "#/definitions/TagFollowInfo"}, "x-go-name": "Data"}, "total": {"description": "The total number of data | 数据总数", "type": "integer", "format": "uint64", "x-go-name": "Total"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "TagFollowListReq": {"description": "Get tag follow list request params | TagFollow列表请求参数", "type": "object", "required": ["page", "pageSize"], "properties": {"page": {"description": "Page number | 第几页", "type": "integer", "format": "uint64", "minimum": 0, "x-go-name": "Page"}, "pageSize": {"description": "Page size | 单页数据行数", "type": "integer", "format": "uint64", "maximum": 100000, "x-go-name": "PageSize"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "TagFollowListResp": {"description": "The response data of tag follow list | TagFollow列表数据", "type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/TagFollowListInfo"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "TagInfo": {"description": "The response data of tag information | Tag信息", "type": "object", "properties": {"coverUrl": {"description": "圈子背景图", "type": "string", "x-go-name": "CoverUrl"}, "createTime": {"description": "创建时间", "type": "integer", "format": "int64", "x-go-name": "CreateTime"}, "desc": {"description": "标签描述", "type": "string", "x-go-name": "Desc"}, "followNum": {"description": "关注数", "type": "integer", "format": "int64", "x-go-name": "FollowNum"}, "iconUrl": {"description": "圈子图标", "type": "string", "x-go-name": "IconUrl"}, "id": {"type": "integer", "format": "uint64", "x-go-name": "Id"}, "isFollow": {"type": "boolean", "x-go-name": "IsFollow"}, "isMember": {"type": "boolean", "x-go-name": "IsMember"}, "memberNum": {"description": "参与数", "type": "integer", "format": "int64", "x-go-name": "MemberNum"}, "name": {"description": "标签名称", "type": "string", "x-go-name": "Name"}, "topicNum": {"description": "主题数", "type": "integer", "format": "int64", "x-go-name": "TopicNum"}, "type": {"description": "话题类型（1、普通 2、官方）", "type": "integer", "format": "int32", "x-go-name": "Type"}, "updateTime": {"description": "最后修改时间", "type": "integer", "format": "int64", "x-go-name": "UpdateTime"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "TagInfoResp": {"description": "Tag information response | Tag信息返回体", "type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/TagInfo"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "TagListInfo": {"description": "Tag list data | Tag列表数据", "type": "object", "properties": {"data": {"description": "The API list data | Tag列表数据", "type": "array", "items": {"$ref": "#/definitions/TagInfo"}, "x-go-name": "Data"}, "exist": {"description": "tagName是否存", "type": "boolean", "x-go-name": "Exist"}, "total": {"description": "The total number of data | 数据总数", "type": "integer", "format": "uint64", "x-go-name": "Total"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "TagListReq": {"description": "Get tag list request params | Tag列表请求参数", "type": "object", "required": ["page", "pageSize"], "properties": {"desc": {"description": "Desc", "type": "string", "x-go-name": "Desc"}, "iconUrl": {"description": "IconUrl", "type": "string", "x-go-name": "IconUrl"}, "name": {"description": "Name", "type": "string", "x-go-name": "Name"}, "page": {"description": "Page number | 第几页", "type": "integer", "format": "uint64", "minimum": 0, "x-go-name": "Page"}, "pageSize": {"description": "Page size | 单页数据行数", "type": "integer", "format": "uint64", "maximum": 100000, "x-go-name": "PageSize"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "TagListResp": {"description": "The response data of tag list | Tag列表数据", "type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/TagListInfo"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "TecentCloudUploadPostPolicyData": {"type": "object", "properties": {"cosHost": {"type": "string", "x-go-name": "CosHost"}, "cosKey": {"type": "string", "x-go-name": "CosKey"}, "customDomain": {"type": "string", "x-go-name": "CustomDomain"}, "fileURL": {"type": "string", "x-go-name": "FileURL"}, "policy": {"type": "string", "x-go-name": "Policy"}, "qAk": {"type": "string", "x-go-name": "QAk"}, "qKeyTime": {"type": "string", "x-go-name": "QKeyTime"}, "qSignAlgorithm": {"type": "string", "x-go-name": "QSignAlgorithm"}, "qSignature": {"type": "string", "x-go-name": "QSignature"}, "securityToken": {"type": "string", "x-go-name": "SecurityToken"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "TecentCloudUploadPostPolicyReq": {"type": "object", "properties": {"fileExt": {"type": "string", "x-go-name": "FileExt"}, "fileType": {"type": "string", "x-go-name": "FileType"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "TecentCloudUploadPostPolicyResp": {"type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/TecentCloudUploadPostPolicyData"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "TecentCloudUploadTokenData": {"type": "object", "properties": {"allowPrefix": {"type": "string", "x-go-name": "AllowPrefix"}, "bucket": {"type": "string", "x-go-name": "Bucket"}, "cosHost": {"type": "string", "x-go-name": "CosHost"}, "customDomain": {"type": "string", "x-go-name": "CustomDomain"}, "expireDuration": {"type": "integer", "format": "int64", "x-go-name": "ExpireDuration"}, "expiredTime": {"type": "integer", "format": "int64", "x-go-name": "ExpiredTime"}, "region": {"type": "string", "x-go-name": "Region"}, "sessionToken": {"type": "string", "x-go-name": "SessionToken"}, "startTime": {"type": "integer", "format": "int64", "x-go-name": "StartTime"}, "tmpSecretId": {"type": "string", "x-go-name": "TmpSecretID"}, "tmpSecretKey": {"type": "string", "x-go-name": "TmpSecretKey"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "TecentCloudUploadTokenReq": {"type": "object", "properties": {"fileType": {"type": "string", "x-go-name": "FileType"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "TecentCloudUploadTokenResp": {"type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/TecentCloudUploadTokenData"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "TerroristInfo": {"type": "object", "properties": {"count": {"type": "integer", "format": "int64", "x-go-name": "Count"}, "hit_flag": {"type": "integer", "format": "int64", "x-go-name": "HitFlag"}, "label": {"type": "string", "x-go-name": "Label"}, "score": {"type": "integer", "format": "int64", "x-go-name": "Score"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ThumbupReq": {"type": "object", "required": ["likeType"], "properties": {"bizId": {"type": "string", "x-go-name": "BizId"}, "likeType": {"type": "integer", "format": "int64", "x-go-name": "LikeType"}, "objId": {"type": "integer", "format": "int64", "x-go-name": "ObjId"}, "userId": {"type": "integer", "format": "int64", "x-go-name": "UserId"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ThumbupResp": {"type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/ThumbupRespData"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ThumbupRespData": {"type": "object", "properties": {"bizId": {"type": "string", "x-go-name": "BizId"}, "dislikeNum": {"type": "integer", "format": "int64", "x-go-name": "DislikeNum"}, "likeNum": {"type": "integer", "format": "int64", "x-go-name": "LikeNum"}, "objId": {"type": "integer", "format": "int64", "x-go-name": "ObjId"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "UUIDPathReq": {"description": "Basic UUID request in path | 基础UUID地址参数请求", "type": "object", "required": ["Id"], "properties": {"Id": {"description": "ID", "type": "string"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "UUIDReq": {"description": "Basic UUID request | 基础UUID参数请求", "type": "object", "required": ["id"], "properties": {"id": {"description": "ID", "type": "string", "maxLength": 36, "minLength": 36, "x-go-name": "Id"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "UUIDsReq": {"description": "Basic UUID array request | 基础UUID数组参数请求", "type": "object", "required": ["ids"], "properties": {"ids": {"description": "Ids", "type": "array", "items": {"type": "string"}, "x-go-name": "Ids"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "UnFollowReq": {"type": "object", "properties": {"followedUserId": {"type": "integer", "format": "int64", "x-go-name": "FollowedUserId"}, "userId": {"type": "integer", "format": "int64", "x-go-name": "UserId"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "UnFollowResp": {"description": "添加 BaseDataInfo", "type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/FollowRespData"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "UnbindDeviceReq": {"description": "Unbind Device | 解绑设备", "type": "object", "properties": {"appCode": {"type": "string", "x-go-name": "AppCode"}, "deviceId": {"type": "string", "x-go-name": "DeviceId"}, "deviceType": {"type": "string", "x-go-name": "DeviceType"}, "machineCode": {"type": "string", "x-go-name": "MachineCode"}, "nonce": {"type": "string", "x-go-name": "<PERSON><PERSON>"}, "signature": {"type": "string", "x-go-name": "Signature"}, "timestamp": {"type": "integer", "format": "int64", "x-go-name": "Timestamp"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "UpdatePasswordReq": {"type": "object", "properties": {"password": {"type": "string", "x-go-name": "Password"}, "rePassword": {"type": "string", "x-go-name": "RePassword"}, "verifyCode": {"type": "string", "x-go-name": "VerifyCode"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "UpdatePasswordResp": {"type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/AuthTokenData"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "UploadCoverResp": {"type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/FileUrl"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "UploadFiles": {"type": "object", "properties": {"fileUrls": {"type": "array", "items": {"type": "string"}, "x-go-name": "FileUrls"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "UploadFilesResp": {"type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/UploadFilesRespData"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "UploadFilesRespData": {"type": "object", "properties": {"data": {"$ref": "#/definitions/UploadFiles"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "User": {"type": "object", "properties": {"avatar": {"type": "string", "x-go-name": "Avatar"}, "backgroundImage": {"type": "string", "x-go-name": "BackgroundImage"}, "commentCount": {"type": "integer", "format": "int64", "x-go-name": "CommentCount"}, "createTime": {"type": "integer", "format": "int64", "x-go-name": "CreateTime"}, "delState": {"type": "integer", "format": "int64", "x-go-name": "DelState"}, "description": {"type": "string", "x-go-name": "Description"}, "email": {"type": "string", "x-go-name": "Email"}, "fansCount": {"type": "integer", "format": "int64", "x-go-name": "FansCount"}, "followCount": {"type": "integer", "format": "int64", "x-go-name": "FollowCount"}, "forbiddenEndTime": {"type": "integer", "format": "int64", "x-go-name": "ForbiddenEndTime"}, "homePage": {"type": "string", "x-go-name": "HomePage"}, "id": {"type": "integer", "format": "int64", "x-go-name": "UserId"}, "idCard": {"type": "string", "x-go-name": "IdCard"}, "info": {"type": "string", "x-go-name": "Info"}, "likeCount": {"type": "integer", "format": "int64", "x-go-name": "LikeCount"}, "mobile": {"type": "string", "x-go-name": "Mobile"}, "mutedEndTime": {"type": "integer", "format": "int64", "x-go-name": "MutedEndTime"}, "nickname": {"type": "string", "x-go-name": "Nickname"}, "password": {"type": "string", "x-go-name": "Password"}, "realName": {"type": "string", "x-go-name": "RealName"}, "realNameVerified": {"type": "integer", "format": "int32", "x-go-name": "RealNameVerified"}, "roles": {"type": "string", "x-go-name": "Roles"}, "score": {"type": "integer", "format": "int64", "x-go-name": "Score"}, "sex": {"type": "integer", "format": "int64", "x-go-name": "Sex"}, "topicCount": {"type": "integer", "format": "int64", "x-go-name": "TopicCount"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "UserAddresInfo": {"description": "The response data of user addres information | UserAddres信息", "type": "object", "properties": {"address": {"description": "收货地址", "type": "string", "x-go-name": "Address"}, "area": {"description": "县/区", "type": "string", "x-go-name": "Area"}, "city": {"description": "市", "type": "string", "x-go-name": "City"}, "consignee": {"description": "收货人", "type": "string", "x-go-name": "Consignee"}, "createdAt": {"description": "Create date | 创建日期", "type": "integer", "format": "int64", "x-go-name": "CreatedAt"}, "default": {"description": "是否默认", "type": "integer", "format": "int32", "x-go-name": "<PERSON><PERSON><PERSON>"}, "gender": {"description": "性别", "type": "integer", "format": "int32", "x-go-name": "Gender"}, "id": {"description": "ID", "type": "integer", "format": "uint64", "x-go-name": "Id"}, "mobile": {"description": "手机号码", "type": "string", "x-go-name": "Mobile"}, "province": {"description": "省", "type": "string", "x-go-name": "Province"}, "recordAddress": {"description": "详细地址", "type": "string", "x-go-name": "RecordAddress"}, "street": {"description": "街道", "type": "string", "x-go-name": "Street"}, "updatedAt": {"description": "Update date | 更新日期", "type": "integer", "format": "int64", "x-go-name": "UpdatedAt"}, "userId": {"description": "用户id", "type": "integer", "format": "int64", "x-go-name": "UserId"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "UserAddresInfoResp": {"description": "UserAddres information response | UserAddres信息返回体", "type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/UserAddresInfo"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "UserAddresListInfo": {"description": "UserAddres list data | UserAddres列表数据", "type": "object", "properties": {"data": {"description": "The API list data | UserAddres列表数据", "type": "array", "items": {"$ref": "#/definitions/UserAddresInfo"}, "x-go-name": "Data"}, "total": {"description": "The total number of data | 数据总数", "type": "integer", "format": "uint64", "x-go-name": "Total"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "UserAddresListReq": {"description": "Get user addres list request params | UserAddres列表请求参数", "type": "object", "required": ["page", "pageSize"], "properties": {"address": {"description": "Address", "type": "string", "x-go-name": "Address"}, "consignee": {"description": "Consignee", "type": "string", "x-go-name": "Consignee"}, "mobile": {"description": "Mobile", "type": "string", "x-go-name": "Mobile"}, "page": {"description": "Page number | 第几页", "type": "integer", "format": "uint64", "minimum": 0, "x-go-name": "Page"}, "pageSize": {"description": "Page size | 单页数据行数", "type": "integer", "format": "uint64", "maximum": 100000, "x-go-name": "PageSize"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "UserAddresListResp": {"description": "The response data of user addres list | UserAddres列表数据", "type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/UserAddresListInfo"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "UserAgreementInfo": {"description": "The response data of user agreement information | UserAgreement信息", "type": "object", "properties": {"content": {"description": "协议正文", "type": "string", "x-go-name": "Content"}, "createdAt": {"description": "创建时间", "type": "integer", "format": "int64", "x-go-name": "CreatedAt"}, "id": {"type": "integer", "format": "uint64", "x-go-name": "Id"}, "key": {"description": "唯一标识符", "type": "string", "x-go-name": "Key"}, "status": {"description": "状态 0 停止1开启", "type": "integer", "format": "uint32", "x-go-name": "Status"}, "title": {"description": "协议标题", "type": "string", "x-go-name": "Title"}, "updatedAt": {"description": "修改时间", "type": "integer", "format": "int64", "x-go-name": "UpdatedAt"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "UserAgreementInfoResp": {"description": "The user agreement information response | UserAgreement信息返回体", "type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/UserAgreementInfo"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "UserAgreementListInfo": {"description": "The user agreement list data | UserAgreement信息列表数据", "type": "object", "properties": {"data": {"description": "The user agreement list data | UserAgreement信息列表数据", "type": "array", "items": {"$ref": "#/definitions/UserAgreementInfo"}, "x-go-name": "Data"}, "total": {"description": "The total number of data | 数据总数", "type": "integer", "format": "uint64", "x-go-name": "Total"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "UserAgreementListReq": {"description": "Get user agreement list request params | UserAgreement列表请求参数", "type": "object", "required": ["page", "pageSize"], "properties": {"content": {"description": "Content", "type": "string", "x-go-name": "Content"}, "key": {"description": "Key", "type": "string", "x-go-name": "Key"}, "page": {"description": "Page number | 第几页", "type": "integer", "format": "uint64", "minimum": 0, "x-go-name": "Page"}, "pageSize": {"description": "Page size | 单页数据行数", "type": "integer", "format": "uint64", "maximum": 100000, "x-go-name": "PageSize"}, "status": {"description": "Status", "type": "integer", "format": "uint32", "x-go-name": "Status"}, "title": {"description": "Title", "type": "string", "x-go-name": "Title"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "UserAgreementListResp": {"description": "The response data of user agreement list | UserAgreement信息列表数据", "type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/UserAgreementListInfo"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "UserArticleListsReq": {"type": "object", "required": ["userId"], "properties": {"articleId": {"type": "integer", "format": "int64", "minimum": 0, "x-go-name": "ArticleId"}, "cursor": {"type": "integer", "format": "int64", "minimum": 0, "x-go-name": "<PERSON><PERSON><PERSON>"}, "pageSize": {"type": "integer", "format": "int64", "minimum": 0, "x-go-name": "PageSize"}, "sortType": {"type": "integer", "format": "int32", "x-go-name": "SortType"}, "userId": {"type": "integer", "format": "int64", "x-go-name": "UserId"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "UserInfo": {"description": "The response data of user information | User信息", "type": "object", "properties": {"avatar": {"description": "Avatar", "type": "string", "x-go-name": "Avatar"}, "backgroundImage": {"description": "BackgroundImage", "type": "string", "x-go-name": "BackgroundImage"}, "commentCount": {"description": "CommentCount", "type": "integer", "format": "int64", "x-go-name": "CommentCount"}, "createTime": {"description": "CreateTime", "type": "integer", "format": "int64", "x-go-name": "CreateTime"}, "delState": {"description": "DelState", "type": "integer", "format": "int32", "x-go-name": "DelState"}, "deleteTime": {"description": "DeleteTime", "type": "integer", "format": "int64", "x-go-name": "DeleteTime"}, "description": {"description": "Description", "type": "string", "x-go-name": "Description"}, "fansCount": {"description": "FansCount", "type": "integer", "format": "int64", "x-go-name": "FansCount"}, "followCount": {"description": "FollowCount", "type": "integer", "format": "int64", "x-go-name": "FollowCount"}, "forbiddenEndTime": {"description": "禁言结束时间", "type": "integer", "format": "int64", "x-go-name": "ForbiddenEndTime"}, "homePage": {"description": "HomePage", "type": "string", "x-go-name": "HomePage"}, "id": {"type": "integer", "format": "uint64", "x-go-name": "Id"}, "info": {"description": "Info", "type": "string", "x-go-name": "Info"}, "likeCount": {"description": "LikeCount", "type": "integer", "format": "int64", "x-go-name": "LikeCount"}, "mobile": {"description": "Mobile", "type": "string", "x-go-name": "Mobile"}, "nickname": {"description": "Nickname", "type": "string", "x-go-name": "Nickname"}, "password": {"description": "Password", "type": "string", "x-go-name": "Password"}, "roles": {"description": "Roles", "type": "string", "x-go-name": "Roles"}, "score": {"description": "Score", "type": "integer", "format": "int64", "x-go-name": "Score"}, "sex": {"description": "性别 0:男 1:女", "type": "integer", "format": "int32", "x-go-name": "Sex"}, "topicCount": {"description": "TopicCount", "type": "integer", "format": "int64", "x-go-name": "TopicCount"}, "updateTime": {"description": "UpdateTime", "type": "integer", "format": "int64", "x-go-name": "UpdateTime"}, "version": {"description": "版本号", "type": "integer", "format": "int64", "x-go-name": "Version"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "UserInfoByMobileReq": {"type": "object", "properties": {"mobile": {"type": "string", "x-go-name": "Mobile"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "UserInfoByNameReq": {"type": "object", "properties": {"nickname": {"type": "string", "x-go-name": "Nickname"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "UserInfoByUserIdReq": {"type": "object", "properties": {"userId": {"type": "integer", "format": "int64", "x-go-name": "UserId"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "UserInfoByUserIdResp": {"type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/UserInfoByUserIdRespData"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "UserInfoByUserIdRespData": {"type": "object", "properties": {"isFans": {"type": "boolean", "x-go-name": "IsFans"}, "isFollow": {"type": "boolean", "x-go-name": "IsFollow"}, "userInfo": {"$ref": "#/definitions/User"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "UserInfoReq": {"type": "object", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "UserInfoResp": {"description": "User information response | User信息返回体", "type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/UserInfo"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "UserListInfo": {"description": "User list data | User列表数据", "type": "object", "properties": {"data": {"description": "The API list data | User列表数据", "type": "array", "items": {"$ref": "#/definitions/UserInfo"}, "x-go-name": "Data"}, "total": {"description": "The total number of data | 数据总数", "type": "integer", "format": "uint64", "x-go-name": "Total"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "UserListReq": {"description": "Get user list request params | User列表请求参数", "type": "object", "required": ["page", "pageSize"], "properties": {"address": {"description": "Address", "type": "string", "x-go-name": "Address"}, "consignee": {"description": "Consignee", "type": "string", "x-go-name": "Consignee"}, "mobile": {"description": "Mobile", "type": "string", "x-go-name": "Mobile"}, "nickname": {"description": "Nickname", "type": "string", "x-go-name": "Nickname"}, "page": {"description": "Page number | 第几页", "type": "integer", "format": "uint64", "minimum": 0, "x-go-name": "Page"}, "pageSize": {"description": "Page size | 单页数据行数", "type": "integer", "format": "uint64", "maximum": 100000, "x-go-name": "PageSize"}, "password": {"description": "Password", "type": "string", "x-go-name": "Password"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "UserListResp": {"description": "The response data of user list | User列表数据", "type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/UserListInfo"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "UserReplyListReq": {"type": "object", "properties": {"cursor": {"type": "integer", "format": "int64", "x-go-name": "<PERSON><PERSON><PERSON>"}, "pageSize": {"type": "integer", "format": "int64", "x-go-name": "PageSize"}, "sortType": {"type": "integer", "format": "int64", "x-go-name": "SortType"}, "userId": {"type": "integer", "format": "int64", "x-go-name": "UserId"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "UserThumbup": {"type": "object", "properties": {"likeType": {"type": "integer", "format": "int64", "x-go-name": "LikeType"}, "thumbupTime": {"type": "integer", "format": "int64", "x-go-name": "ThumbupTime"}, "userId": {"type": "integer", "format": "int64", "x-go-name": "UserId"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "VerificationReq": {"type": "object", "properties": {"mobile": {"type": "string", "x-go-name": "Mobile"}, "templateId": {"type": "string", "x-go-name": "TemplateId"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "VerificationResp": {"type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/VerificationRespData"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "VerificationRespData": {"type": "object", "properties": {"id": {"type": "string", "x-go-name": "Id"}, "msg": {"type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "VerifyEmailCodeReq": {"type": "object", "properties": {"code": {"type": "string", "x-go-name": "Code"}, "email": {"type": "string", "x-go-name": "Email"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "VerifyEmailCodeResp": {"type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/SendEmailCodeData"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "VerifyLoginByAuthTypeReq": {"type": "object", "properties": {"authType": {"type": "string", "x-go-name": "AuthType"}, "mobile": {"type": "string", "x-go-name": "Mobile"}, "verifyCode": {"type": "string", "x-go-name": "VerifyCode"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "VerifyLoginReq": {"type": "object", "properties": {"mobile": {"type": "string", "x-go-name": "Mobile"}, "verifyCode": {"type": "string", "x-go-name": "VerifyCode"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "VideoAuditedCallbackData": {"type": "object", "properties": {"ads_info": {"$ref": "#/definitions/AdsInfo"}, "data_id": {"type": "string", "x-go-name": "DataID"}, "event": {"type": "string", "x-go-name": "Event"}, "forbidden_status": {"type": "integer", "format": "int64", "x-go-name": "ForbiddenStatus"}, "politics_info": {"$ref": "#/definitions/PoliticsInfo"}, "porn_info": {"$ref": "#/definitions/PornInfo"}, "result": {"type": "integer", "format": "int64", "x-go-name": "Result"}, "terrorist_info": {"$ref": "#/definitions/TerroristInfo"}, "trace_id": {"type": "string", "x-go-name": "TraceID"}, "url": {"type": "string", "x-go-name": "URL"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "VideoAuditedCallbackReq": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"$ref": "#/definitions/VideoAuditedCallbackData"}, "message": {"type": "string", "x-go-name": "Message"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ViewListReq": {"type": "object", "properties": {"bizId": {"type": "string", "x-go-name": "BizId"}, "cursor": {"type": "integer", "format": "int64", "x-go-name": "<PERSON><PERSON><PERSON>"}, "objId": {"type": "integer", "format": "int64", "x-go-name": "ObjId"}, "pageSize": {"type": "integer", "format": "int64", "x-go-name": "PageSize"}, "sortType": {"type": "integer", "format": "int64", "x-go-name": "SortType"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ViewListResp": {"type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/ViewListRespData"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ViewListRespData": {"type": "object", "properties": {"cursor": {"type": "integer", "format": "int64", "x-go-name": "<PERSON><PERSON><PERSON>"}, "isEnd": {"type": "boolean", "x-go-name": "IsEnd"}, "items": {"type": "array", "items": {"$ref": "#/definitions/ViewRecordItem"}, "x-go-name": "Items"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ViewRecordItem": {"type": "object", "properties": {"bizId": {"type": "string", "x-go-name": "BizId"}, "createTime": {"type": "integer", "format": "int64", "x-go-name": "CreateTime"}, "id": {"type": "integer", "format": "int64", "x-go-name": "Id"}, "objId": {"type": "integer", "format": "int64", "x-go-name": "ObjId"}, "updateTime": {"type": "integer", "format": "int64", "x-go-name": "UpdateTime"}, "userId": {"type": "integer", "format": "int64", "x-go-name": "UserId"}, "viewUserInfo": {"$ref": "#/definitions/ViewUserInfo"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ViewUserInfo": {"type": "object", "properties": {"avatar": {"type": "string", "x-go-name": "Avatar"}, "backgroundImage": {"type": "string", "x-go-name": "BackgroundImage"}, "description": {"type": "string", "x-go-name": "Description"}, "homePage": {"type": "string", "x-go-name": "HomePage"}, "id": {"type": "integer", "format": "int64", "x-go-name": "Id"}, "info": {"type": "string", "x-go-name": "Info"}, "mobile": {"type": "string", "x-go-name": "Mobile"}, "nickname": {"type": "string", "x-go-name": "Nickname"}, "sex": {"type": "integer", "format": "int64", "x-go-name": "Sex"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "WXAuthReq": {"type": "object", "properties": {"code": {"type": "string", "x-go-name": "Code"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "WXAuthResp": {"type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/WXAuthRespData"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "WXAuthRespData": {"type": "object", "properties": {"accessExpire": {"type": "integer", "format": "int64", "x-go-name": "AccessExpire"}, "accessToken": {"type": "string", "x-go-name": "AccessToken"}, "authKey": {"type": "string", "x-go-name": "<PERSON><PERSON><PERSON><PERSON>"}, "authType": {"type": "string", "x-go-name": "AuthType"}, "avatar": {"type": "string", "x-go-name": "Avatar"}, "nickname": {"type": "string", "x-go-name": "<PERSON><PERSON><PERSON>"}, "refreshAfter": {"type": "integer", "format": "int64", "x-go-name": "RefreshAfter"}, "registered": {"type": "boolean", "x-go-name": "Registered"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "WXMiniAuthReq": {"type": "object", "properties": {"code": {"type": "string", "x-go-name": "Code"}, "encryptedData": {"type": "string", "x-go-name": "EncryptedData"}, "iv": {"type": "string", "x-go-name": "IV"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "WXMiniAuthResp": {"type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/AuthTokenData"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}}, "securityDefinitions": {"Token": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header"}}, "security": [{"Token": ["[]"]}]}