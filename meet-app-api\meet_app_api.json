{"consumes": ["application/json"], "produces": ["application/json"], "schemes": ["http", "https"], "swagger": "2.0", "info": {"description": "Description: meet_app_api service", "title": "meet_app_api", "version": "0.0.1"}, "host": "localhost:40002", "basePath": "/", "paths": {"/app/v1/appVersion/compareVersion": {"post": {"description": "Compare Version | 对比版本是否需要更新", "tags": ["appversion"], "summary": "Compare Version | 对比版本是否需要更新", "operationId": "CompareVersion", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/AppVersionCompareReq"}}], "responses": {"200": {"description": "AppVersionInfoResp", "schema": {"$ref": "#/definitions/AppVersionInfoResp"}}}}}, "/app/v1/area": {"post": {"description": "Get Area By ID | 通过ID获取区", "tags": ["area"], "summary": "Get Area By ID | 通过ID获取区", "operationId": "GetAreaById", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDReq"}}], "responses": {"200": {"description": "AreaInfoResp", "schema": {"$ref": "#/definitions/AreaInfoResp"}}}}}, "/app/v1/area/list": {"post": {"description": "Get Area List | 获取区列表", "tags": ["area"], "summary": "Get Area List | 获取区列表", "operationId": "GetAreaList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/AreaListReq"}}], "responses": {"200": {"description": "AreaListResp", "schema": {"$ref": "#/definitions/AreaListResp"}}}}}, "/app/v1/article": {"post": {"description": "Get Article By ID | 通过ID获取文章", "tags": ["article"], "summary": "Get Article By ID | 通过ID获取文章", "operationId": "GetArticleById", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDReq"}}], "responses": {"200": {"description": "ArticleInfoResp", "schema": {"$ref": "#/definitions/ArticleInfoResp"}}}}}, "/app/v1/article/list": {"post": {"description": "Get Article List | 获取文章列表", "tags": ["article"], "summary": "Get Article List | 获取文章列表", "operationId": "GetArticleList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/ArticleListReq"}}], "responses": {"200": {"description": "ArticleListResp", "schema": {"$ref": "#/definitions/ArticleListResp"}}}}}, "/app/v1/banner": {"post": {"description": "Get Banner By ID | 通过ID获取轮播图", "tags": ["banner"], "summary": "Get Banner By ID | 通过ID获取轮播图", "operationId": "GetBannerById", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDReq"}}], "responses": {"200": {"description": "BannerInfoResp", "schema": {"$ref": "#/definitions/BannerInfoResp"}}}}}, "/app/v1/banner/list": {"post": {"description": "Get Banner List | 获取轮播图列表", "tags": ["banner"], "summary": "Get Banner List | 获取轮播图列表", "operationId": "GetBannerList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/BannerListReq"}}], "responses": {"200": {"description": "BannerListResp", "schema": {"$ref": "#/definitions/BannerListResp"}}}}}, "/app/v1/city": {"post": {"description": "Get City By ID | 通过ID获取城市", "tags": ["city"], "summary": "Get City By ID | 通过ID获取城市", "operationId": "GetCityById", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDReq"}}], "responses": {"200": {"description": "CityInfoResp", "schema": {"$ref": "#/definitions/CityInfoResp"}}}}}, "/app/v1/city/list": {"post": {"description": "Get City List | 获取城市列表", "tags": ["city"], "summary": "Get City List | 获取城市列表", "operationId": "GetCityList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/CityListReq"}}], "responses": {"200": {"description": "CityListResp", "schema": {"$ref": "#/definitions/CityListResp"}}}}}, "/app/v1/configuration": {"get": {"description": "Get public system configuration | 获取公开系统参数", "tags": ["publicapi"], "summary": "Get public system configuration | 获取公开系统参数", "operationId": "GetPublicSystemConfiguration", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/ConfigurationtReq"}}], "responses": {"200": {"description": "ConfigurationResp", "schema": {"$ref": "#/definitions/ConfigurationResp"}}}}}, "/app/v1/group": {"post": {"description": "Get Group By ID | 获取圈子信息", "tags": ["group"], "summary": "Get Group By ID | 获取圈子信息", "operationId": "GetGroupById", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDReq"}}], "responses": {"200": {"description": "GroupInfoResp", "schema": {"$ref": "#/definitions/GroupInfoResp"}}}}}, "/app/v1/group/list": {"post": {"description": "Get Group List | 获取圈子列表", "tags": ["group"], "summary": "Get Group List | 获取圈子列表", "operationId": "GetGroupList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/GroupListReq"}}], "responses": {"200": {"description": "GroupListResp", "schema": {"$ref": "#/definitions/GroupListResp"}}}}}, "/app/v1/group/userFollowList": {"post": {"description": "Get User Follow List | 获取加入的圈子列表", "tags": ["group"], "summary": "Get User Follow List | 获取加入的圈子列表", "operationId": "GetUserFollowList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/GroupListReq"}}], "responses": {"200": {"description": "GroupListResp", "schema": {"$ref": "#/definitions/GroupListResp"}}}}}, "/app/v1/groupFollow/create": {"post": {"description": "Create Group Follow | 加入圈子", "tags": ["groupfollow"], "summary": "Create Group Follow | 加入圈子", "operationId": "CreateGroupFollow", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/GroupFollowInfo"}}], "responses": {"200": {"description": "GroupFollowInfoResp", "schema": {"$ref": "#/definitions/GroupFollowInfoResp"}}}}}, "/app/v1/groupFollow/update": {"post": {"description": "Update Group Follow | 退出圈子", "tags": ["groupfollow"], "summary": "Update Group Follow | 退出圈子", "operationId": "UpdateGroupFollow", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/GroupFollowInfo"}}], "responses": {"200": {"description": "GroupFollowInfoResp", "schema": {"$ref": "#/definitions/GroupFollowInfoResp"}}}}}, "/app/v1/groupMember/list": {"post": {"description": "Get Group Member List | 获取圈主列表", "tags": ["groupmember"], "summary": "Get Group Member List | 获取圈主列表", "operationId": "GetGroupMemberList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/GroupMemberListReq"}}], "responses": {"200": {"description": "GroupMemberListResp", "schema": {"$ref": "#/definitions/GroupMemberListResp"}}}}}, "/app/v1/license/activate": {"post": {"description": "Activate License | 激活License", "tags": ["malllicense"], "summary": "Activate License | 激活License", "operationId": "ActivateLicense", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/ActivateLicenseReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/app/v1/license/bindDevice": {"post": {"description": "Bind Device | 绑定设备", "tags": ["malllicense"], "summary": "Bind Device | 绑定设备", "operationId": "BindDevice", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/BindDeviceReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/app/v1/license/getMachineCode": {"post": {"description": "Get Machine Code | 获取机器码", "tags": ["malllicense"], "summary": "Get Machine Code | 获取机器码", "operationId": "GetMachineCode", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/GetMachineCodeReq"}}], "responses": {"200": {"description": "GetMachineCodeResp", "schema": {"$ref": "#/definitions/GetMachineCodeResp"}}}}}, "/app/v1/license/getServerStatus": {"post": {"description": "Get Server Status | 获取服务器状态", "tags": ["malllicense"], "summary": "Get Server Status | 获取服务器状态", "operationId": "GetServerStatus", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/EmptyReq"}}], "responses": {"200": {"description": "ServerStatusResp", "schema": {"$ref": "#/definitions/ServerStatusResp"}}}}}, "/app/v1/license/getServerTime": {"post": {"description": "Get Server Timest | 获取服务器时间", "tags": ["malllicense"], "summary": "Get Server Timest | 获取服务器时间", "operationId": "GetServerTime", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/EmptyReq"}}], "responses": {"200": {"description": "ServerTimeResp", "schema": {"$ref": "#/definitions/ServerTimeResp"}}}}}, "/app/v1/license/heartbeat": {"post": {"description": "Heartbeat Request | 心跳请求", "tags": ["malllicense"], "summary": "Heartbeat Request | 心跳请求", "operationId": "Heartbeat", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/HeartbeatReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/app/v1/license/query": {"post": {"description": "Query License | 查询License", "tags": ["malllicense"], "summary": "Query License | 查询License", "operationId": "QueryLicense", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/QueryLicenseReq"}}], "responses": {"200": {"description": "QueryLicenseResp", "schema": {"$ref": "#/definitions/QueryLicenseResp"}}}}}, "/app/v1/license/unbindDevice": {"post": {"description": "Unbind Device | 解绑设备", "tags": ["malllicense"], "summary": "Unbind Device | 解绑设备", "operationId": "UnbindDevice", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/UnbindDeviceReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/app/v1/maintenInfo": {"post": {"description": "Get Mainten Info By ID | 通过ID获取维修单", "tags": ["mainteninfo"], "summary": "Get Mainten Info By ID | 通过ID获取维修单", "operationId": "GetMaintenInfoById", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDReq"}}], "responses": {"200": {"description": "MaintenInfoInfoResp", "schema": {"$ref": "#/definitions/MaintenInfoInfoResp"}}}}}, "/app/v1/maintenInfo/create": {"post": {"description": "Create Mainten Info | 创建维修单", "tags": ["mainteninfo"], "summary": "Create Mainten Info | 创建维修单", "operationId": "CreateMaintenInfo", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/MaintenInfoInfo"}}], "responses": {"200": {"description": "MaintenInfoInfoResp", "schema": {"$ref": "#/definitions/MaintenInfoInfoResp"}}}}}, "/app/v1/maintenInfo/list": {"post": {"description": "Get Mainten Info List | 获取维修单列表", "tags": ["mainteninfo"], "summary": "Get Mainten Info List | 获取维修单列表", "operationId": "GetMaintenInfoList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/MaintenInfoListReq"}}], "responses": {"200": {"description": "MaintenInfoListResp", "schema": {"$ref": "#/definitions/MaintenInfoListResp"}}}}}, "/app/v1/mallOrder": {"post": {"description": "Get mall order by ID | 通过ID获取订单", "tags": ["mallorder"], "summary": "Get mall order by ID | 通过ID获取订单", "operationId": "GetMallOrderById", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDReq"}}], "responses": {"200": {"description": "MallOrderInfoResp", "schema": {"$ref": "#/definitions/MallOrderInfoResp"}}}}}, "/app/v1/mallOrder/anonymous": {"post": {"description": "Get mall order by ID | 通过ID获取匿名订单", "tags": ["anonymous<PERSON><PERSON>er"], "summary": "Get mall order by ID | 通过ID获取匿名订单", "operationId": "GetAnonymousMallOrderById", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/AnonymousMallOrderReq"}}], "responses": {"200": {"description": "MallOrderInfoResp", "schema": {"$ref": "#/definitions/MallOrderInfoResp"}}}}}, "/app/v1/mallOrder/anonymous/create": {"post": {"description": "Create anonymous mall order information | 创建匿名订单", "tags": ["anonymous<PERSON><PERSON>er"], "summary": "Create anonymous mall order information | 创建匿名订单", "operationId": "CreateAnonymousMallOrder", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/CreateAnonymousMallOrderReq"}}], "responses": {"200": {"description": "CreateMallOrderResp", "schema": {"$ref": "#/definitions/CreateMallOrderResp"}}}}}, "/app/v1/mallOrder/anonymous/list": {"post": {"description": "Get anonymous mall order list | 获取匿名订单列表", "tags": ["anonymous<PERSON><PERSON>er"], "summary": "Get anonymous mall order list | 获取匿名订单列表", "operationId": "GetAnonymousMallOrderList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/AnonymousMallOrderListReq"}}], "responses": {"200": {"description": "MallOrderListResp", "schema": {"$ref": "#/definitions/MallOrderListResp"}}}}}, "/app/v1/mallOrder/create": {"post": {"description": "Create mall order information | 创建订单", "tags": ["mallorder"], "summary": "Create mall order information | 创建订单", "operationId": "CreateMallOrder", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/CreateMallOrderReq"}}], "responses": {"200": {"description": "CreateMallOrderResp", "schema": {"$ref": "#/definitions/CreateMallOrderResp"}}}}}, "/app/v1/mallOrder/list": {"post": {"description": "Get mall order list | 获取订单列表", "tags": ["mallorder"], "summary": "Get mall order list | 获取订单列表", "operationId": "GetMallOrderList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/MallOrderListReq"}}], "responses": {"200": {"description": "MallOrderListResp", "schema": {"$ref": "#/definitions/MallOrderListResp"}}}}}, "/app/v1/mallOrder/payment/status": {"post": {"description": "Query payment status | 查询支付状态", "tags": ["payment"], "summary": "Query payment status | 查询支付状态", "operationId": "QueryPaymentStatus", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/QueryPaymentStatusReq"}}], "responses": {"200": {"description": "QueryPaymentStatusResp", "schema": {"$ref": "#/definitions/QueryPaymentStatusResp"}}}}}, "/app/v1/mallProduct": {"post": {"description": "Get mall product by ID | 通过ID获取商品信息", "tags": ["mallproduct"], "summary": "Get mall product by ID | 通过ID获取商品信息", "operationId": "GetMallProductById", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/GetMallProductByDeviceIdAndMachineCodeReq"}}], "responses": {"200": {"description": "MallProductResp", "schema": {"$ref": "#/definitions/MallProductResp"}}}}}, "/app/v1/mallProduct/getByAppPackageName": {"post": {"description": "Get mall product by App package name | 通过应用包名获取商品信息", "tags": ["mallproduct"], "summary": "Get mall product by App package name | 通过应用包名获取商品信息", "operationId": "GetMallProductByAppPackageName", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/AppPackageNameReq"}}], "responses": {"200": {"description": "MallProductResp", "schema": {"$ref": "#/definitions/MallProductResp"}}}}}, "/app/v1/mallProduct/list": {"post": {"description": "Get mall product list | 获取商品信息列表", "tags": ["mallproduct"], "summary": "Get mall product list | 获取商品信息列表", "operationId": "GetMallProductList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/MallProductListReq"}}], "responses": {"200": {"description": "MallProductListResp", "schema": {"$ref": "#/definitions/MallProductListResp"}}}}}, "/app/v1/mallProductSku": {"post": {"description": "Get mall product sku by ID | 通过ID获取商品SKU信息", "tags": ["mallproductsku"], "summary": "Get mall product sku by ID | 通过ID获取商品SKU信息", "operationId": "GetMallProductSkuById", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDReq"}}], "responses": {"200": {"description": "MallProductSkuInfoResp", "schema": {"$ref": "#/definitions/MallProductSkuInfoResp"}}}}}, "/app/v1/mallProductSku/getByDeviceIdAndMachineCode": {"post": {"description": "Get Mall Product By DeviceId and MachineCode | 通过设备ID和机器码获取商品SKU信息", "tags": ["mallproductsku"], "summary": "Get Mall Product By DeviceId and MachineCode | 通过设备ID和机器码获取商品SKU信息", "operationId": "GetMallProductByDeviceIdAndMachineCode", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/GetMallProductByDeviceIdAndMachineCodeReq"}}], "responses": {"200": {"description": "MallProductSkuInfoResp", "schema": {"$ref": "#/definitions/MallProductSkuInfoResp"}}}}}, "/app/v1/mallProductSku/list": {"post": {"description": "Get mall product sku list | 获取商品SKU信息列表", "tags": ["mallproductsku"], "summary": "Get mall product sku list | 获取商品SKU信息列表", "operationId": "GetMallProductSkuList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/MallProductSkuListReq"}}], "responses": {"200": {"description": "MallProductSkuListResp", "schema": {"$ref": "#/definitions/MallProductSkuListResp"}}}}}, "/app/v1/payment/mock/notify/{channelCode}": {"post": {"description": "Mock Payment Notify | 模拟支付回调", "tags": ["payment"], "summary": "Mock Payment Notify | 模拟支付回调", "operationId": "MockPaymentNotify", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/MockNotifyReq"}}], "responses": {"200": {"$ref": "#/responses/string"}}}}, "/app/v1/payment/notify/{channelCode}": {"post": {"description": "Payment Notify | 支付回调", "tags": ["payment"], "summary": "Payment Notify | 支付回调", "operationId": "PaymentNotify", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/NotifyReq"}}], "responses": {"200": {"$ref": "#/responses/string"}}}}, "/app/v1/payment/submit": {"post": {"description": "Submit payment order | 提交支付订单", "tags": ["payment"], "summary": "Submit payment order | 提交支付订单", "operationId": "SubmitPayOrder", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/MallOrderSubmitReq"}}], "responses": {"200": {"description": "MallOrderSubmitResp", "schema": {"$ref": "#/definitions/MallOrderSubmitResp"}}}}}, "/app/v1/payment/success/alipay": {"get": {"description": "Payment Succee | 同步完成支付", "tags": ["payment"], "summary": "Payment Succee | 同步完成支付", "operationId": "AliPaySuccess", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/AlipaySuccessReq"}}], "responses": {"200": {"$ref": "#/responses/string"}}}}, "/app/v1/product": {"post": {"description": "Get Product By ID | 通过ID获取产品信息", "tags": ["product"], "summary": "Get Product By ID | 通过ID获取产品信息", "operationId": "GetProductById", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDReq"}}], "responses": {"200": {"description": "ProductInfoResp", "schema": {"$ref": "#/definitions/ProductInfoResp"}}}}}, "/app/v1/product/list": {"post": {"description": "Get Product List | 获取产品列表", "tags": ["product"], "summary": "Get Product List | 获取产品列表", "operationId": "GetProductList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/ProductListReq"}}], "responses": {"200": {"description": "ProductListResp", "schema": {"$ref": "#/definitions/ProductListResp"}}}}}, "/app/v1/product/update": {"post": {"description": "Update Product | 更新产品", "tags": ["product"], "summary": "Update Product | 更新产品", "operationId": "UpdateProduct", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/ProductInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/app/v1/province": {"post": {"description": "Get Province By ID | 通过ID获取省份", "tags": ["province"], "summary": "Get Province By ID | 通过ID获取省份", "operationId": "GetProvinceById", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDReq"}}], "responses": {"200": {"description": "ProvinceInfoResp", "schema": {"$ref": "#/definitions/ProvinceInfoResp"}}}}}, "/app/v1/province/list": {"post": {"description": "Get Province List | 获取省份列表", "tags": ["province"], "summary": "Get Province List | 获取省份列表", "operationId": "GetProvinceList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/ProvinceListReq"}}], "responses": {"200": {"description": "ProvinceListResp", "schema": {"$ref": "#/definitions/ProvinceListResp"}}}}}, "/app/v1/refund/mock/notify/{channelCode}": {"post": {"description": "Mock Refund Notify | 模拟退款回调", "tags": ["payment"], "summary": "Mock Refund Notify | 模拟退款回调", "operationId": "MockRefundNotify", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/MockNotifyReq"}}], "responses": {"200": {"$ref": "#/responses/string"}}}}, "/app/v1/refund/notify/{channelCode}": {"post": {"description": "Refund Notify | 退款回调", "tags": ["payment"], "summary": "Refund Notify | 退款回调", "operationId": "RefundNotify", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/NotifyReq"}}], "responses": {"200": {"$ref": "#/responses/string"}}}}, "/app/v1/segment": {"post": {"description": "Get Segment By ID | 通过ID获取栏目信息", "tags": ["segment"], "summary": "Get Segment By ID | 通过ID获取栏目信息", "operationId": "GetSegmentById", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDReq"}}], "responses": {"200": {"description": "SegmentInfoResp", "schema": {"$ref": "#/definitions/SegmentInfoResp"}}}}}, "/app/v1/segment/list": {"post": {"description": "Get Segment List | 获取栏目列表", "tags": ["segment"], "summary": "Get Segment List | 获取栏目列表", "operationId": "GetSegmentList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/SegmentListReq"}}], "responses": {"200": {"description": "SegmentListResp", "schema": {"$ref": "#/definitions/SegmentListResp"}}}}}, "/app/v1/segmentArticleRecord": {"post": {"description": "Get segment article record by ID | 通过ID获取SegmentArticleRecord", "tags": ["segmentarticlerecord"], "summary": "Get segment article record by ID | 通过ID获取SegmentArticleRecord", "operationId": "GetSegmentArticleRecordById", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDReq"}}], "responses": {"200": {"description": "SegmentArticleRecordInfoResp", "schema": {"$ref": "#/definitions/SegmentArticleRecordInfoResp"}}}}}, "/app/v1/segmentArticleRecord/list": {"post": {"description": "Get segment article record list | 获取SegmentArticleRecord列表", "tags": ["segmentarticlerecord"], "summary": "Get segment article record list | 获取SegmentArticleRecord列表", "operationId": "GetSegmentArticleRecordList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/SegmentArticleRecordListReq"}}], "responses": {"200": {"description": "SegmentArticleRecordListResp", "schema": {"$ref": "#/definitions/SegmentArticleRecordListResp"}}}}}, "/app/v1/street": {"post": {"description": "Get Street By ID | 通过ID获取街道", "tags": ["street"], "summary": "Get Street By ID | 通过ID获取街道", "operationId": "GetStreetById", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDReq"}}], "responses": {"200": {"description": "StreetInfoResp", "schema": {"$ref": "#/definitions/StreetInfoResp"}}}}}, "/app/v1/street/list": {"post": {"description": "Get Street List | 获取街道列表", "tags": ["street"], "summary": "Get Street List | 获取街道列表", "operationId": "GetStreetList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/StreetListReq"}}], "responses": {"200": {"description": "StreetListResp", "schema": {"$ref": "#/definitions/StreetListResp"}}}}}, "/app/v1/tag": {"post": {"description": "Get Tag By ID | 获取话题信息", "tags": ["tag"], "summary": "Get Tag By ID | 获取话题信息", "operationId": "GetTagById", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDReq"}}], "responses": {"200": {"description": "TagInfoResp", "schema": {"$ref": "#/definitions/TagInfoResp"}}}}}, "/app/v1/tag/create": {"post": {"description": "Create Tag | 创建话题", "tags": ["tag"], "summary": "Create Tag | 创建话题", "operationId": "CreateTag", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/TagInfo"}}], "responses": {"200": {"description": "TagInfoResp", "schema": {"$ref": "#/definitions/TagInfoResp"}}}}}, "/app/v1/tag/list": {"post": {"description": "Get Tag List | 获取话题列表", "tags": ["tag"], "summary": "Get Tag List | 获取话题列表", "operationId": "GetTagList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/TagListReq"}}], "responses": {"200": {"description": "TagListResp", "schema": {"$ref": "#/definitions/TagListResp"}}}}}, "/app/v1/tagFollow/create": {"post": {"description": "Create Tag Follow | 关注话题", "tags": ["tagfollow"], "summary": "Create Tag Follow | 关注话题", "operationId": "CreateTagFollow", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/TagFollowInfo"}}], "responses": {"200": {"description": "TagFollowInfoResp", "schema": {"$ref": "#/definitions/TagFollowInfoResp"}}}}}, "/app/v1/tagFollow/update": {"post": {"description": "Update Tag Follow | 取消关注话题", "tags": ["tagfollow"], "summary": "Update Tag Follow | 取消关注话题", "operationId": "UpdateTagFollow", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/TagFollowInfo"}}], "responses": {"200": {"description": "TagFollowInfoResp", "schema": {"$ref": "#/definitions/TagFollowInfoResp"}}}}}, "/app/v1/userAddres": {"post": {"description": "Get User Addres By ID | 获取用户地址信息", "tags": ["useraddres"], "summary": "Get User Addres By ID | 获取用户地址信息", "operationId": "GetUserAddresById", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDReq"}}], "responses": {"200": {"description": "UserAddresInfoResp", "schema": {"$ref": "#/definitions/UserAddresInfoResp"}}}}}, "/app/v1/userAddres/create": {"post": {"description": "Create User Addres | 创建用户地址", "tags": ["useraddres"], "summary": "Create User Addres | 创建用户地址", "operationId": "CreateUserAddres", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/UserAddresInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/app/v1/userAddres/delete": {"post": {"description": "Delete User Addres | 删除用户地址", "tags": ["useraddres"], "summary": "Delete User Addres | 删除用户地址", "operationId": "DeleteUserAddres", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDsReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/app/v1/userAddres/list": {"post": {"description": "Get User Addres List | 获取用户地址列表", "tags": ["useraddres"], "summary": "Get User Addres List | 获取用户地址列表", "operationId": "GetUserAddresList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/UserAddresListReq"}}], "responses": {"200": {"description": "UserAddresListResp", "schema": {"$ref": "#/definitions/UserAddresListResp"}}}}}, "/app/v1/userAddres/update": {"post": {"description": "Update User Addres | 修改用户地址", "tags": ["useraddres"], "summary": "Update User Addres | 修改用户地址", "operationId": "UpdateUserAddres", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/UserAddresInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/app/v1/userAgreement": {"post": {"description": "Get user agreement by ID | 通过ID获取UserAgreement信息", "tags": ["useragreement"], "summary": "Get user agreement by ID | 通过ID获取UserAgreement信息", "operationId": "GetUserAgreementById", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDReq"}}], "responses": {"200": {"description": "UserAgreementInfoResp", "schema": {"$ref": "#/definitions/UserAgreementInfoResp"}}}}}, "/app/v1/userAgreement/getUserAgreementByKey": {"post": {"description": "Get", "tags": ["useragreement"], "summary": "Get", "operationId": "GetUserAgreementByKey", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/GetUserAgreementByKeyReq"}}], "responses": {"200": {"description": "UserAgreementInfoResp", "schema": {"$ref": "#/definitions/UserAgreementInfoResp"}}}}}, "/bolo_lexicon/list": {"post": {"description": "Get bolo lexicon list | 获取BoloLexicon信息列表", "tags": ["bololexicon"], "summary": "Get bolo lexicon list | 获取BoloLexicon信息列表", "operationId": "GetBoloLexiconList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/BoloLexiconListReq"}}], "responses": {"200": {"description": "BoloLexiconListResp", "schema": {"$ref": "#/definitions/BoloLexiconListResp"}}}}}, "/health": {"get": {"description": "健康检查接口 | Health Check for Load Balancer", "summary": "健康检查接口 | Health Check for Load Balancer", "operationId": "healthCheck", "responses": {"200": {"description": "HealthCheckResp", "schema": {"$ref": "#/definitions/HealthCheckResp"}}}}}, "/mall_order_item": {"post": {"description": "Get mall order item by ID | 通过ID获取MallOrderItem信息", "tags": ["mallorderitem"], "summary": "Get mall order item by ID | 通过ID获取MallOrderItem信息", "operationId": "GetMallOrderItemById", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDReq"}}], "responses": {"200": {"description": "MallOrderItemInfoResp", "schema": {"$ref": "#/definitions/MallOrderItemInfoResp"}}}}}, "/mall_order_item/list": {"post": {"description": "Get mall order item list | 获取MallOrderItem信息列表", "tags": ["mallorderitem"], "summary": "Get mall order item list | 获取MallOrderItem信息列表", "operationId": "GetMallOrderItemList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/MallOrderItemListReq"}}], "responses": {"200": {"description": "MallOrderItemListResp", "schema": {"$ref": "#/definitions/MallOrderItemListResp"}}}}}, "/rental_device": {"post": {"description": "Get rental device by ID | 通过ID获取RentalDevice信息", "tags": ["rentaldevice"], "summary": "Get rental device by ID | 通过ID获取RentalDevice信息", "operationId": "GetRentalDeviceById", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDReq"}}], "responses": {"200": {"description": "RentalDeviceInfoResp", "schema": {"$ref": "#/definitions/RentalDeviceInfoResp"}}}}}, "/rental_device/create": {"post": {"description": "Create rental device information | 创建RentalDevice信息", "tags": ["rentaldevice"], "summary": "Create rental device information | 创建RentalDevice信息", "operationId": "CreateRentalDevice", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/RentalDeviceInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/rental_device/delete": {"post": {"description": "Delete rental device information | 删除RentalDevice信息", "tags": ["rentaldevice"], "summary": "Delete rental device information | 删除RentalDevice信息", "operationId": "DeleteRentalDevice", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDsReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/rental_device/list": {"post": {"description": "Get rental device list | 获取RentalDevice信息列表", "tags": ["rentaldevice"], "summary": "Get rental device list | 获取RentalDevice信息列表", "operationId": "GetRentalDeviceList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/RentalDeviceListReq"}}], "responses": {"200": {"description": "RentalDeviceListResp", "schema": {"$ref": "#/definitions/RentalDeviceListResp"}}}}}, "/rental_device/update": {"post": {"description": "Update rental device information | 更新RentalDevice信息", "tags": ["rentaldevice"], "summary": "Update rental device information | 更新RentalDevice信息", "operationId": "UpdateRentalDevice", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/RentalDeviceInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/v1/article/admin/delete": {"delete": {"description": "Admin Delete Article | 管理员删除文章", "tags": ["admin"], "summary": "Admin Delete Article | 管理员删除文章", "operationId": "AdminDeleteArticle", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/AdminArticleDeleteReq"}}], "responses": {"200": {"description": "BaseDataInfo", "schema": {"$ref": "#/definitions/BaseDataInfo"}}}}}, "/v1/article/content/image/auditedCallback": {"post": {"description": "Image Audited Callback | 图片审核结果", "tags": ["article"], "summary": "Image Audited Callback | 图片审核结果", "operationId": "ImageAuditedCallback", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/ImageAuditedCallbackReq"}}], "responses": {"200": {"description": "AuditedCallbackResp", "schema": {"$ref": "#/definitions/AuditedCallbackResp"}}}}}, "/v1/article/content/video/auditedCallback": {"post": {"description": "Video Audited Callback | 视频审核结果", "tags": ["article"], "summary": "Video Audited Callback | 视频审核结果", "operationId": "VideoAuditedCallback", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/VideoAuditedCallbackReq"}}], "responses": {"200": {"description": "AuditedCallbackResp", "schema": {"$ref": "#/definitions/AuditedCallbackResp"}}}}}, "/v1/article/delete": {"delete": {"description": "Delete Article | 删除文章", "tags": ["article"], "summary": "Delete Article | 删除文章", "operationId": "ArticleDelete", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/ArticleDeleteReq"}}], "responses": {"200": {"description": "CommonResp", "schema": {"$ref": "#/definitions/CommonResp"}}}}}, "/v1/article/detail": {"post": {"description": "Get Article Detail | 话题详情", "tags": ["article"], "summary": "Get Article Detail | 话题详情", "operationId": "ArticleDetail", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/ArticleDetailReq"}}], "responses": {"200": {"description": "ArticleDetailResp", "schema": {"$ref": "#/definitions/ArticleDetailResp"}}}}}, "/v1/article/draft/lists": {"post": {"description": "Get Article Draft List | 获取文章草稿列表", "tags": ["article"], "summary": "Get Article Draft List | 获取文章草稿列表", "operationId": "ArticleDraftLists", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/ArticleDraftListsReq"}}], "responses": {"200": {"description": "ArticleDraftListsResp", "schema": {"$ref": "#/definitions/ArticleDraftListsResp"}}}}}, "/v1/article/draft/save": {"post": {"description": "Save Draft | 保存草稿", "tags": ["article"], "summary": "Save Draft | 保存草稿", "operationId": "ArticleSaveDraft", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/ArticlSaveDraftReq"}}], "responses": {"200": {"description": "ArticlSaveDraftResp", "schema": {"$ref": "#/definitions/ArticlSaveDraftResp"}}}}}, "/v1/article/like/lists": {"post": {"description": "Get Article Like List | 文章点赞列表", "tags": ["article"], "summary": "Get Article Like List | 文章点赞列表", "operationId": "ArticleLikeList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/ArticleLikeListReq"}}], "responses": {"200": {"description": "ArticleLikeListResp", "schema": {"$ref": "#/definitions/ArticleLikeListResp"}}}}}, "/v1/article/lists": {"post": {"description": "Get Article List | 话题列表", "tags": ["article"], "summary": "Get Article List | 话题列表", "operationId": "ArticleLists", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/ArticleListsReq"}}], "responses": {"200": {"description": "ArticleListsResp", "schema": {"$ref": "#/definitions/ArticleListsResp"}}}}}, "/v1/article/listsByGroupId": {"post": {"description": "Get Article List By GroupId | 根据群组ID获取话题列表", "tags": ["article"], "summary": "Get Article List By GroupId | 根据群组ID获取话题列表", "operationId": "ArticleListsByGroupId", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/ArticleListsByGroupIdReq"}}], "responses": {"200": {"description": "ArticleListsResp", "schema": {"$ref": "#/definitions/ArticleListsResp"}}}}}, "/v1/article/listsByTagId": {"post": {"description": "Get Article List By TagId | 根据标签ID获取话题列表", "tags": ["article"], "summary": "Get Article List By TagId | 根据标签ID获取话题列表", "operationId": "ArticleListsByTagId", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/ArticleListsByTagIdReq"}}], "responses": {"200": {"description": "ArticleListsResp", "schema": {"$ref": "#/definitions/ArticleListsResp"}}}}}, "/v1/article/publish": {"post": {"description": "Publish Article | 发布话题", "tags": ["article"], "summary": "Publish Article | 发布话题", "operationId": "Publish", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/PublishReq"}}], "responses": {"200": {"description": "PublishResp", "schema": {"$ref": "#/definitions/PublishResp"}}}}}, "/v1/article/reply": {"post": {"description": "Reply Article | 评论文章", "tags": ["article"], "summary": "Reply Article | 评论文章", "operationId": "Reply", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/ReplyReq"}}], "responses": {"200": {"description": "ReplyResp", "schema": {"$ref": "#/definitions/ReplyResp"}}}}}, "/v1/article/reply/admin/delete": {"delete": {"description": "Admin Delete Reply | 管理员删除评论", "tags": ["admin"], "summary": "Admin Delete Reply | 管理员删除评论", "operationId": "AdminDeleteReply", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/AdminDelReplyReq"}}], "responses": {"200": {"description": "BaseDataInfo", "schema": {"$ref": "#/definitions/BaseDataInfo"}}}}}, "/v1/article/reply/delete": {"delete": {"description": "Delete Reply | 删除评论", "tags": ["article"], "summary": "Delete Reply | 删除评论", "operationId": "DeleteReply", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/DelReplyReq"}}], "responses": {"200": {"description": "DelReplyResp", "schema": {"$ref": "#/definitions/DelReplyResp"}}}}}, "/v1/article/reply/lists": {"post": {"description": "Get Reply List | 文章评论列表", "tags": ["article"], "summary": "Get Reply List | 文章评论列表", "operationId": "ReplyList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/ReplyListReq"}}], "responses": {"200": {"description": "ReplyListResp", "schema": {"$ref": "#/definitions/ReplyListResp"}}}}}, "/v1/article/reply/userlists": {"post": {"description": "Get User Reply List | 用户评论列表", "tags": ["article"], "summary": "Get User Reply List | 用户评论列表", "operationId": "UserReplyList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/UserReplyListReq"}}], "responses": {"200": {"description": "ReplyListResp", "schema": {"$ref": "#/definitions/ReplyListResp"}}}}}, "/v1/article/upload/cos/policy": {"post": {"description": "Get Tecent Cloud COS Post Policy | 获取腾讯云COS Post Policy", "tags": ["article"], "summary": "Get Tecent Cloud COS Post Policy | 获取腾讯云COS Post Policy", "operationId": "GetTecentCloudCosPostPolicy", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/TecentCloudUploadPostPolicyReq"}}], "responses": {"200": {"description": "TecentCloudUploadPostPolicyResp", "schema": {"$ref": "#/definitions/TecentCloudUploadPostPolicyResp"}}}}}, "/v1/article/upload/cos/token": {"post": {"description": "Get Tecent Cloud STS Temporary Key | 获取腾讯云STS临时密钥", "tags": ["article"], "summary": "Get Tecent Cloud STS Temporary Key | 获取腾讯云STS临时密钥", "operationId": "GetTecentCloudUploadToken", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/TecentCloudUploadTokenReq"}}], "responses": {"200": {"description": "TecentCloudUploadTokenResp", "schema": {"$ref": "#/definitions/TecentCloudUploadTokenResp"}}}}}, "/v1/article/upload/cover": {"post": {"description": "Upload Cover | 上传封面", "tags": ["article"], "summary": "Upload Cover | 上传封面", "operationId": "UploadCover", "responses": {"200": {"description": "UploadCoverResp", "schema": {"$ref": "#/definitions/UploadCoverResp"}}}}}, "/v1/article/userlists": {"post": {"description": "Get User Article List | 用户话题列表", "tags": ["article"], "summary": "Get User Article List | 用户话题列表", "operationId": "UserArticleLists", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/UserArticleListsReq"}}], "responses": {"200": {"description": "ArticleListsResp", "schema": {"$ref": "#/definitions/ArticleListsResp"}}}}}, "/v1/article/view/add": {"post": {"description": "Add Article View Record | 添加文章阅读记录", "tags": ["article"], "summary": "Add Article View Record | 添加文章阅读记录", "operationId": "AddArticleViewRecord", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/AddArticleViewRecordReq"}}], "responses": {"200": {"description": "AddArticleViewRecordResp", "schema": {"$ref": "#/definitions/AddArticleViewRecordResp"}}}}}, "/v1/geetestVerify": {"post": {"description": "Geetest Verify | 极验验证", "tags": ["user"], "summary": "Geetest Verify | 极验验证", "operationId": "GeetestVerify", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/GeetestVerifyReq"}}], "responses": {"200": {"description": "GeetestVerifyResp", "schema": {"$ref": "#/definitions/GeetestVerifyResp"}}}}}, "/v1/user/admin/ban": {"post": {"description": "Admin Ban User | 管理员封禁用户", "tags": ["admin"], "summary": "Admin Ban User | 管理员封禁用户", "operationId": "AdminBanUser", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/AdminBanUserReq"}}], "responses": {"200": {"description": "BaseDataInfo", "schema": {"$ref": "#/definitions/BaseDataInfo"}}}}}, "/v1/user/admin/mute": {"post": {"description": "Admin Mute User | 管理员禁言用户", "tags": ["admin"], "summary": "Admin Mute User | 管理员禁言用户", "operationId": "AdminMuteUser", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/AdminMuteUserReq"}}], "responses": {"200": {"description": "BaseDataInfo", "schema": {"$ref": "#/definitions/BaseDataInfo"}}}}}, "/v1/user/admin/unban": {"post": {"description": "Admin Unban User | 管理员解封用户", "tags": ["admin"], "summary": "Admin Unban User | 管理员解封用户", "operationId": "AdminUnbanUser", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/AdminUnbanUserReq"}}], "responses": {"200": {"description": "BaseDataInfo", "schema": {"$ref": "#/definitions/BaseDataInfo"}}}}}, "/v1/user/admin/unmute": {"post": {"description": "Admin Unmute User | 管理员解禁用户", "tags": ["admin"], "summary": "Admin Unmute User | 管理员解禁用户", "operationId": "AdminUnmuteUser", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/AdminUnmuteUserReq"}}], "responses": {"200": {"description": "BaseDataInfo", "schema": {"$ref": "#/definitions/BaseDataInfo"}}}}}, "/v1/user/bindWxByMobile": {"post": {"description": "Bind WX By Mobile | 绑定用户微信号", "tags": ["user"], "summary": "Bind WX By Mobile | 绑定用户微信号", "operationId": "BindWxByMobile", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/BindWxByMobileReq"}}], "responses": {"200": {"description": "LoginResp", "schema": {"$ref": "#/definitions/LoginResp"}}}}}, "/v1/user/detail": {"post": {"description": "Get Current Login User Info | 当前登陆用户详情", "tags": ["user"], "summary": "Get Current Login User Info | 当前登陆用户详情", "operationId": "Detail", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/UserInfoReq"}}], "responses": {"200": {"description": "AppletUserInfoResp", "schema": {"$ref": "#/definitions/AppletUserInfoResp"}}}}}, "/v1/user/email/code": {"post": {"description": "Send Email Code | 发送邮箱验证 (用于验证邮箱)", "tags": ["user"], "summary": "Send Email Code | 发送邮箱验证 (用于验证邮箱)", "operationId": "SendEmailCode", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/SendEmailCodeReq"}}], "responses": {"200": {"description": "SendEmailCodeResp", "schema": {"$ref": "#/definitions/SendEmailCodeResp"}}}}}, "/v1/user/email/code/verify": {"post": {"description": "Verify Email Code | 验证邮箱验证码", "tags": ["user"], "summary": "Verify Email Code | 验证邮箱验证码", "operationId": "VerifiedEmailCode", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/VerifyEmailCodeReq"}}], "responses": {"200": {"description": "VerifyEmailCodeResp", "schema": {"$ref": "#/definitions/VerifyEmailCodeResp"}}}}}, "/v1/user/email/set": {"post": {"description": "Get user's permission code | 设置用户邮箱", "tags": ["user"], "summary": "Get user's permission code | 设置用户邮箱", "operationId": "SetUserEmail", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/SetUserEmailReq"}}], "responses": {"200": {"description": "BaseDataInfo", "schema": {"$ref": "#/definitions/BaseDataInfo"}}}}}, "/v1/user/fanslists": {"post": {"description": "Get Fans List | 用户粉丝列表", "tags": ["user"], "summary": "Get Fans List | 用户粉丝列表", "operationId": "FansList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/FansListReq"}}], "responses": {"200": {"description": "FansListResp", "schema": {"$ref": "#/definitions/FansListResp"}}}}}, "/v1/user/follow": {"post": {"description": "Follow User | 用户关注", "tags": ["user"], "summary": "Follow User | 用户关注", "operationId": "Follow", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/FollowReq"}}], "responses": {"200": {"description": "FollowResp", "schema": {"$ref": "#/definitions/FollowResp"}}}}}, "/v1/user/followlists": {"post": {"description": "Get Follow List | 用户关注列表", "tags": ["user"], "summary": "Get Follow List | 用户关注列表", "operationId": "FollowList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/FollowListReq"}}], "responses": {"200": {"description": "FollowListResp", "schema": {"$ref": "#/definitions/FollowListResp"}}}}}, "/v1/user/getUserInfoByMobile": {"post": {"description": "Get User Info By Mobile | 根据手机号获取用户详情", "tags": ["user"], "summary": "Get User Info By Mobile | 根据手机号获取用户详情", "operationId": "GetUserInfoByMobile", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/UserInfoByMobileReq"}}], "responses": {"200": {"description": "UserInfoByUserIdResp", "schema": {"$ref": "#/definitions/UserInfoByUserIdResp"}}}}}, "/v1/user/getUserInfoByName": {"post": {"description": "Get User Info By Name | 根据用户名获取用户详情", "tags": ["user"], "summary": "Get User Info By Name | 根据用户名获取用户详情", "operationId": "GetUserInfoByName", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/UserInfoByNameReq"}}], "responses": {"200": {"description": "AppletUserInfoResp", "schema": {"$ref": "#/definitions/AppletUserInfoResp"}}}}}, "/v1/user/getUserInfoByUserId": {"post": {"description": "Get User Info By UserId | 根据用户ID获取用户详情", "tags": ["user"], "summary": "Get User Info By UserId | 根据用户ID获取用户详情", "operationId": "GetUserInfoByUserId", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/UserInfoByUserIdReq"}}], "responses": {"200": {"description": "UserInfoByUserIdResp", "schema": {"$ref": "#/definitions/UserInfoByUserIdResp"}}}}}, "/v1/user/getcode": {"post": {"description": "Get Send Sms Code | 根据手机号获取已经发送验证码(自动化测试专用)", "tags": ["user"], "summary": "Get Send Sms Code | 根据手机号获取已经发送验证码(自动化测试专用)", "operationId": "GetSendSmsCode", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/GetSendSmsCodeReq"}}], "responses": {"200": {"description": "GetSendSmsCodeResp", "schema": {"$ref": "#/definitions/GetSendSmsCodeResp"}}}}}, "/v1/user/im/getUserSig": {"post": {"description": "Get User Sig | 获取用户签名", "tags": ["user"], "summary": "Get User Sig | 获取用户签名", "operationId": "GetUserSig", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/GetUserSigReq"}}], "responses": {"200": {"description": "GetUserSigResp", "schema": {"$ref": "#/definitions/GetUserSigResp"}}}}}, "/v1/user/isthumbup": {"post": {"description": "Is Thumbup | 用户是否已点赞", "tags": ["user"], "summary": "Is Thumbup | 用户是否已点赞", "operationId": "IsThumbup", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IsThumbupReq"}}], "responses": {"200": {"description": "IsThumbupResp", "schema": {"$ref": "#/definitions/IsThumbupResp"}}}}}, "/v1/user/login": {"post": {"description": "User Login | 用户登录", "tags": ["user"], "summary": "User Login | 用户登录", "operationId": "<PERSON><PERSON>", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/LoginReq"}}], "responses": {"200": {"description": "LoginResp", "schema": {"$ref": "#/definitions/LoginResp"}}}}}, "/v1/user/message/lists": {"post": {"description": "Get Message List | 获取用户消息列表", "tags": ["user"], "summary": "Get Message List | 获取用户消息列表", "operationId": "GetMessageList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/GetMessageListReq"}}], "responses": {"200": {"description": "GetMessageListResp", "schema": {"$ref": "#/definitions/GetMessageListResp"}}}}}, "/v1/user/msessage/read": {"post": {"description": "Read Message | 阅读消息接口", "tags": ["user"], "summary": "Read Message | 阅读消息接口", "operationId": "ReadMessage", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/ReadMessageReq"}}], "responses": {"200": {"description": "ReadMessageResp", "schema": {"$ref": "#/definitions/ReadMessageResp"}}}}}, "/v1/user/perm": {"get": {"description": "Get user's permission code | 获取用户权限码", "tags": ["user"], "summary": "Get user's permission code | 获取用户权限码", "operationId": "GetUserPermCode", "responses": {"200": {"description": "PermCodeResp", "schema": {"$ref": "#/definitions/PermCodeResp"}}}}}, "/v1/user/product/add": {"post": {"description": "Add User Product | 添加产品", "tags": ["user"], "summary": "Add User Product | 添加产品", "operationId": "AddUserProduct", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/AddUserProductReq"}}], "responses": {"200": {"description": "AddUserProductResp", "schema": {"$ref": "#/definitions/AddUserProductResp"}}}}}, "/v1/user/product/lists": {"post": {"description": "Get User Product List | 查询用户产品", "tags": ["user"], "summary": "Get User Product List | 查询用户产品", "operationId": "GetUserProductList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/GetUserProductListReq"}}], "responses": {"200": {"description": "GetUserProductListResp", "schema": {"$ref": "#/definitions/GetUserProductListResp"}}}}}, "/v1/user/realname": {"post": {"description": "RealNameAuthentication | 实名认证", "tags": ["user"], "summary": "RealNameAuthentication | 实名认证", "operationId": "RealNameAuthentication", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/RealNameAuthReq"}}], "responses": {"200": {"description": "RealNameAuthResp", "schema": {"$ref": "#/definitions/RealNameAuthResp"}}}}}, "/v1/user/register": {"post": {"description": "User Register | 用户注册", "tags": ["user"], "summary": "User Register | 用户注册", "operationId": "Register", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/RegisterReq"}}], "responses": {"200": {"description": "RegisterResp", "schema": {"$ref": "#/definitions/RegisterResp"}}}}}, "/v1/user/registerByAuthId": {"post": {"description": "Register By AuthId | 通过授权信息注册用户", "tags": ["user"], "summary": "Register By AuthId | 通过授权信息注册用户", "operationId": "RegisterByAuthId", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/RegisterByAuthIdReq"}}], "responses": {"200": {"description": "LoginResp", "schema": {"$ref": "#/definitions/LoginResp"}}}}}, "/v1/user/session/create": {"post": {"description": " 创建登录session（用于同设备登录）", "tags": ["user"], "summary": " 创建登录session（用于同设备登录）", "operationId": "CreateUserMobileSsoLoginSession", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/CreateUserMobileSsoLoginSessionReq"}}], "responses": {"200": {"description": "CreateUserMobileSsoLoginSessionResp", "schema": {"$ref": "#/definitions/CreateUserMobileSsoLoginSessionResp"}}}}}, "/v1/user/session/delete": {"delete": {"description": " 删除登录session（用于同设备登录）", "tags": ["user"], "summary": " 删除登录session（用于同设备登录）", "operationId": "DeleteUserMobileSsoLoginSession", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/DeleteUserMobileSsoLoginSessionReq"}}], "responses": {"200": {"description": "DeleteUserMobileSsoLoginSessionResp", "schema": {"$ref": "#/definitions/DeleteUserMobileSsoLoginSessionResp"}}}}}, "/v1/user/session/getMobileSSOUserLoginInfo": {"post": {"description": "获取用户登录信息（用于同设备登录）", "tags": ["user"], "summary": "获取用户登录信息（用于同设备登录）", "operationId": "GetUserMobileSsoUserLoginInfo", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/GetUserMobileSsoUserLoginInfoReq"}}], "responses": {"200": {"description": "GetUserMobileSsoUserLoginInfoResp", "schema": {"$ref": "#/definitions/GetUserMobileSsoUserLoginInfoResp"}}}}}, "/v1/user/session/getTicket": {"post": {"description": "获取登录Ticket（用于同设备登录）", "tags": ["user"], "summary": "获取登录Ticket（用于同设备登录）", "operationId": "GetUserMobileSsoTicket", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/GetUserMobileSsoTicketReq"}}], "responses": {"200": {"description": "GetUserMobileSsoTicketResp", "schema": {"$ref": "#/definitions/GetUserMobileSsoTicketResp"}}}}}, "/v1/user/setAvatar": {"post": {"description": "Set Avatar | 修改头像", "tags": ["user"], "summary": "Set Avatar | 修改头像", "operationId": "Set<PERSON><PERSON><PERSON>", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/SetAvatarReq"}}], "responses": {"200": {"description": "SetAvatarResp", "schema": {"$ref": "#/definitions/SetAvatarResp"}}}}}, "/v1/user/setBackgroundImage": {"post": {"description": "Set Background Image | 设置背景图", "tags": ["user"], "summary": "Set Background Image | 设置背景图", "operationId": "SetBackgroundImage", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/SetBackgroundImageReq"}}], "responses": {"200": {"description": "SetBackgroundImageResp", "schema": {"$ref": "#/definitions/SetBackgroundImageResp"}}}}}, "/v1/user/setNickname": {"post": {"description": "Set Nickname | 修改用户名", "tags": ["user"], "summary": "Set Nickname | 修改用户名", "operationId": "SetNickname", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/SetNicknameReq"}}], "responses": {"200": {"description": "SetNicknameResp", "schema": {"$ref": "#/definitions/SetNicknameResp"}}}}}, "/v1/user/setPassword": {"post": {"description": "Set Password | 设置密码", "tags": ["user"], "summary": "Set Password | 设置密码", "operationId": "SetPassword", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/SetPasswordReq"}}], "responses": {"200": {"description": "SetPasswordResp", "schema": {"$ref": "#/definitions/SetPasswordResp"}}}}}, "/v1/user/setUserDescription": {"post": {"description": "Set User Description | 修改用户介绍", "tags": ["user"], "summary": "Set User Description | 修改用户介绍", "operationId": "SetUserDescription", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/SetUserDescriptionReq"}}], "responses": {"200": {"description": "SetUserDescriptionResp", "schema": {"$ref": "#/definitions/SetUserDescriptionResp"}}}}}, "/v1/user/setUserHomePage": {"post": {"description": "Set User Home Page | 修改用户主页", "tags": ["user"], "summary": "Set User Home Page | 修改用户主页", "operationId": "SetUserHomePage", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/SetUserHomePageReq"}}], "responses": {"200": {"description": "SetUserHomePageResp", "schema": {"$ref": "#/definitions/SetUserHomePageResp"}}}}}, "/v1/user/setUserInfo": {"post": {"description": "Set User Info | 修改用户简介", "tags": ["user"], "summary": "Set User Info | 修改用户简介", "operationId": "SetUserInfo", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/SetUserInfoReq"}}], "responses": {"200": {"description": "SetAppletUserInfoResp", "schema": {"$ref": "#/definitions/SetAppletUserInfoResp"}}}}}, "/v1/user/setUserSex": {"post": {"description": "Set User Sex | 修改用户性别", "tags": ["user"], "summary": "Set User Sex | 修改用户性别", "operationId": "SetUserSex", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/SetUserSexReq"}}], "responses": {"200": {"description": "SetUserSexResp", "schema": {"$ref": "#/definitions/SetUserSexResp"}}}}}, "/v1/user/thumbup": {"post": {"description": "Thumbup | 用户点赞", "tags": ["user"], "summary": "Thumbup | 用户点赞", "operationId": "<PERSON><PERSON>bu<PERSON>", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/ThumbupReq"}}], "responses": {"200": {"description": "ThumbupResp", "schema": {"$ref": "#/definitions/ThumbupResp"}}}}}, "/v1/user/unfollow": {"post": {"description": "UnFollow User | 用户取消关注", "tags": ["user"], "summary": "UnFollow User | 用户取消关注", "operationId": "UnFollow", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/UnFollowReq"}}], "responses": {"200": {"description": "UnFollowResp", "schema": {"$ref": "#/definitions/UnFollowResp"}}}}}, "/v1/user/updatePassword": {"post": {"description": "Update Password | 修改密码", "tags": ["user"], "summary": "Update Password | 修改密码", "operationId": "UpdatePassword", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/UpdatePasswordReq"}}], "responses": {"200": {"description": "UpdatePasswordResp", "schema": {"$ref": "#/definitions/UpdatePasswordResp"}}}}}, "/v1/user/verifylogin": {"post": {"description": "Verify Login | 验证码登录", "tags": ["user"], "summary": "Verify Login | 验证码登录", "operationId": "VerifyLogin", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/VerifyLoginReq"}}], "responses": {"200": {"description": "LoginResp", "schema": {"$ref": "#/definitions/LoginResp"}}}}}, "/v1/user/verifyloginByAuthType": {"post": {"description": "Verify Login By AuthType | 验证码登录(AuthType)", "tags": ["user"], "summary": "Verify Login By AuthType | 验证码登录(AuthType)", "operationId": "VerifyLoginByAuthType", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/VerifyLoginByAuthTypeReq"}}], "responses": {"200": {"description": "LoginResp", "schema": {"$ref": "#/definitions/LoginResp"}}}}}, "/v1/user/wxAuth": {"post": {"description": "WX Auth | 微信登录", "tags": ["user"], "summary": "WX Auth | 微信登录", "operationId": "WxAuth", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/WXAuthReq"}}], "responses": {"200": {"description": "WXAuthResp", "schema": {"$ref": "#/definitions/WXAuthResp"}}}}}, "/v1/user/wxMiniAuth": {"post": {"description": "WX Mini Auth | 微信小程序授权登录", "tags": ["user"], "summary": "WX Mini Auth | 微信小程序授权登录", "operationId": "WxMiniAuth", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/WXMiniAuthReq"}}], "responses": {"200": {"description": "WXMiniAuthResp", "schema": {"$ref": "#/definitions/WXMiniAuthResp"}}}}}, "/v1/verification": {"post": {"description": "Sms Verification | 短信验证", "tags": ["user"], "summary": "Sms Verification | 短信验证", "operationId": "Verification", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/VerificationReq"}}], "responses": {"200": {"description": "VerificationResp", "schema": {"$ref": "#/definitions/VerificationResp"}}}}}}, "definitions": {"ActivateLicenseReq": {"description": "Activate License | 激活License", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ActivateLicenseResp": {"description": "Activate License Response | 激活License响应", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "AddArticleViewRecordReq": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "AddArticleViewRecordResp": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "AddUserProductReq": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "AddUserProductResp": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "AdminArticleDeleteReq": {"description": "管理员删除文章", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "AdminBanUserReq": {"description": "管理员封禁用户", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "AdminDelReplyReq": {"description": "管理员删除评论", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "AdminMuteUserReq": {"description": "管理员禁言用户", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "AdminUnbanUserReq": {"description": "管理员解封用户", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "AdminUnmuteUserReq": {"description": "管理员解禁用户", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "AdsInfo": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "AlipaySuccessReq": {"description": "Alipay Success Request | 支付成功请求", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "AnonymousMallOrderListReq": {"description": "匿名订单列表", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "AnonymousMallOrderReq": {"description": "匿名订单", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "AppPackageNameReq": {"description": "Get mall product by App package name request | 通过应用包名获取商品信息请求", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "AppVersionCompareReq": {"description": "AppVersionCompare", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "AppVersionInfo": {"description": "The response data of app version information | AppVersion信息", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "AppVersionInfoReq": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "AppVersionInfoResp": {"description": "AppVersion information response | AppVersion信息返回体", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "AppVersionListInfo": {"description": "AppVersion list data | AppVersion列表数据", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "AppVersionListReq": {"description": "Get app version list request params | AppVersion列表请求参数", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "AppVersionListResp": {"description": "The response data of app version list | AppVersion列表数据", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "AppletUserInfoResp": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "AreaInfo": {"description": "The response data of area information | Area信息", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "AreaInfoResp": {"description": "Area information response | Area信息返回体", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "AreaListInfo": {"description": "Area list data | Area列表数据", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "AreaListReq": {"description": "Get area list request params | Area列表请求参数", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "AreaListResp": {"description": "The response data of area list | Area列表数据", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ArticlSaveDraftReq": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ArticlSaveDraftResp": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ArticleDeleteReq": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ArticleDetailReq": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ArticleDetailResp": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ArticleDraftListsReq": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ArticleDraftListsResp": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ArticleInfo": {"description": "The response data of article information | Article信息", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ArticleInfoResp": {"description": "Article information response | Article信息返回体", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ArticleLikeListReq": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ArticleLikeListResp": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ArticleListInfo": {"description": "Article list data | Article列表数据", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ArticleListReq": {"description": "Get article list request params | Article列表请求参数", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ArticleListResp": {"description": "The response data of article list | Article列表数据", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ArticleListsByGroupIdReq": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ArticleListsByTagIdReq": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ArticleListsReq": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ArticleListsResp": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "AuditedCallbackResp": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "AuthorInfo": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "BannerInfo": {"description": "The response data of banner information | Banner信息", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "BannerInfoResp": {"description": "Banner information response | Banner信息返回体", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "BannerListInfo": {"description": "Banner list data | Banner列表数据", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "BannerListReq": {"description": "Get banner list request params | Banner列表请求参数", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "BannerListResp": {"description": "The response data of banner list | Banner列表数据", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "BaseDataInfo": {"description": "The basic response with data | 基础带数据信息", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "BaseIDInfo": {"description": "The base ID response data | 基础ID信息", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "BaseIDInt32Info": {"description": "The base ID response data (int32) | 基础ID信息 (int32)", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "BaseIDInt64Info": {"description": "The base ID response data (int64) | 基础ID信息 (int64)", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "BaseIDUint32Info": {"description": "The base ID response data (uint32) | 基础ID信息 (uint32)", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "BaseListInfo": {"description": "The basic response with data | 基础带数据信息", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "BaseMsgResp": {"description": "The basic response without data | 基础不带数据信息", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "BaseUUIDInfo": {"description": "The base UUID response data | 基础UUID信息", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "BindDeviceReq": {"description": "Bind Device | 绑定设备", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "BindWxByMobileReq": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "BoloLexiconInfo": {"description": "The response data of bolo lexicon information | BoloLexicon信息", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "BoloLexiconInfoResp": {"description": "The bolo lexicon information response | BoloLexicon信息返回体", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "BoloLexiconListInfo": {"description": "The bolo lexicon list data | BoloLexicon信息列表数据", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "BoloLexiconListReq": {"description": "Get bolo lexicon list request params | BoloLexicon列表请求参数", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "BoloLexiconListResp": {"description": "The response data of bolo lexicon list | BoloLexicon信息列表数据", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "CityInfo": {"description": "The response data of city information | City信息", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "CityInfoResp": {"description": "City information response | City信息返回体", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "CityListInfo": {"description": "City list data | City列表数据", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "CityListReq": {"description": "Get city list request params | City列表请求参数", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "CityListResp": {"description": "The response data of city list | City列表数据", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "CommonResp": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ConfigurationInfo": {"description": "The response data of configuration information | 参数配置信息", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ConfigurationInfoResp": {"description": "Configuration information response | 参数配置信息返回体", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ConfigurationListInfo": {"description": "Configuration list data | 参数配置列表数据", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ConfigurationListReq": {"description": "Get configuration list request params | 参数配置列表请求参数", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ConfigurationListResp": {"description": "The response data of configuration list | 参数配置列表数据", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ConfigurationResp": {"description": "Get public system configuration response | 获取公开系统参数返回体", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ConfigurationtReq": {"description": "Get public system configuration request params | 获取公开系统参数请求参数", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "CreateAnonymousMallOrderReq": {"description": "匿名下单请求", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "CreateMallOrderReq": {"description": "创建订单", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "CreateMallOrderResp": {"description": "创建订单响应", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "CreateUserMobileSsoLoginSessionReq": {"description": "创建用户移动端sso登录session", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "CreateUserMobileSsoLoginSessionResp": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "DelReplyReq": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "DelReplyResp": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "DeleteUserMobileSsoLoginSessionReq": {"description": "删除用户移动端sso登录session", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "DeleteUserMobileSsoLoginSessionResp": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "EmptyReq": {"description": "Empty request | 无参数请求", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "FansListReq": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "FansListResp": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "FansUserInfo": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "FollowListReq": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "FollowListResp": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "FollowReq": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "FollowResp": {"description": "添加 BaseDataInfo", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "FollowUserInfo": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "FromUserInfo": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "GeetestVerifyReq": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "GeetestVerifyResp": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "GetMachineCodeReq": {"description": "Get Machine Code | 获取机器码", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "GetMachineCodeResp": {"description": "Get Machine Code Response | 获取机器码响应", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "GetMallProductByDeviceIdAndMachineCodeReq": {"description": "Get Mall Product By DeviceId and MachineCode | 通过设备ID和机器码获取商品SKU信息", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "GetMessageListReq": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "GetMessageListResp": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "GetSendSmsCodeReq": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "GetSendSmsCodeResp": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "GetUserAgreementByKeyReq": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "GetUserMobileSsoTicketReq": {"description": "获取用户移动端sso登录ticket", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "GetUserMobileSsoTicketResp": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "GetUserMobileSsoUserLoginInfoReq": {"description": "获取用户移动端sso登录用户信息", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "GetUserMobileSsoUserLoginInfoResp": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "GetUserProductListReq": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "GetUserProductListResp": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "GetUserSigReq": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "GetUserSigResp": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "GroupFollowInfo": {"description": "The response data of group follow information | GroupFollow信息", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "GroupFollowInfoResp": {"description": "GroupFollow information response | GroupFollow信息返回体", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "GroupFollowListInfo": {"description": "GroupFollow list data | GroupFollow列表数据", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "GroupFollowListReq": {"description": "Get group follow list request params | GroupFollow列表请求参数", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "GroupFollowListResp": {"description": "The response data of group follow list | GroupFollow列表数据", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "GroupInfo": {"description": "The response data of group information | Group信息", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "GroupInfoResp": {"description": "Group information response | Group信息返回体", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "GroupListInfo": {"description": "Group list data | Group列表数据", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "GroupListReq": {"description": "Get group list request params | Group列表请求参数", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "GroupListResp": {"description": "The response data of group list | Group列表数据", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "GroupMemberInfo": {"description": "The response data of group member information | GroupMember信息", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "GroupMemberInfoResp": {"description": "GroupMember information response | GroupMember信息返回体", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "GroupMemberListInfo": {"description": "GroupMember list data | GroupMember列表数据", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "GroupMemberListReq": {"description": "Get group member list request params | GroupMember列表请求参数", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "GroupMemberListResp": {"description": "The response data of group member list | GroupMember列表数据", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "HealthCheckResp": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "HeartbeatReq": {"description": "Heartbeat Request | 心跳请求", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "HeartbeatResp": {"description": "Heartbeat Response | 心跳响应", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "IDInt32PathReq": {"description": "Basic ID request (int32) | 基础ID地址参数请求 (int32)", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "IDInt32Req": {"description": "Basic ID request (int32) | 基础ID参数请求 (int32)", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "IDInt64PathReq": {"description": "Basic ID request (int64) | 基础ID地址参数请求 (int64)", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "IDInt64Req": {"description": "Basic ID request (int64) | 基础ID参数请求 (int64)", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "IDPathReq": {"description": "Basic ID request | 基础ID地址参数请求", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "IDReq": {"description": "Basic ID request | 基础ID参数请求", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "IDUint32PathReq": {"description": "Basic ID request (uint32) | 基础ID地址参数请求 (uint32)", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "IDUint32Req": {"description": "Basic ID request (uint32) | 基础ID参数请求 (uint32)", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "IDsInt32Req": {"description": "Basic IDs request (int32) | 基础ID数组参数请求 (int32)", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "IDsInt64Req": {"description": "Basic IDs request (int64) | 基础ID数组参数请求 (int64)", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "IDsReq": {"description": "Basic IDs request | 基础ID数组参数请求", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "IDsUint32Req": {"description": "Basic IDs request (uint32) | 基础ID数组参数请求 (uint32)", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ImageAuditedCallbackReq": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "IsThumbupReq": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "IsThumbupResp": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "LicenseInfo": {"description": "License | License信息", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "LoginReq": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "LoginResp": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "MaintenInfoInfo": {"description": "The response data of mainten info information | MaintenInfo信息", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "MaintenInfoInfoResp": {"description": "MaintenInfo information response | MaintenInfo信息返回体", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "MaintenInfoListInfo": {"description": "MaintenInfo list data | MaintenInfo列表数据", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "MaintenInfoListReq": {"description": "Get mainten info list request params | MaintenInfo列表请求参数", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "MaintenInfoListResp": {"description": "The response data of mainten info list | MaintenInfo列表数据", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "MallOrderInfo": {"description": "The response data of mall order information | 订单信息", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "MallOrderInfoResp": {"description": "The mall order information response | MallOrder信息返回体", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "MallOrderItemInfo": {"description": "The response data of mall order item information | MallOrderItem信息", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "MallOrderItemInfoResp": {"description": "The mall order item information response | MallOrderItem信息返回体", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "MallOrderItemListInfo": {"description": "The mall order item list data | MallOrderItem信息列表数据", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "MallOrderItemListReq": {"description": "Get mall order item list request params | MallOrderItem列表请求参数", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "MallOrderItemListResp": {"description": "The response data of mall order item list | MallOrderItem信息列表数据", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "MallOrderListInfo": {"description": "The mall order list data | 订单列表数据", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "MallOrderListReq": {"description": "Get mall order list request params | 订单列表请求参数", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "MallOrderListResp": {"description": "The response data of mall order list | 订单列表数据", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "MallOrderSubmitReq": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "MallOrderSubmitResp": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "MallProductInfo": {"description": "The response data of mall product information | 商品信息", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "MallProductInfoResp": {"description": "The mall product information response | 商品信息返回体", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "MallProductListInfo": {"description": "The mall product list data | 商品信息列表数据", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "MallProductListReq": {"description": "Get mall product list request params | 商品列表请求参数", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "MallProductListResp": {"description": "The response data of mall product list | 商品信息列表数据", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "MallProductResp": {"description": "The product information response | 商品信息返回体", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "MallProductSkuInfo": {"description": "The response data of mall product sku information | 商品SKU信息", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "MallProductSkuInfoResp": {"description": "The mall product sku information response | 商品SKU信息返回体", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "MallProductSkuListInfo": {"description": "The mall product sku list data | 商品SKU信息列表数据", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "MallProductSkuListReq": {"description": "Get mall product sku list request params | MallProductSku列表请求参数", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "MallProductSkuListResp": {"description": "The response data of mall product sku list | 商品SKU信息列表数据", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "MockNotifyReq": {"description": "Mock Payment Notify | 模拟支付回调", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "NotifyReq": {"description": "Payment Notify | 支付回调", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "PageInfo": {"description": "The page request parameters | 列表请求参数", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "PermCodeResp": {"description": "The permission code for front end permission control | 权限码： 用于前端权限控制", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "PoliticsInfo": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "PornInfo": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ProductInfo": {"description": "The response data of product information | Product信息", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ProductInfoResp": {"description": "Product information response | Product信息返回体", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ProductListInfo": {"description": "Product list data | Product列表数据", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ProductListReq": {"description": "Get product list request params | Product列表请求参数", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ProductListResp": {"description": "The response data of product list | Product列表数据", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ProvinceInfo": {"description": "The response data of province information | Province信息", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ProvinceInfoResp": {"description": "Province information response | Province信息返回体", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ProvinceListInfo": {"description": "Province list data | Province列表数据", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ProvinceListReq": {"description": "Get province list request params | Province列表请求参数", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ProvinceListResp": {"description": "The response data of province list | Province列表数据", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "PublishReq": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "PublishResp": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "QueryLicenseReq": {"description": "Query License | 查询License", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "QueryLicenseResp": {"description": "Query License Response | 查询License响应", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "QueryPaymentStatusReq": {"description": "查询支付状态请求", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "QueryPaymentStatusResp": {"description": "查询支付状态响应", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ReadMessageReq": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ReadMessageResp": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "RealNameAuthReq": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "RealNameAuthResp": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "RegisterByAuthIdReq": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "RegisterReq": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "RegisterResp": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "RentalDeviceInfo": {"description": "The response data of rental device information | RentalDevice信息", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "RentalDeviceInfoResp": {"description": "The rental device information response | RentalDevice信息返回体", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "RentalDeviceListInfo": {"description": "The rental device list data | RentalDevice信息列表数据", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "RentalDeviceListReq": {"description": "Get rental device list request params | RentalDevice列表请求参数", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "RentalDeviceListResp": {"description": "The response data of rental device list | RentalDevice信息列表数据", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ReplyArticleInfo": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ReplyListReq": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ReplyListResp": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ReplyReq": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ReplyResp": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ReplyUserInfo": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "SegmentArticleRecordInfo": {"description": "The response data of segment article record information | SegmentArticleRecord信息", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "SegmentArticleRecordInfoResp": {"description": "SegmentArticleRecord information response | SegmentArticleRecord信息返回体", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "SegmentArticleRecordListInfo": {"description": "SegmentArticleRecord list data | SegmentArticleRecord列表数据", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "SegmentArticleRecordListReq": {"description": "Get segment article record list request params | SegmentArticleRecord列表请求参数", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "SegmentArticleRecordListResp": {"description": "The response data of segment article record list | SegmentArticleRecord列表数据", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "SegmentInfo": {"description": "The response data of segment information | Segment信息", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "SegmentInfoResp": {"description": "Segment information response | Segment信息返回体", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "SegmentListInfo": {"description": "Segment list data | Segment列表数据", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "SegmentListReq": {"description": "Get segment list request params | Segment列表请求参数", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "SegmentListResp": {"description": "The response data of segment list | Segment列表数据", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "SendEmailCodeReq": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "SendEmailCodeResp": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ServerStatusReq": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ServerStatusResp": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ServerTimeResp": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "SetAppletUserInfoResp": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "SetAvatarReq": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "SetAvatarResp": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "SetBackgroundImageReq": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "SetBackgroundImageResp": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "SetNicknameReq": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "SetNicknameResp": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "SetPasswordReq": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "SetPasswordResp": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "SetUserDescriptionReq": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "SetUserDescriptionResp": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "SetUserEmailReq": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "SetUserEmailResp": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "SetUserHomePageReq": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "SetUserHomePageResp": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "SetUserInfoReq": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "SetUserSexReq": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "SetUserSexResp": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "StreetInfo": {"description": "The response data of street information | Street信息", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "StreetInfoResp": {"description": "Street information response | Street信息返回体", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "StreetListInfo": {"description": "Street list data | Street列表数据", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "StreetListReq": {"description": "Get street list request params | Street列表请求参数", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "StreetListResp": {"description": "The response data of street list | Street列表数据", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "TagFollowInfo": {"description": "The response data of tag follow information | TagFollow信息", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "TagFollowInfoResp": {"description": "TagFollow information response | TagFollow信息返回体", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "TagFollowListInfo": {"description": "TagFollow list data | TagFollow列表数据", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "TagFollowListReq": {"description": "Get tag follow list request params | TagFollow列表请求参数", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "TagFollowListResp": {"description": "The response data of tag follow list | TagFollow列表数据", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "TagInfo": {"description": "The response data of tag information | Tag信息", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "TagInfoResp": {"description": "Tag information response | Tag信息返回体", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "TagListInfo": {"description": "Tag list data | Tag列表数据", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "TagListReq": {"description": "Get tag list request params | Tag列表请求参数", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "TagListResp": {"description": "The response data of tag list | Tag列表数据", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "TecentCloudUploadPostPolicyReq": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "TecentCloudUploadPostPolicyResp": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "TecentCloudUploadTokenReq": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "TecentCloudUploadTokenResp": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "TerroristInfo": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ThumbupReq": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ThumbupResp": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "UUIDPathReq": {"description": "Basic UUID request in path | 基础UUID地址参数请求", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "UUIDReq": {"description": "Basic UUID request | 基础UUID参数请求", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "UUIDsReq": {"description": "Basic UUID array request | 基础UUID数组参数请求", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "UnFollowReq": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "UnFollowResp": {"description": "添加 BaseDataInfo", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "UnbindDeviceReq": {"description": "Unbind Device | 解绑设备", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "UpdatePasswordReq": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "UpdatePasswordResp": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "UploadCoverResp": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "UploadFilesResp": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "UserAddresInfo": {"description": "The response data of user addres information | UserAddres信息", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "UserAddresInfoResp": {"description": "UserAddres information response | UserAddres信息返回体", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "UserAddresListInfo": {"description": "UserAddres list data | UserAddres列表数据", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "UserAddresListReq": {"description": "Get user addres list request params | UserAddres列表请求参数", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "UserAddresListResp": {"description": "The response data of user addres list | UserAddres列表数据", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "UserAgreementInfo": {"description": "The response data of user agreement information | UserAgreement信息", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "UserAgreementInfoResp": {"description": "The user agreement information response | UserAgreement信息返回体", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "UserAgreementListInfo": {"description": "The user agreement list data | UserAgreement信息列表数据", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "UserAgreementListReq": {"description": "Get user agreement list request params | UserAgreement列表请求参数", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "UserAgreementListResp": {"description": "The response data of user agreement list | UserAgreement信息列表数据", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "UserArticleListsReq": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "UserInfo": {"description": "The response data of user information | User信息", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "UserInfoByMobileReq": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "UserInfoByNameReq": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "UserInfoByUserIdReq": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "UserInfoByUserIdResp": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "UserInfoReq": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "UserInfoResp": {"description": "User information response | User信息返回体", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "UserListInfo": {"description": "User list data | User列表数据", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "UserListReq": {"description": "Get user list request params | User列表请求参数", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "UserListResp": {"description": "The response data of user list | User列表数据", "x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "UserReplyListReq": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "VerificationReq": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "VerificationResp": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "VerifyEmailCodeReq": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "VerifyEmailCodeResp": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "VerifyLoginByAuthTypeReq": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "VerifyLoginReq": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "VideoAuditedCallbackReq": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ViewListReq": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ViewListResp": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "ViewUserInfo": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "WXAuthReq": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "WXAuthResp": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "WXMiniAuthReq": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}, "WXMiniAuthResp": {"x-go-package": "seevision.cn/server/meet-app-api/internal/types"}}, "securityDefinitions": {"Token": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header"}}, "security": [{"Token": ["[]"]}]}