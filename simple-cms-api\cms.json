{"consumes": ["application/json"], "produces": ["application/json"], "schemes": ["http", "https"], "swagger": "2.0", "info": {"description": "Description: cms service", "title": "cms", "version": "0.1.0"}, "host": "localhost:9107", "basePath": "/", "paths": {"/init/database": {"get": {"description": "Initialize database | 初始化数据库", "tags": ["base"], "summary": "Initialize database | 初始化数据库", "operationId": "InitDatabase", "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/websitecms/article": {"post": {"description": "Get article by ID | 通过ID获取文章", "tags": ["article"], "summary": "Get article by ID | 通过ID获取文章", "operationId": "GetArticleById", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/UUIDReq"}}], "responses": {"200": {"description": "ArticleInfoResp", "schema": {"$ref": "#/definitions/ArticleInfoResp"}}}}}, "/websitecms/article/create": {"post": {"description": "Create article information | 创建文章", "tags": ["article"], "summary": "Create article information | 创建文章", "operationId": "CreateArticle", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/ArticleInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/websitecms/article/delete": {"post": {"description": "Delete article information | 删除文章信息", "tags": ["article"], "summary": "Delete article information | 删除文章信息", "operationId": "DeleteArticle", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/UUIDsReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/websitecms/article/list": {"get": {"description": "Get public article list | 获取公开文章列表", "tags": ["publicapi"], "summary": "Get public article list | 获取公开文章列表", "operationId": "GetPublicArticleList", "parameters": [{"minimum": 0, "type": "integer", "format": "uint64", "x-go-name": "Page", "description": "Page number | 第几页", "name": "page", "in": "query", "required": true}, {"maximum": 100000, "type": "integer", "format": "uint64", "x-go-name": "PageSize", "description": "Page size | 单页数据行数", "name": "pageSize", "in": "query", "required": true}, {"type": "string", "x-go-name": "Title", "description": "Title | 文章标题", "name": "title", "in": "query"}, {"type": "string", "x-go-name": "SubTitle", "description": "Short title | 文章副标题", "name": "subTitle", "in": "query"}, {"type": "string", "x-go-name": "Keyword", "description": "Keyword | 文章关键字", "name": "keyword", "in": "query"}, {"type": "integer", "format": "uint64", "x-go-name": "CategoryId", "description": "Category | 文章栏目 ID", "name": "categoryId", "in": "query"}, {"type": "array", "items": {"type": "integer", "format": "uint64"}, "x-go-name": "TagIds", "description": "Tags | 文章标签", "name": "tagIds", "in": "query"}, {"type": "string", "x-go-name": "Sort", "description": "Sort | 排序方式", "name": "sort", "in": "query"}], "responses": {"200": {"description": "PublicArticleListResp", "schema": {"$ref": "#/definitions/PublicArticleListResp"}}}}, "post": {"description": "Get article list | 获取文章列表", "tags": ["article"], "summary": "Get article list | 获取文章列表", "operationId": "GetArticleList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/ArticleListReq"}}], "responses": {"200": {"description": "ArticleListResp", "schema": {"$ref": "#/definitions/ArticleListResp"}}}}}, "/websitecms/article/update": {"post": {"description": "Update article information | 更新文章", "tags": ["article"], "summary": "Update article information | 更新文章", "operationId": "UpdateArticle", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/ArticleInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/websitecms/article/{id}": {"get": {"description": "Get public Article by ID | 通过 ID 获取公开文章", "tags": ["publicapi"], "summary": "Get public Article by ID | 通过 ID 获取公开文章", "operationId": "GetPublicArticleById", "parameters": [{"type": "string", "x-go-name": "Id", "description": "ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "PublicArticleInfoResp", "schema": {"$ref": "#/definitions/PublicArticleInfoResp"}}}}}, "/websitecms/article_category": {"post": {"description": "Get category by ID | 通过ID获取栏目", "tags": ["category"], "summary": "Get category by ID | 通过ID获取栏目", "operationId": "GetCategoryById", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDReq"}}], "responses": {"200": {"description": "CategoryInfoResp", "schema": {"$ref": "#/definitions/CategoryInfoResp"}}}}}, "/websitecms/article_category/create": {"post": {"description": "Create category information | 创建栏目", "tags": ["category"], "summary": "Create category information | 创建栏目", "operationId": "CreateCategory", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/CategoryInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/websitecms/article_category/delete": {"post": {"description": "Delete category information | 删除栏目信息", "tags": ["category"], "summary": "Delete category information | 删除栏目信息", "operationId": "DeleteCategory", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDsReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/websitecms/article_category/list": {"get": {"description": "Get public category list | 获取公开栏目列表", "tags": ["publicapi"], "summary": "Get public category list | 获取公开栏目列表", "operationId": "GetPublicCategoryList", "parameters": [{"minimum": 0, "type": "integer", "format": "uint64", "x-go-name": "Page", "description": "Page number | 第几页", "name": "page", "in": "query", "required": true}, {"maximum": 100000, "type": "integer", "format": "uint64", "x-go-name": "PageSize", "description": "Page size | 单页数据行数", "name": "pageSize", "in": "query", "required": true}, {"type": "string", "x-go-name": "Title", "description": "Title | 标题", "name": "title", "in": "query"}, {"type": "string", "x-go-name": "SubTitle", "description": "The sub title of category | 栏目副标题", "name": "subTitle", "in": "query"}], "responses": {"200": {"description": "CategoryListResp", "schema": {"$ref": "#/definitions/CategoryListResp"}}}}, "post": {"description": "Get category list | 获取栏目列表", "tags": ["category"], "summary": "Get category list | 获取栏目列表", "operationId": "GetCategoryList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/CategoryListReq"}}], "responses": {"200": {"description": "CategoryListResp", "schema": {"$ref": "#/definitions/CategoryListResp"}}}}}, "/websitecms/article_category/update": {"post": {"description": "Update category information | 更新栏目", "tags": ["category"], "summary": "Update category information | 更新栏目", "operationId": "UpdateCategory", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/CategoryInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/websitecms/article_category/{id}": {"get": {"description": "Get public category by ID | 通过ID获取公开栏目", "tags": ["publicapi"], "summary": "Get public category by ID | 通过ID获取公开栏目", "operationId": "GetPublicCategoryById", "parameters": [{"type": "integer", "format": "uint64", "x-go-name": "Id", "description": "ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "CategoryInfoResp", "schema": {"$ref": "#/definitions/CategoryInfoResp"}}}}}, "/websitecms/article_comment": {"post": {"description": "Get article comment by ID | 通过ID获取文章评论", "tags": ["articlecomment"], "summary": "Get article comment by ID | 通过ID获取文章评论", "operationId": "GetArticleCommentById", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/UUIDReq"}}], "responses": {"200": {"description": "ArticleCommentInfoResp", "schema": {"$ref": "#/definitions/ArticleCommentInfoResp"}}}}}, "/websitecms/article_comment/add": {"post": {"description": "Add article comment information | 添加文章评论", "tags": ["articlecomment"], "summary": "Add article comment information | 添加文章评论", "operationId": "AddArticleComment", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/ArticleCommentReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/websitecms/article_comment/article": {"post": {"description": "Get comments by article ID | 通过文章 ID  获取评论", "tags": ["publicapi"], "summary": "Get comments by article ID | 通过文章 ID  获取评论", "operationId": "GetCommentByArticleId", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/PublicArticleCommentListReq"}}], "responses": {"200": {"description": "ArticleCommentListResp", "schema": {"$ref": "#/definitions/ArticleCommentListResp"}}}}}, "/websitecms/article_comment/create": {"post": {"description": "Create article comment information | 创建文章评论", "tags": ["articlecomment"], "summary": "Create article comment information | 创建文章评论", "operationId": "CreateArticleComment", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/ArticleCommentInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/websitecms/article_comment/delete": {"post": {"description": "Delete article comment information | 删除文章评论信息", "tags": ["articlecomment"], "summary": "Delete article comment information | 删除文章评论信息", "operationId": "DeleteArticleComment", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/UUIDsReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/websitecms/article_comment/list": {"post": {"description": "Get article comment list | 获取文章评论列表", "tags": ["articlecomment"], "summary": "Get article comment list | 获取文章评论列表", "operationId": "GetArticleCommentList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/ArticleCommentListReq"}}], "responses": {"200": {"description": "ArticleCommentListResp", "schema": {"$ref": "#/definitions/ArticleCommentListResp"}}}}}, "/websitecms/article_comment/update": {"post": {"description": "Update article comment information | 更新文章评论", "tags": ["articlecomment"], "summary": "Update article comment information | 更新文章评论", "operationId": "UpdateArticleComment", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/ArticleCommentInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/websitecms/article_tag": {"post": {"description": "Get tag by ID | 通过ID获取文章标签", "tags": ["tag"], "summary": "Get tag by ID | 通过ID获取文章标签", "operationId": "GetTagById", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDReq"}}], "responses": {"200": {"description": "TagInfoResp", "schema": {"$ref": "#/definitions/TagInfoResp"}}}}}, "/websitecms/article_tag/create": {"post": {"description": "Create tag information | 创建文章标签", "tags": ["tag"], "summary": "Create tag information | 创建文章标签", "operationId": "CreateTag", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/TagInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/websitecms/article_tag/delete": {"post": {"description": "Delete tag information | 删除文章标签信息", "tags": ["tag"], "summary": "Delete tag information | 删除文章标签信息", "operationId": "DeleteTag", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDsReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/websitecms/article_tag/list": {"post": {"description": "Get tag list | 获取文章标签列表", "tags": ["tag"], "summary": "Get tag list | 获取文章标签列表", "operationId": "GetTagList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/TagListReq"}}], "responses": {"200": {"description": "TagListResp", "schema": {"$ref": "#/definitions/TagListResp"}}}}}, "/websitecms/article_tag/update": {"post": {"description": "Update tag information | 更新文章标签", "tags": ["tag"], "summary": "Update tag information | 更新文章标签", "operationId": "UpdateTag", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/TagInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/websitecms/history": {"post": {"description": "Get history by ID | 通过ID获取History", "tags": ["history"], "summary": "Get history by ID | 通过ID获取History", "operationId": "GetHistoryById", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDReq"}}], "responses": {"200": {"description": "HistoryInfoResp", "schema": {"$ref": "#/definitions/HistoryInfoResp"}}}}}, "/websitecms/history/create": {"post": {"description": "Create history information | 创建History", "tags": ["history"], "summary": "Create history information | 创建History", "operationId": "CreateHistory", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/HistoryInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/websitecms/history/delete": {"post": {"description": "Delete history information | 删除History信息", "tags": ["history"], "summary": "Delete history information | 删除History信息", "operationId": "DeleteHistory", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDsReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/websitecms/history/list": {"get": {"description": "Get public History list | 获取History列表", "tags": ["publicapi"], "summary": "Get public History list | 获取History列表", "operationId": "GetPublicHistoryList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/PublicHistoryListReq"}}], "responses": {"200": {"description": "HistoryListResp", "schema": {"$ref": "#/definitions/HistoryListResp"}}}}, "post": {"description": "Get history list | 获取History列表", "tags": ["history"], "summary": "Get history list | 获取History列表", "operationId": "GetHistoryList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/HistoryListReq"}}], "responses": {"200": {"description": "HistoryListResp", "schema": {"$ref": "#/definitions/HistoryListResp"}}}}}, "/websitecms/history/update": {"post": {"description": "Update history information | 更新History", "tags": ["history"], "summary": "Update history information | 更新History", "operationId": "UpdateHistory", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/HistoryInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}}, "definitions": {"ArticleCommentInfo": {"description": "The response data of article comment information | 文章评论信息", "type": "object", "properties": {"articleId": {"description": "Article ID | 文章 ID", "type": "string", "x-go-name": "ArticleId"}, "content": {"description": "Content | 内容", "type": "string", "x-go-name": "Content"}, "createdAt": {"description": "Create date | 创建日期", "type": "integer", "format": "int64", "x-go-name": "CreatedAt"}, "formatCreatedAt": {"description": "Format Created Date | 格式化后的创建日期", "type": "string", "x-go-name": "FormatCreatedAt"}, "id": {"description": "ID", "type": "string", "x-go-name": "Id"}, "nickname": {"description": "Nickname | 昵称", "type": "string", "x-go-name": "Nickname"}, "parentId": {"description": "ParentId | 父级 ID", "type": "string", "x-go-name": "ParentId"}, "status": {"description": "Status | 状态", "type": "integer", "format": "uint8", "x-go-name": "Status"}, "updatedAt": {"description": "Update date | 更新日期", "type": "integer", "format": "int64", "x-go-name": "UpdatedAt"}, "userId": {"description": "UserId | 用户 ID", "type": "string", "x-go-name": "UserId"}}, "x-go-package": "github.com/suyuan32/simple-cms/internal/types"}, "ArticleCommentInfoResp": {"description": "ArticleComment information response | 文章评论信息返回体", "type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/ArticleCommentInfo"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "github.com/suyuan32/simple-cms/internal/types"}, "ArticleCommentListInfo": {"description": "ArticleComment list data | 文章评论列表数据", "type": "object", "properties": {"data": {"description": "The API list data | 文章评论列表数据", "type": "array", "items": {"$ref": "#/definitions/ArticleCommentInfo"}, "x-go-name": "Data"}, "total": {"description": "The total number of data | 数据总数", "type": "integer", "format": "uint64", "x-go-name": "Total"}}, "x-go-package": "github.com/suyuan32/simple-cms/internal/types"}, "ArticleCommentListReq": {"description": "Get article comment list request params | 文章评论列表请求参数", "type": "object", "required": ["page", "pageSize"], "properties": {"content": {"description": "Content", "type": "string", "x-go-name": "Content"}, "page": {"description": "Page number | 第几页", "type": "integer", "format": "uint64", "minimum": 0, "x-go-name": "Page"}, "pageSize": {"description": "Page size | 单页数据行数", "type": "integer", "format": "uint64", "maximum": 100000, "x-go-name": "PageSize"}}, "x-go-package": "github.com/suyuan32/simple-cms/internal/types"}, "ArticleCommentListResp": {"description": "The response data of article comment list | 文章评论列表数据", "type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/ArticleCommentListInfo"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "github.com/suyuan32/simple-cms/internal/types"}, "ArticleCommentReq": {"description": "The request data of article comment information | 文章评论请求", "type": "object", "properties": {"articleId": {"description": "Article ID | 文章 ID", "type": "string", "x-go-name": "ArticleId"}, "content": {"description": "Content | 内容", "type": "string", "x-go-name": "Content"}, "nickname": {"description": "Nickname | 昵称", "type": "string", "x-go-name": "Nickname"}, "parentId": {"description": "ParentId | 父级 ID", "type": "string", "x-go-name": "ParentId"}}, "x-go-package": "github.com/suyuan32/simple-cms/internal/types"}, "ArticleInfo": {"description": "The response data of article information | 文章信息", "type": "object", "properties": {"author": {"description": "Author | 文章作者", "type": "string", "x-go-name": "Author"}, "categoryId": {"description": "Category ID | 栏目ID", "type": "integer", "format": "uint64", "x-go-name": "CategoryId"}, "content": {"description": "Content | 文章内容", "type": "string", "x-go-name": "Content"}, "createdAt": {"description": "Create date | 创建日期", "type": "integer", "format": "int64", "x-go-name": "CreatedAt"}, "hit": {"description": "Hit | 点击数", "type": "integer", "format": "uint32", "x-go-name": "Hit"}, "id": {"description": "ID", "type": "string", "x-go-name": "Id"}, "img": {"description": "The cover image of the article | 文章封面图", "type": "string", "x-go-name": "Img"}, "introduction": {"description": "Introduction | 文章简介", "type": "string", "x-go-name": "Introduction"}, "isRecommended": {"description": "IsRecommended | 是否推荐", "type": "boolean", "x-go-name": "IsRecommended"}, "keyword": {"description": "Keyword | 文章关键字", "type": "string", "x-go-name": "Keyword"}, "source": {"description": "Source | 文章来源", "type": "string", "x-go-name": "Source"}, "status": {"description": "Status", "type": "integer", "format": "uint8", "x-go-name": "Status"}, "subTitle": {"description": "SubTitle | 文章副标题", "type": "string", "x-go-name": "SubTitle"}, "tagIds": {"description": "Tags | 文章标签", "type": "array", "items": {"type": "integer", "format": "uint64"}, "x-go-name": "TagIds"}, "thumbnail": {"description": "The thumbnail of the cover image | 封面图缩略图", "type": "string", "x-go-name": "<PERSON><PERSON><PERSON><PERSON>"}, "title": {"description": "Title | 文章标题", "type": "string", "x-go-name": "Title"}, "updatedAt": {"description": "Update date | 更新日期", "type": "integer", "format": "int64", "x-go-name": "UpdatedAt"}}, "x-go-package": "github.com/suyuan32/simple-cms/internal/types"}, "ArticleInfoResp": {"description": "Article information response | 文章信息返回体", "type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/ArticleInfo"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "github.com/suyuan32/simple-cms/internal/types"}, "ArticleListInfo": {"description": "Article list data | 文章列表数据", "type": "object", "properties": {"data": {"description": "The API list data | 文章列表数据", "type": "array", "items": {"$ref": "#/definitions/ArticleInfo"}, "x-go-name": "Data"}, "total": {"description": "The total number of data | 数据总数", "type": "integer", "format": "uint64", "x-go-name": "Total"}}, "x-go-package": "github.com/suyuan32/simple-cms/internal/types"}, "ArticleListReq": {"description": "Get article list request params | 文章列表请求参数", "type": "object", "required": ["page", "pageSize"], "properties": {"categoryId": {"description": "Category | 文章栏目 ID", "type": "integer", "format": "uint64", "x-go-name": "CategoryId"}, "keyword": {"description": "Keyword | 文章关键字", "type": "string", "x-go-name": "Keyword"}, "page": {"description": "Page number | 第几页", "type": "integer", "format": "uint64", "minimum": 0, "x-go-name": "Page"}, "pageSize": {"description": "Page size | 单页数据行数", "type": "integer", "format": "uint64", "maximum": 100000, "x-go-name": "PageSize"}, "sort": {"description": "Sort | 排序方式", "type": "string", "x-go-name": "Sort"}, "subTitle": {"description": "Short title | 文章副标题", "type": "string", "x-go-name": "SubTitle"}, "tagIds": {"description": "Tags | 文章标签", "type": "array", "items": {"type": "integer", "format": "uint64"}, "x-go-name": "TagIds"}, "title": {"description": "Title | 文章标题", "type": "string", "x-go-name": "Title"}}, "x-go-package": "github.com/suyuan32/simple-cms/internal/types"}, "ArticleListResp": {"description": "The response data of article list | 文章列表数据", "type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/ArticleListInfo"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "github.com/suyuan32/simple-cms/internal/types"}, "BaseDataInfo": {"description": "The basic response with data | 基础带数据信息", "type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "github.com/suyuan32/simple-cms/internal/types"}, "BaseIDInfo": {"description": "The base ID response data | 基础ID信息", "type": "object", "properties": {"createdAt": {"description": "Create date | 创建日期", "type": "integer", "format": "int64", "x-go-name": "CreatedAt"}, "id": {"description": "ID", "type": "integer", "format": "uint64", "x-go-name": "Id"}, "updatedAt": {"description": "Update date | 更新日期", "type": "integer", "format": "int64", "x-go-name": "UpdatedAt"}}, "x-go-package": "github.com/suyuan32/simple-cms/internal/types"}, "BaseListInfo": {"description": "The basic response with data | 基础带数据信息", "type": "object", "properties": {"data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data"}, "total": {"description": "The total number of data | 数据总数", "type": "integer", "format": "uint64", "x-go-name": "Total"}}, "x-go-package": "github.com/suyuan32/simple-cms/internal/types"}, "BaseMsgResp": {"description": "The basic response without data | 基础不带数据信息", "type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "github.com/suyuan32/simple-cms/internal/types"}, "BaseUUIDInfo": {"description": "The base UUID response data | 基础UUID信息", "type": "object", "properties": {"createdAt": {"description": "Create date | 创建日期", "type": "integer", "format": "int64", "x-go-name": "CreatedAt"}, "id": {"description": "ID", "type": "string", "x-go-name": "Id"}, "updatedAt": {"description": "Update date | 更新日期", "type": "integer", "format": "int64", "x-go-name": "UpdatedAt"}}, "x-go-package": "github.com/suyuan32/simple-cms/internal/types"}, "CategoryInfo": {"description": "The response data of category information | 栏目信息", "type": "object", "properties": {"banner": {"description": "Banner | 栏目 Banner 图", "type": "string", "x-go-name": "Banner"}, "createdAt": {"description": "Create date | 创建日期", "type": "integer", "format": "int64", "x-go-name": "CreatedAt"}, "description": {"description": "Description | 描述", "type": "string", "x-go-name": "Description"}, "id": {"description": "ID", "type": "integer", "format": "uint64", "x-go-name": "Id"}, "isLeaf": {"description": "Whether if leaf node | 是否为叶子节点", "type": "boolean", "x-go-name": "<PERSON><PERSON><PERSON><PERSON>"}, "parentId": {"description": "ParentId | 父级 ID", "type": "integer", "format": "uint64", "x-go-name": "ParentId"}, "sort": {"description": "Sort | 排序", "type": "integer", "format": "uint32", "x-go-name": "Sort"}, "state": {"description": "State | 状态", "type": "boolean", "x-go-name": "State"}, "subTitle": {"description": "The sub title of category | 栏目副标题", "type": "string", "x-go-name": "SubTitle"}, "title": {"description": "Title | 栏目标题", "type": "string", "x-go-name": "Title"}, "updatedAt": {"description": "Update date | 更新日期", "type": "integer", "format": "int64", "x-go-name": "UpdatedAt"}}, "x-go-package": "github.com/suyuan32/simple-cms/internal/types"}, "CategoryInfoResp": {"description": "Category information response | 栏目信息返回体", "type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/CategoryInfo"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "github.com/suyuan32/simple-cms/internal/types"}, "CategoryListInfo": {"description": "Category list data | 栏目列表数据", "type": "object", "properties": {"data": {"description": "The API list data | 栏目列表数据", "type": "array", "items": {"$ref": "#/definitions/CategoryInfo"}, "x-go-name": "Data"}, "total": {"description": "The total number of data | 数据总数", "type": "integer", "format": "uint64", "x-go-name": "Total"}}, "x-go-package": "github.com/suyuan32/simple-cms/internal/types"}, "CategoryListReq": {"description": "Get category list request params | 栏目列表请求参数", "type": "object", "required": ["page", "pageSize"], "properties": {"page": {"description": "Page number | 第几页", "type": "integer", "format": "uint64", "minimum": 0, "x-go-name": "Page"}, "pageSize": {"description": "Page size | 单页数据行数", "type": "integer", "format": "uint64", "maximum": 100000, "x-go-name": "PageSize"}, "subTitle": {"description": "The sub title of category | 栏目副标题", "type": "string", "x-go-name": "SubTitle"}, "title": {"description": "Title | 标题", "type": "string", "x-go-name": "Title"}}, "x-go-package": "github.com/suyuan32/simple-cms/internal/types"}, "CategoryListResp": {"description": "The response data of category list | 栏目列表数据", "type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/CategoryListInfo"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "github.com/suyuan32/simple-cms/internal/types"}, "HistoryInfo": {"description": "The response data of history information | History信息", "type": "object", "properties": {"content": {"description": "内容", "type": "string", "x-go-name": "Content"}, "createTime": {"description": "创建时间", "type": "integer", "format": "int64", "x-go-name": "CreateTime"}, "deleteTime": {"description": "删除时间", "type": "integer", "format": "int64", "x-go-name": "DeleteTime"}, "enContent": {"description": "英文内容", "type": "string", "x-go-name": "En<PERSON><PERSON>nt"}, "enTitle": {"description": "英文标题", "type": "string", "x-go-name": "EnTitle"}, "group": {"description": "分组", "type": "string", "x-go-name": "Group"}, "id": {"type": "integer", "format": "uint64", "x-go-name": "Id"}, "sort": {"description": "排序", "type": "integer", "format": "int8", "x-go-name": "Sort"}, "title": {"description": "标题", "type": "string", "x-go-name": "Title"}, "updateTime": {"description": "更新时间", "type": "integer", "format": "int64", "x-go-name": "UpdateTime"}}, "x-go-package": "github.com/suyuan32/simple-cms/internal/types"}, "HistoryInfoResp": {"description": "History information response | History信息返回体", "type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/HistoryInfo"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "github.com/suyuan32/simple-cms/internal/types"}, "HistoryListInfo": {"description": "History list data | History列表数据", "type": "object", "properties": {"data": {"description": "The API list data | History列表数据", "type": "array", "items": {"$ref": "#/definitions/HistoryInfo"}, "x-go-name": "Data"}, "total": {"description": "The total number of data | 数据总数", "type": "integer", "format": "uint64", "x-go-name": "Total"}}, "x-go-package": "github.com/suyuan32/simple-cms/internal/types"}, "HistoryListReq": {"description": "Get history list request params | History列表请求参数", "type": "object", "required": ["page", "pageSize"], "properties": {"content": {"description": "内容", "type": "string", "x-go-name": "Content"}, "group": {"description": "分组", "type": "string", "x-go-name": "Group"}, "page": {"description": "Page number | 第几页", "type": "integer", "format": "uint64", "minimum": 0, "x-go-name": "Page"}, "pageSize": {"description": "Page size | 单页数据行数", "type": "integer", "format": "uint64", "maximum": 100000, "x-go-name": "PageSize"}, "title": {"description": "标题", "type": "string", "x-go-name": "Title"}}, "x-go-package": "github.com/suyuan32/simple-cms/internal/types"}, "HistoryListResp": {"description": "The response data of history list | History列表数据", "type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/HistoryListInfo"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "github.com/suyuan32/simple-cms/internal/types"}, "IDReq": {"description": "Basic ID request | 基础ID参数请求", "type": "object", "required": ["id"], "properties": {"id": {"description": "ID", "type": "integer", "format": "uint64", "x-go-name": "Id"}}, "x-go-package": "github.com/suyuan32/simple-cms/internal/types"}, "IDsReq": {"description": "Basic IDs request | 基础ID数组参数请求", "type": "object", "required": ["ids"], "properties": {"ids": {"description": "IDs", "type": "array", "items": {"type": "integer", "format": "uint64"}, "x-go-name": "Ids"}}, "x-go-package": "github.com/suyuan32/simple-cms/internal/types"}, "PageInfo": {"description": "The page request parameters | 列表请求参数", "type": "object", "required": ["page", "pageSize"], "properties": {"page": {"description": "Page number | 第几页", "type": "integer", "format": "uint64", "minimum": 0, "x-go-name": "Page"}, "pageSize": {"description": "Page size | 单页数据行数", "type": "integer", "format": "uint64", "maximum": 100000, "x-go-name": "PageSize"}}, "x-go-package": "github.com/suyuan32/simple-cms/internal/types"}, "PublicArticleCommentListReq": {"description": "Get article comment list request params | 文章评论列表请求参数", "type": "object", "required": ["page", "pageSize"], "properties": {"articleId": {"description": "Content", "type": "string", "x-go-name": "ArticleId"}, "page": {"description": "Page number | 第几页", "type": "integer", "format": "uint64", "minimum": 0, "x-go-name": "Page"}, "pageSize": {"description": "Page size | 单页数据行数", "type": "integer", "format": "uint64", "maximum": 100000, "x-go-name": "PageSize"}}, "x-go-package": "github.com/suyuan32/simple-cms/internal/types"}, "PublicArticleInfo": {"description": "The response data of public article information | 公开的文章信息", "type": "object", "properties": {"author": {"description": "Author | 文章作者", "type": "string", "x-go-name": "Author"}, "category": {"description": "Category  | 栏目名称", "type": "string", "x-go-name": "Category"}, "content": {"description": "Content | 文章内容", "type": "string", "x-go-name": "Content"}, "createdAt": {"description": "Create date | 创建日期", "type": "integer", "format": "int64", "x-go-name": "CreatedAt"}, "hit": {"description": "Hit | 点击数", "type": "integer", "format": "uint32", "x-go-name": "Hit"}, "id": {"description": "ID", "type": "string", "x-go-name": "Id"}, "img": {"description": "The cover image of the article | 文章封面图", "type": "string", "x-go-name": "Img"}, "introduction": {"description": "Introduction | 文章简介", "type": "string", "x-go-name": "Introduction"}, "keyword": {"description": "Keyword | 文章关键字", "type": "string", "x-go-name": "Keyword"}, "source": {"description": "Source | 文章来源", "type": "string", "x-go-name": "Source"}, "subTitle": {"description": "SubTitle | 文章副标题", "type": "string", "x-go-name": "SubTitle"}, "tags": {"description": "Tags | 文章标签", "type": "array", "items": {"type": "string"}, "x-go-name": "Tags"}, "thumbnail": {"description": "The thumbnail of the cover image | 封面图缩略图", "type": "string", "x-go-name": "<PERSON><PERSON><PERSON><PERSON>"}, "title": {"description": "Title | 文章标题", "type": "string", "x-go-name": "Title"}, "updatedAt": {"description": "Update date | 更新日期", "type": "integer", "format": "int64", "x-go-name": "UpdatedAt"}}, "x-go-package": "github.com/suyuan32/simple-cms/internal/types"}, "PublicArticleInfoResp": {"description": "Article information response | 文章信息返回体", "type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/PublicArticleInfo"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "github.com/suyuan32/simple-cms/internal/types"}, "PublicArticleListInfo": {"description": "Article list data | 文章列表数据", "type": "object", "properties": {"data": {"description": "The API list data | 文章列表数据", "type": "array", "items": {"$ref": "#/definitions/PublicArticleInfo"}, "x-go-name": "Data"}, "total": {"description": "The total number of data | 数据总数", "type": "integer", "format": "uint64", "x-go-name": "Total"}}, "x-go-package": "github.com/suyuan32/simple-cms/internal/types"}, "PublicArticleListResp": {"description": "The response data of article list | 文章列表数据", "type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/PublicArticleListInfo"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "github.com/suyuan32/simple-cms/internal/types"}, "PublicHistoryListReq": {"type": "object", "required": ["page", "pageSize"], "properties": {"content": {"description": "内容", "type": "string", "x-go-name": "Content"}, "group": {"description": "分组", "type": "string", "x-go-name": "Group"}, "page": {"description": "Page number | 第几页\nin: query", "type": "integer", "format": "uint64", "minimum": 0, "x-go-name": "Page"}, "pageSize": {"description": "Page size | 单页数据行数\nin: query", "type": "integer", "format": "uint64", "maximum": 100000, "x-go-name": "PageSize"}, "title": {"description": "标题", "type": "string", "x-go-name": "Title"}}, "x-go-package": "github.com/suyuan32/simple-cms/internal/types"}, "TagInfo": {"description": "The response data of tag information | 文章标签信息", "type": "object", "properties": {"createdAt": {"description": "Create date | 创建日期", "type": "integer", "format": "int64", "x-go-name": "CreatedAt"}, "id": {"description": "ID", "type": "integer", "format": "uint64", "x-go-name": "Id"}, "title": {"description": "Title", "type": "string", "x-go-name": "Title"}, "updatedAt": {"description": "Update date | 更新日期", "type": "integer", "format": "int64", "x-go-name": "UpdatedAt"}}, "x-go-package": "github.com/suyuan32/simple-cms/internal/types"}, "TagInfoResp": {"description": "Tag information response | 文章标签信息返回体", "type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/TagInfo"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "github.com/suyuan32/simple-cms/internal/types"}, "TagListInfo": {"description": "Tag list data | 文章标签列表数据", "type": "object", "properties": {"data": {"description": "The API list data | 文章标签列表数据", "type": "array", "items": {"$ref": "#/definitions/TagInfo"}, "x-go-name": "Data"}, "total": {"description": "The total number of data | 数据总数", "type": "integer", "format": "uint64", "x-go-name": "Total"}}, "x-go-package": "github.com/suyuan32/simple-cms/internal/types"}, "TagListReq": {"description": "Get tag list request params | 文章标签列表请求参数", "type": "object", "required": ["page", "pageSize"], "properties": {"page": {"description": "Page number | 第几页", "type": "integer", "format": "uint64", "minimum": 0, "x-go-name": "Page"}, "pageSize": {"description": "Page size | 单页数据行数", "type": "integer", "format": "uint64", "maximum": 100000, "x-go-name": "PageSize"}, "title": {"description": "Title", "type": "string", "x-go-name": "Title"}}, "x-go-package": "github.com/suyuan32/simple-cms/internal/types"}, "TagListResp": {"description": "The response data of tag list | 文章标签列表数据", "type": "object", "properties": {"code": {"description": "Error code | 错误代码", "type": "integer", "format": "int64", "x-go-name": "Code"}, "data": {"description": "Data | 数据", "type": "string", "x-go-name": "Data", "$ref": "#/definitions/TagListInfo"}, "msg": {"description": "Message | 提示信息", "type": "string", "x-go-name": "Msg"}}, "x-go-package": "github.com/suyuan32/simple-cms/internal/types"}, "UUIDReq": {"description": "Basic UUID request | 基础UUID参数请求", "type": "object", "required": ["id"], "properties": {"id": {"description": "ID", "type": "string", "maxLength": 36, "minLength": 36, "x-go-name": "Id"}}, "x-go-package": "github.com/suyuan32/simple-cms/internal/types"}, "UUIDsReq": {"description": "Basic UUID array request | 基础UUID数组参数请求", "type": "object", "required": ["ids"], "properties": {"ids": {"description": "Ids", "type": "array", "items": {"type": "string"}, "x-go-name": "Ids"}}, "x-go-package": "github.com/suyuan32/simple-cms/internal/types"}}, "securityDefinitions": {"Token": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header"}}, "security": [{"Token": ["[]"]}]}