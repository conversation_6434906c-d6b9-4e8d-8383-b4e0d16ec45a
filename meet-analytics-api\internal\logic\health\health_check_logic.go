package health

import (
	"context"
	"time"

	"seevision.cn/server/meet-analytics-api/internal/svc"
	"seevision.cn/server/meet-analytics-api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type HealthCheckLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewHealthCheckLogic(ctx context.Context, svcCtx *svc.ServiceContext) *HealthCheckLogic {
	return &HealthCheckLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx}
}

func (l *HealthCheckLogic) HealthCheck() (resp *types.HealthCheckResp, err error) {
	return &types.HealthCheckResp{
		Status:    "ok",
		Timestamp: time.Now().Unix(),
		Version:   "v1.0.0",
		ServiceInfo: types.ServiceInfo{
			Name:         "meet-analytics-api",
			BuildVersion: "v1.0.0",
			BuildTime:    time.Now().Format("2006-01-02 15:04:05"),
			GoVersion:    "go1.21",
		},
	}, nil
}
