{"consumes": ["application/json"], "produces": ["application/json"], "schemes": ["http", "https"], "swagger": "2.0", "info": {"description": "Description: meet_analytics_api service", "title": "meet_analytics_api", "version": "0.0.1"}, "host": "localhost:40006", "basePath": "/", "paths": {"/app/v1/analytics/health": {"get": {"description": "健康检查 | Health check", "tags": ["analytics"], "summary": "健康检查 | Health check", "operationId": "HealthCheck", "responses": {"200": {"description": "HealthCheckResp", "schema": {"$ref": "#/definitions/HealthCheckResp"}}}}}, "/app/v1/analytics/report": {"post": {"description": "通用事件上报 | Universal event report // 支持所有类型的事件上报：用户行为、业务事件、系统事件、用户信息、设备信息等", "tags": ["analytics"], "summary": "通用事件上报 | Universal event report // 支持所有类型的事件上报：用户行为、业务事件、系统事件、用户信息、设备信息等", "operationId": "UniversalReport", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/UniversalReportReq"}}], "responses": {"200": {"description": "EventReportResp", "schema": {"$ref": "#/definitions/EventReportResp"}}}}}, "/init/database": {"get": {"description": "Initialize database | 初始化数据库", "tags": ["base"], "summary": "Initialize database | 初始化数据库", "operationId": "InitDatabase", "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}}, "definitions": {"BaseDataInfo": {"description": "The basic response with data | 基础带数据信息", "x-go-package": "seevision.cn/server/meet-analytics-api/internal/types"}, "BaseIDInfo": {"description": "The base ID response data | 基础ID信息", "x-go-package": "seevision.cn/server/meet-analytics-api/internal/types"}, "BaseIDInt32Info": {"description": "The base ID response data (int32) | 基础ID信息 (int32)", "x-go-package": "seevision.cn/server/meet-analytics-api/internal/types"}, "BaseIDInt64Info": {"description": "The base ID response data (int64) | 基础ID信息 (int64)", "x-go-package": "seevision.cn/server/meet-analytics-api/internal/types"}, "BaseIDStringInfo": {"description": "The base ID response data (string) | 基础ID信息 (string)", "x-go-package": "seevision.cn/server/meet-analytics-api/internal/types"}, "BaseIDUint32Info": {"description": "The base ID response data (uint32) | 基础ID信息 (uint32)", "x-go-package": "seevision.cn/server/meet-analytics-api/internal/types"}, "BaseListInfo": {"description": "The basic response with data | 基础带数据信息", "x-go-package": "seevision.cn/server/meet-analytics-api/internal/types"}, "BaseMsgResp": {"description": "The basic response without data | 基础不带数据信息", "x-go-package": "seevision.cn/server/meet-analytics-api/internal/types"}, "BaseUUIDInfo": {"description": "The base UUID response data | 基础UUID信息", "x-go-package": "seevision.cn/server/meet-analytics-api/internal/types"}, "EmptyReq": {"description": "Empty request | 无参数请求", "x-go-package": "seevision.cn/server/meet-analytics-api/internal/types"}, "EventReportResp": {"description": "事件上报响应 | Event report response", "x-go-package": "seevision.cn/server/meet-analytics-api/internal/types"}, "HealthCheckResp": {"description": "健康检查响应 | Health check response", "x-go-package": "seevision.cn/server/meet-analytics-api/internal/types"}, "IDInt32PathReq": {"description": "Basic ID request (int32) | 基础ID地址参数请求 (int32)", "x-go-package": "seevision.cn/server/meet-analytics-api/internal/types"}, "IDInt32Req": {"description": "Basic ID request (int32) | 基础ID参数请求 (int32)", "x-go-package": "seevision.cn/server/meet-analytics-api/internal/types"}, "IDInt64PathReq": {"description": "Basic ID request (int64) | 基础ID地址参数请求 (int64)", "x-go-package": "seevision.cn/server/meet-analytics-api/internal/types"}, "IDInt64Req": {"description": "Basic ID request (int64) | 基础ID参数请求 (int64)", "x-go-package": "seevision.cn/server/meet-analytics-api/internal/types"}, "IDPathReq": {"description": "Basic ID request | 基础ID地址参数请求", "x-go-package": "seevision.cn/server/meet-analytics-api/internal/types"}, "IDReq": {"description": "Basic ID request | 基础ID参数请求", "x-go-package": "seevision.cn/server/meet-analytics-api/internal/types"}, "IDStringPathReq": {"description": "Basic ID request (string) | 基础ID地址参数请求 (string)", "x-go-package": "seevision.cn/server/meet-analytics-api/internal/types"}, "IDStringReq": {"description": "Basic ID request (string) | 基础ID参数请求 (string)", "x-go-package": "seevision.cn/server/meet-analytics-api/internal/types"}, "IDUint32PathReq": {"description": "Basic ID request (uint32) | 基础ID地址参数请求 (uint32)", "x-go-package": "seevision.cn/server/meet-analytics-api/internal/types"}, "IDUint32Req": {"description": "Basic ID request (uint32) | 基础ID参数请求 (uint32)", "x-go-package": "seevision.cn/server/meet-analytics-api/internal/types"}, "IDsInt32Req": {"description": "Basic IDs request (int32) | 基础ID数组参数请求 (int32)", "x-go-package": "seevision.cn/server/meet-analytics-api/internal/types"}, "IDsInt64Req": {"description": "Basic IDs request (int64) | 基础ID数组参数请求 (int64)", "x-go-package": "seevision.cn/server/meet-analytics-api/internal/types"}, "IDsReq": {"description": "Basic IDs request | 基础ID数组参数请求", "x-go-package": "seevision.cn/server/meet-analytics-api/internal/types"}, "IDsStringReq": {"description": "Basic IDs request (string) | 基础ID数组参数请求 (string)", "x-go-package": "seevision.cn/server/meet-analytics-api/internal/types"}, "IDsUint32Req": {"description": "Basic IDs request (uint32) | 基础ID数组参数请求 (uint32)", "x-go-package": "seevision.cn/server/meet-analytics-api/internal/types"}, "PageInfo": {"description": "The page request parameters | 列表请求参数", "x-go-package": "seevision.cn/server/meet-analytics-api/internal/types"}, "ServiceInfo": {"description": "服务信息 | Service info", "x-go-package": "seevision.cn/server/meet-analytics-api/internal/types"}, "UUIDPathReq": {"description": "Basic UUID request in path | 基础UUID地址参数请求", "x-go-package": "seevision.cn/server/meet-analytics-api/internal/types"}, "UUIDReq": {"description": "Basic UUID request | 基础UUID参数请求", "x-go-package": "seevision.cn/server/meet-analytics-api/internal/types"}, "UUIDsReq": {"description": "Basic UUID array request | 基础UUID数组参数请求", "x-go-package": "seevision.cn/server/meet-analytics-api/internal/types"}, "UniversalReportReq": {"description": "通用事件上报请求 | Universal event report request", "x-go-package": "seevision.cn/server/meet-analytics-api/internal/types"}}, "securityDefinitions": {"Token": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header"}}, "security": [{"Token": []}]}