// Code generated by goctl. DO NOT EDIT.
package types

// The basic response with data | 基础带数据信息
// swagger:model BaseDataInfo
type BaseDataInfo struct {
	// Error code | 错误代码
	Code int `json:"code"`
	// Message | 提示信息
	Msg string `json:"msg"`
	// Data | 数据
	Data string `json:"data,omitempty"`
}

// The basic response with data | 基础带数据信息
// swagger:model BaseListInfo
type BaseListInfo struct {
	// The total number of data | 数据总数
	Total uint64 `json:"total"`
	// Data | 数据
	Data string `json:"data,omitempty"`
}

// The basic response without data | 基础不带数据信息
// swagger:model BaseMsgResp
type BaseMsgResp struct {
	// Error code | 错误代码
	Code int `json:"code"`
	// Message | 提示信息
	Msg string `json:"msg"`
}

// The page request parameters | 列表请求参数
// swagger:model PageInfo
type PageInfo struct {
	// Page number | 第几页
	// required : true
	// min : 0
	Page uint64 `json:"page" validate:"required,number,gt=0"`
	// Page size | 单页数据行数
	// required : true
	// max : 100000
	PageSize uint64 `json:"pageSize" validate:"required,number,lt=100000"`
}

// Basic ID request | 基础ID参数请求
// swagger:model IDReq
type IDReq struct {
	// ID
	// Required: true
	Id uint64 `json:"id" validate:"number"`
}

// Basic IDs request | 基础ID数组参数请求
// swagger:model IDsReq
type IDsReq struct {
	// IDs
	// Required: true
	Ids []uint64 `json:"ids"`
}

// Basic ID request in path | 基础ID地址参数请求
// swagger:parameters GetPublicCategoryById
type IDPathReq struct {
	// ID
	// Required: true
	// in: path
	Id uint64 `json:"id,optional" path:"id"`
}

// Basic UUID request in path | 基础UUID地址参数请求
// swagger:parameters GetPublicArticleById
type UUIDPathReq struct {
	// ID
	// Required: true
	// in: path
	Id string `json:"id,optional" path:"id"`
}

// Basic UUID request | 基础UUID参数请求
// swagger:model UUIDReq
type UUIDReq struct {
	// ID
	// required : true
	// max length : 36
	// min length : 36
	Id string `json:"id" validate:"required,len=36"`
}

// Basic UUID array request | 基础UUID数组参数请求
// swagger:model UUIDsReq
type UUIDsReq struct {
	// Ids
	// Required: true
	Ids []string `json:"ids"`
}

// The base ID response data | 基础ID信息
// swagger:model BaseIDInfo
type BaseIDInfo struct {
	// ID
	Id *uint64 `json:"id,optional"`
	// Create date | 创建日期
	CreatedAt *int64 `json:"createdAt,optional"`
	// Update date | 更新日期
	UpdatedAt *int64 `json:"updatedAt,optional"`
}

// The base UUID response data | 基础UUID信息
// swagger:model BaseUUIDInfo
type BaseUUIDInfo struct {
	// ID
	Id *string `json:"id,optional"`
	// Create date | 创建日期
	CreatedAt *int64 `json:"createdAt,optional"`
	// Update date | 更新日期
	UpdatedAt *int64 `json:"updatedAt,optional"`
}

// swagger:model HealthCheckResp
type HealthCheckResp struct {
	Status    string            `json:"status"`    // ok | degraded | error
	Timestamp int64             `json:"timestamp"` // Unix时间戳
	Service   string            `json:"service"`   // 服务名称
	Version   string            `json:"version"`   // 版本信息
	Checks    map[string]string `json:"checks"`    // 依赖检查详情
}

// The response data of article information | 文章信息
// swagger:model ArticleInfo
type ArticleInfo struct {
	BaseUUIDInfo
	// Status
	Status *uint8 `json:"status,optional"`
	// Title | 文章标题
	Title *string `json:"title,optional"`
	// SubTitle | 文章副标题
	SubTitle *string `json:"subTitle,optional"`
	// Introduction | 文章简介
	Introduction *string `json:"introduction,optional"`
	// Keyword | 文章关键字
	Keyword *string `json:"keyword,optional"`
	// The cover image of the article | 文章封面图
	Img *string `json:"img,optional"`
	// Content | 文章内容
	Content *string `json:"content,optional"`
	// Author | 文章作者
	Author *string `json:"author,optional"`
	// Source | 文章来源
	Source *string `json:"source,optional"`
	// Hit | 点击数
	Hit *uint32 `json:"hit,optional"`
	// The thumbnail of the cover image | 封面图缩略图
	Thumbnail *string `json:"thumbnail,optional"`
	// IsRecommended | 是否推荐
	IsRecommended *bool `json:"isRecommended,optional"`
	// Category ID | 栏目ID
	CategoryId *uint64 `json:"categoryId,optional"`
	// Tags | 文章标签
	TagIds []uint64 `json:"tagIds,optional,omitempty"`
}

// The response data of article list | 文章列表数据
// swagger:model ArticleListResp
type ArticleListResp struct {
	BaseDataInfo
	// Article list data | 文章列表数据
	Data ArticleListInfo `json:"data"`
}

// Article list data | 文章列表数据
// swagger:model ArticleListInfo
type ArticleListInfo struct {
	BaseListInfo
	// The API list data | 文章列表数据
	Data []ArticleInfo `json:"data"`
}

// Get article list request params | 文章列表请求参数
// swagger:model ArticleListReq
type ArticleListReq struct {
	PageInfo
	// Title | 文章标题
	Title *string `json:"title,optional"`
	// Short title | 文章副标题
	SubTitle *string `json:"subTitle,optional"`
	// Keyword | 文章关键字
	Keyword *string `json:"keyword,optional"`
	// Category | 文章栏目 ID
	CategoryId *uint64 `json:"categoryId,optional"`
	// Tags | 文章标签
	TagIds []uint64 `json:"tagIds,optional"`
	// Sort | 排序方式
	Sort *string `json:"sort,optional"`
}

// Article information response | 文章信息返回体
// swagger:model ArticleInfoResp
type ArticleInfoResp struct {
	BaseDataInfo
	// Article information | 文章数据
	Data ArticleInfo `json:"data"`
}

// The response data of public article information | 公开的文章信息
// swagger:model PublicArticleInfo
type PublicArticleInfo struct {
	BaseUUIDInfo
	// Title | 文章标题
	Title *string `json:"title,optional"`
	// SubTitle | 文章副标题
	SubTitle *string `json:"subTitle,optional"`
	// Introduction | 文章简介
	Introduction *string `json:"introduction,optional"`
	// Keyword | 文章关键字
	Keyword *string `json:"keyword,optional"`
	// The cover image of the article | 文章封面图
	Img *string `json:"img,optional"`
	// Content | 文章内容
	Content *string `json:"content,optional"`
	// Author | 文章作者
	Author *string `json:"author,optional"`
	// Source | 文章来源
	Source *string `json:"source,optional"`
	// Hit | 点击数
	Hit *uint32 `json:"hit,optional"`
	// The thumbnail of the cover image | 封面图缩略图
	Thumbnail *string `json:"thumbnail,optional"`
	// Category  | 栏目名称
	Category *string `json:"category,optional"`
	// Tags | 文章标签
	Tags []string `json:"tags,optional,omitempty"`
}

// The response data of article list | 文章列表数据
// swagger:model PublicArticleListResp
type PublicArticleListResp struct {
	BaseDataInfo
	// Article list data | 文章列表数据
	Data PublicArticleListInfo `json:"data"`
}

// Article list data | 文章列表数据
// swagger:model PublicArticleListInfo
type PublicArticleListInfo struct {
	BaseListInfo
	// The API list data | 文章列表数据
	Data []PublicArticleInfo `json:"data"`
}

// Article information response | 文章信息返回体
// swagger:model PublicArticleInfoResp
type PublicArticleInfoResp struct {
	BaseDataInfo
	// Article information | 文章数据
	Data PublicArticleInfo `json:"data"`
}

// Get public article list request params | 公开文章列表请求参数
// swagger:parameters GetPublicArticleList
type PublicArticleListReq struct {
	// Page number | 第几页
	// in: query
	// required : true
	// min : 0
	Page uint64 `json:"page,optional" form:"page" validate:"required,number,gt=0"`
	// Page size | 单页数据行数
	// in: query
	// required : true
	// max : 100000
	PageSize uint64 `json:"pageSize,optional" form:"pageSize" validate:"required,number,lt=100000"`
	// Title | 文章标题
	// in: query
	Title *string `json:"title,optional" form:"title,optional"`
	// Short title | 文章副标题
	// in: query
	SubTitle *string `json:"subTitle,optional" form:"subTitle,optional"`
	// Keyword | 文章关键字
	// in: query
	Keyword *string `json:"keyword,optional" form:"keyword,optional"`
	// Category | 文章栏目 ID
	// in: query
	CategoryId *uint64 `json:"categoryId,optional" form:"categoryId,optional"`
	// Tags | 文章标签
	// in: query
	TagIds []uint64 `json:"tagIds,optional" form:"tagIds,optional"`
	// Sort | 排序方式
	// in: query
	Sort *string `json:"sort,optional" form:"sort,optional"`
}

// The response data of category information | 栏目信息
// swagger:model CategoryInfo
type CategoryInfo struct {
	BaseIDInfo
	// State | 状态
	State *bool `json:"state,optional"`
	// Sort | 排序
	Sort *uint32 `json:"sort,optional"`
	// Title | 栏目标题
	Title *string `json:"title,optional"`
	// The sub title of category | 栏目副标题
	SubTitle *string `json:"subTitle,optional"`
	// Banner | 栏目 Banner 图
	Banner *string `json:"banner,optional"`
	// Description | 描述
	Description *string `json:"description,optional"`
	// Whether if leaf node | 是否为叶子节点
	IsLeaf *bool `json:"isLeaf,optional"`
	// ParentId | 父级 ID
	ParentId *uint64 `json:"parentId,optional"`
}

// The response data of category list | 栏目列表数据
// swagger:model CategoryListResp
type CategoryListResp struct {
	BaseDataInfo
	// Category list data | 栏目列表数据
	Data CategoryListInfo `json:"data"`
}

// Category list data | 栏目列表数据
// swagger:model CategoryListInfo
type CategoryListInfo struct {
	BaseListInfo
	// The API list data | 栏目列表数据
	Data []CategoryInfo `json:"data"`
}

// Get category list request params | 栏目列表请求参数
// swagger:model CategoryListReq
type CategoryListReq struct {
	PageInfo
	// Title | 标题
	Title *string `json:"title,optional"`
	// The sub title of category | 栏目副标题
	SubTitle *string `json:"subTitle,optional"`
}

// Category information response | 栏目信息返回体
// swagger:model CategoryInfoResp
type CategoryInfoResp struct {
	BaseDataInfo
	// Category information | 栏目数据
	Data CategoryInfo `json:"data"`
}

// Get public category list request params | 公开栏目列表请求参数
// swagger:parameters GetPublicCategoryList
type PublicCategoryListReq struct {
	// Page number | 第几页
	// in: query
	// required : true
	// min : 0
	Page uint64 `json:"page,optional" form:"page" validate:"required,number,gt=0"`
	// Page size | 单页数据行数
	// in: query
	// required : true
	// max : 100000
	PageSize uint64 `json:"pageSize,optional" form:"pageSize" validate:"required,number,lt=100000"`
	// Title | 标题
	Title *string `json:"title,optional" form:"title,optional"`
	// The sub title of category | 栏目副标题
	SubTitle *string `json:"subTitle,optional" form:"subTitle,optional"`
}

// The response data of tag information | 文章标签信息
// swagger:model TagInfo
type TagInfo struct {
	BaseIDInfo
	// Title
	Title *string `json:"title,optional"`
}

// The response data of tag list | 文章标签列表数据
// swagger:model TagListResp
type TagListResp struct {
	BaseDataInfo
	// Tag list data | 文章标签列表数据
	Data TagListInfo `json:"data"`
}

// Tag list data | 文章标签列表数据
// swagger:model TagListInfo
type TagListInfo struct {
	BaseListInfo
	// The API list data | 文章标签列表数据
	Data []TagInfo `json:"data"`
}

// Get tag list request params | 文章标签列表请求参数
// swagger:model TagListReq
type TagListReq struct {
	PageInfo
	// Title
	Title *string `json:"title,optional"`
}

// Tag information response | 文章标签信息返回体
// swagger:model TagInfoResp
type TagInfoResp struct {
	BaseDataInfo
	// Tag information | 文章标签数据
	Data TagInfo `json:"data"`
}

// The response data of article comment information | 文章评论信息
// swagger:model ArticleCommentInfo
type ArticleCommentInfo struct {
	BaseUUIDInfo
	// Status | 状态
	Status *uint8 `json:"status,optional"`
	// Content | 内容
	Content *string `json:"content,optional"`
	// UserId | 用户 ID
	UserId *string `json:"userId,optional"`
	// ParentId | 父级 ID
	ParentId *string `json:"parentId,optional"`
	// Article ID | 文章 ID
	ArticleId *string `json:"articleId"`
	// Format Created Date | 格式化后的创建日期
	FormatCreatedAt *string `json:"formatCreatedAt,optional"`
	// Nickname | 昵称
	Nickname *string `json:"nickname,optional"`
}

// The request data of article comment information | 文章评论请求
// swagger:model ArticleCommentReq
type ArticleCommentReq struct {
	// Content | 内容
	Content *string `json:"content,optional"`
	// ParentId | 父级 ID
	ParentId *string `json:"parentId,optional"`
	// Article ID | 文章 ID
	ArticleId *string `json:"articleId"`
	// Nickname | 昵称
	Nickname *string `json:"nickname,optional"`
}

// The response data of article comment list | 文章评论列表数据
// swagger:model ArticleCommentListResp
type ArticleCommentListResp struct {
	BaseDataInfo
	// ArticleComment list data | 文章评论列表数据
	Data ArticleCommentListInfo `json:"data"`
}

// ArticleComment list data | 文章评论列表数据
// swagger:model ArticleCommentListInfo
type ArticleCommentListInfo struct {
	BaseListInfo
	// The API list data | 文章评论列表数据
	Data []ArticleCommentInfo `json:"data"`
}

// Get article comment list request params | 文章评论列表请求参数
// swagger:model ArticleCommentListReq
type ArticleCommentListReq struct {
	PageInfo
	// Content
	Content *string `json:"content,optional"`
}

// ArticleComment information response | 文章评论信息返回体
// swagger:model ArticleCommentInfoResp
type ArticleCommentInfoResp struct {
	BaseDataInfo
	// ArticleComment information | 文章评论数据
	Data ArticleCommentInfo `json:"data"`
}

// Get article comment list request params | 文章评论列表请求参数
// swagger:model PublicArticleCommentListReq
type PublicArticleCommentListReq struct {
	PageInfo
	// Content
	ArticleId *string `json:"articleId,optional"`
}

// The response data of history information | History信息
// swagger:model HistoryInfo
type HistoryInfo struct {
	Id *uint64 `json:"id,optional"`
	// 标题
	Title *string `json:"title,optional"`
	// 内容
	Content *string `json:"content,optional"`
	// 英文标题
	EnTitle *string `json:"enTitle,optional"`
	// 英文内容
	EnContent *string `json:"enContent,optional"`
	// 分组
	Group *string `json:"group,optional"`
	// 排序
	Sort *int8 `json:"sort,optional"`
	// 创建时间
	CreateTime *int64 `json:"createTime,optional"`
	// 更新时间
	UpdateTime *int64 `json:"updateTime,optional"`
	// 删除时间
	DeleteTime *int64 `json:"deleteTime,optional"`
}

// The response data of history list | History列表数据
// swagger:model HistoryListResp
type HistoryListResp struct {
	BaseDataInfo
	// History list data | History列表数据
	Data HistoryListInfo `json:"data"`
}

// History list data | History列表数据
// swagger:model HistoryListInfo
type HistoryListInfo struct {
	BaseListInfo
	// The API list data | History列表数据
	Data []HistoryInfo `json:"data"`
}

// Get history list request params | History列表请求参数
// swagger:model HistoryListReq
type HistoryListReq struct {
	PageInfo
	// 标题
	Title *string `json:"title,optional"`
	// 内容
	Content *string `json:"content,optional"`
	// 分组
	Group *string `json:"group,optional"`
}

// History information response | History信息返回体
// swagger:model HistoryInfoResp
type HistoryInfoResp struct {
	BaseDataInfo
	// History information | History数据
	Data HistoryInfo `json:"data"`
}

// swagger:model PublicHistoryListReq
type PublicHistoryListReq struct {
	// Page number | 第几页
	// in: query
	// required : true
	// min : 0
	Page uint64 `json:"page,optional" form:"page" validate:"required,number,gt=0"`
	// Page size | 单页数据行数
	// in: query
	// required : true
	// max : 100000
	PageSize uint64 `json:"pageSize,optional" form:"pageSize" validate:"required,number,lt=100000"`
	// 标题
	Title *string `json:"title,optional"`
	// 内容
	Content *string `json:"content,optional"`
	// 分组
	Group *string `json:"group,optional"`
}
