// Code generated by goctl. DO NOT EDIT.
package types

// The basic response with data | 基础带数据信息
// swagger:model BaseDataInfo
type BaseDataInfo struct {
	// Error code | 错误代码
	Code int `json:"code"`
	// Message | 提示信息
	Msg string `json:"msg"`
	// Data | 数据
	Data string `json:"data,omitempty"`
}

// The basic response with data | 基础带数据信息
// swagger:model BaseListInfo
type BaseListInfo struct {
	// The total number of data | 数据总数
	Total uint64 `json:"total"`
	// Data | 数据
	Data string `json:"data,omitempty"`
}

// The basic response without data | 基础不带数据信息
// swagger:model BaseMsgResp
type BaseMsgResp struct {
	// Error code | 错误代码
	Code int `json:"code"`
	// Message | 提示信息
	Msg string `json:"msg"`
}

// The page request parameters | 列表请求参数
// swagger:model PageInfo
type PageInfo struct {
	// Page number | 第几页
	// required : true
	// min : 0
	Page uint64 `json:"page" validate:"required,number,gt=0"`
	// Page size | 单页数据行数
	// required : true
	// max : 100000
	PageSize uint64 `json:"pageSize" validate:"required,number,lt=100000"`
}

// Basic ID request | 基础ID参数请求
// swagger:model IDReq
type IDReq struct {
	// ID
	// Required: true
	Id uint64 `json:"id" validate:"number"`
}

// Basic IDs request | 基础ID数组参数请求
// swagger:model IDsReq
type IDsReq struct {
	// IDs
	// Required: true
	Ids []uint64 `json:"ids"`
}

// Basic ID request | 基础ID地址参数请求
// swagger:model IDPathReq
type IDPathReq struct {
	// ID
	// Required: true
	Id uint64 `path:"id"`
}

// Basic UUID request | 基础UUID参数请求
// swagger:model UUIDReq
type UUIDReq struct {
	// ID
	// Required: true
	// Max length: 36
	Id string `json:"id" validate:"len=36"`
}

// Basic UUID array request | 基础UUID数组参数请求
// swagger:model UUIDsReq
type UUIDsReq struct {
	// Ids
	// Required: true
	Ids []string `json:"ids"`
}

// The base ID response data | 基础ID信息
// swagger:model BaseIDInfo
type BaseIDInfo struct {
	// ID
	Id *uint64 `json:"id,optional"`
	// Create date | 创建日期
	CreatedAt *int64 `json:"createdAt,optional"`
	// Update date | 更新日期
	UpdatedAt *int64 `json:"updatedAt,optional"`
}

// The base UUID response data | 基础UUID信息
// swagger:model BaseUUIDInfo
type BaseUUIDInfo struct {
	// ID
	Id *string `json:"id,optional"`
	// Create date | 创建日期
	CreatedAt *int64 `json:"createdAt,optional"`
	// Update date | 更新日期
	UpdatedAt *int64 `json:"updatedAt,optional"`
}

// swagger:model HealthCheckResp
type HealthCheckResp struct {
	Status    string            `json:"status"`    // ok | degraded | error
	Timestamp int64             `json:"timestamp"` // Unix时间戳
	Service   string            `json:"service"`   // 服务名称
	Version   string            `json:"version"`   // 版本信息
	Checks    map[string]string `json:"checks"`    // 依赖检查详情
}

// The response data of role information | 角色信息
// swagger:model RoleInfo
type RoleInfo struct {
	BaseIDInfo
	// Translated Name | 展示名称
	Trans string `json:"trans,optional"`
	// Status | 状态
	// max : 20
	Status *uint32 `json:"status,optional" validate:"omitempty,lt=20"`
	// Name | 角色名称
	// max length : 30
	Name *string `json:"name,optional" validate:"omitempty,max=30"`
	// Role code | 角色码
	// max length : 20
	Code *string `json:"code,optional" validate:"omitempty,max=20"`
	// DefaultRouter | 默认首页
	// max length : 80
	DefaultRouter *string `json:"defaultRouter,optional" validate:"omitempty,max=80"`
	// Remark | 备注
	// max length : 200
	Remark *string `json:"remark,optional" validate:"omitempty,max=200"`
	// Sort | 排序
	// max : 10000
	Sort *uint32 `json:"sort,optional" validate:"omitempty,lt=10000"`
}

// The response data of role list | 角色列表数据
// swagger:model RoleListResp
type RoleListResp struct {
	BaseDataInfo
	// Role list data | 角色列表数据
	Data RoleListInfo `json:"data"`
}

// Role list data | 角色列表数据
// swagger:model RoleListInfo
type RoleListInfo struct {
	BaseListInfo
	// The API list data | 角色列表数据
	Data []RoleInfo `json:"data"`
}

// Get role list request params | 角色列表请求参数
// swagger:model RoleListReq
type RoleListReq struct {
	PageInfo
	// Name | 角色名称
	Name *string `json:"name,optional"`
}

// Role information response | 角色信息返回体
// swagger:model RoleInfoResp
type RoleInfoResp struct {
	BaseDataInfo
	// Role information | 角色数据
	Data RoleInfo `json:"data"`
}

// The response data of user information | 用户信息
// swagger:model UserInfo
type UserInfo struct {
	BaseUUIDInfo
	// Status | 状态
	// max : 20
	Status *uint32 `json:"status,optional" validate:"omitempty,lt=20"`
	// Username | 用户名
	// max length : 50
	Username *string `json:"username,optional" validate:"omitempty,max=50"`
	// Nickname | 昵称
	// max length : 40
	Nickname *string `json:"nickname,optional" validate:"omitempty,max=40"`
	// Password | 密码
	// min length : 6
	Password *string `json:"password,optional" validate:"omitempty,min=6"`
	// Description | 描述
	// max length : 100
	Description *string `json:"description,optional" validate:"omitempty,max=100"`
	// HomePath | 首页
	// max length : 70
	HomePath *string `json:"homePath,optional" validate:"omitempty,max=70"`
	// RoleId | 角色ID
	RoleIds []uint64 `json:"roleIds,optional"`
	// Mobile | 手机号
	// max length : 18
	Mobile *string `json:"mobile,optional" validate:"omitempty,max=18"`
	// Email | 邮箱
	// max length : 80
	Email *string `json:"email,optional" validate:"omitempty,max=80"`
	// Avatar | 头像地址
	// max length : 300
	Avatar *string `json:"avatar,optional" validate:"omitempty,max=300"`
	// Department ID | 部门ID
	DepartmentId *uint64 `json:"departmentId,optional,omitempty"`
	// Position ID | 职位ID
	PositionIds []uint64 `json:"positionId,optional,omitempty"`
}

// The response data of user list | 用户列表数据
// swagger:model UserListResp
type UserListResp struct {
	BaseDataInfo
	// User list data | User列表数据
	Data UserListInfo `json:"data"`
}

// User list data | 用户列表数据
// swagger:model UserListInfo
type UserListInfo struct {
	BaseListInfo
	// The API list data | User列表数据
	Data []UserInfo `json:"data"`
}

// Get user list request params | 用户列表请求参数
// swagger:model UserListReq
type UserListReq struct {
	PageInfo
	// User Name | 用户名
	// max length : 20
	Username *string `json:"username,optional" validate:"omitempty,alphanum,max=20"`
	// User's nickname | 用户的昵称
	// max length : 10
	Nickname *string `json:"nickname,optional" validate:"omitempty,alphanumunicode,max=10"`
	// User's mobile phone number | 用户的手机号码
	// max length : 18
	Mobile *string `json:"mobile,optional" validate:"omitempty,eq=|numeric,max=18"`
	// The user's email address | 用户的邮箱
	// max length : 100
	Email *string `json:"email,optional" validate:"omitempty,email,max=100"`
	// User's role ID | 用户的角色ID
	RoleIds []uint64 `json:"roleIds,optional"`
	// The user's department ID | 用户所属部门ID
	DepartmentId *uint64 `json:"departmentId,optional"`
	// User's position id | 用户的职位ID
	PositionId *uint64 `json:"positionId,optional"`
	// Description | 描述
	// max length : 100
	Description *string `json:"description,optional" validate:"omitempty,max=100"`
}

// User information response | 用户信息返回体
// swagger:model UserInfoResp
type UserInfoResp struct {
	BaseDataInfo
	// User information | User数据
	Data UserInfo `json:"data"`
}

// register request | 注册参数
// swagger:model RegisterReq
type RegisterReq struct {
	// User Name | 用户名
	// required : true
	// max length : 20
	Username string `json:"username" validate:"required,alphanum,max=20"`
	// Password | 密码
	// required : true
	// max length : 30
	// min length : 6
	Password string `json:"password" validate:"required,max=30,min=6"`
	// Captcha ID which store in redis | 验证码编号, 存在redis中
	// required : true
	// max length : 20
	// min length : 20
	CaptchaId string `json:"captchaId" validate:"required,len=20"`
	// The Captcha which users input | 用户输入的验证码
	// required : true
	// max length : 5
	// min length : 5
	Captcha string `json:"captcha" validate:"required,len=5"`
	// The user's email address | 用户的邮箱
	// required : true
	// max length : 100
	Email string `json:"email" validate:"required,email,max=100"`
}

// Register by email request | 邮箱注册参数
// swagger:model RegisterByEmailReq
type RegisterByEmailReq struct {
	// User Name | 用户名
	// required : true
	// max length : 20
	Username string `json:"username" validate:"required,alphanum,max=20"`
	// Password | 密码
	// required : true
	// max length : 30
	// min length : 6
	Password string `json:"password" validate:"required,max=30,min=6"`
	// The Captcha which users input | 用户输入的验证码
	// required : true
	// max length : 5
	// min length : 5
	Captcha string `json:"captcha" validate:"required,len=5"`
	// The user's email address | 用户的邮箱
	// required : true
	// max length : 100
	Email string `json:"email" validate:"required,email,max=100"`
}

// Register by SMS request | 短信注册参数
// swagger:model RegisterBySmsReq
type RegisterBySmsReq struct {
	// User Name | 用户名
	// required : true
	// max length : 20
	Username string `json:"username" validate:"required,alphanum,max=20"`
	// Password | 密码
	// required : true
	// max length : 30
	// min length : 6
	Password string `json:"password" validate:"required,max=30,min=6"`
	// The Captcha which users input | 用户输入的验证码
	// required : true
	// max length : 5
	// min length : 5
	Captcha string `json:"captcha" validate:"required,len=5"`
	// The user's mobile phone number | 用户的手机号码
	// required : true
	// max length : 20
	PhoneNumber string `json:"phoneNumber"  validate:"required,numeric,max=20"`
}

// change user's password request | 修改密码请求参数
// swagger:model ChangePasswordReq
type ChangePasswordReq struct {
	// User's old password | 用户旧密码
	// required : true
	// max length : 30
	OldPassword string `json:"oldPassword" validate:"required,max=30"`
	// User's new password | 用户新密码
	// required : true
	// max length : 30
	NewPassword string `json:"newPassword" validate:"required,max=30"`
}

// The log in information | 登陆返回的数据信息
// swagger:model LoginInfo
type LoginInfo struct {
	// User's UUID | 用户的UUID
	UserId string `json:"userId"`
	// Token for authorization | 验证身份的token
	Token string `json:"token"`
	// Expire timestamp | 过期时间戳
	Expire uint64 `json:"expire"`
}

// The simple role data | 简单的角色数据
// swagger:model RoleInfoSimple
type RoleInfoSimple struct {
	// Role name | 角色名
	RoleName string `json:"roleName"`
	// Role value | 角色值
	Value string `json:"value"`
}

// The response data of user's basic information | 用户基本信息返回数据
// swagger:model UserBaseIDInfoResp
type UserBaseIDInfoResp struct {
	BaseDataInfo
	// The  data of user's basic information | 用户基本信息
	Data UserBaseIDInfo `json:"data"`
}

// The  data of user's basic information | 用户基本信息
// swagger:model UserBaseIDInfo
type UserBaseIDInfo struct {
	// User's UUID | 用户的UUID
	UUID *string `json:"userId"`
	// User's name | 用户名
	Username *string `json:"username"`
	// User's nickname | 用户的昵称
	Nickname *string `json:"nickname"`
	// The user's avatar path | 用户的头像路径
	Avatar *string `json:"avatar"`
	// The home page that the user enters after logging in | 用户登陆后进入的首页
	HomePath *string `json:"homePath"`
	// The description of user | 用户的描述信息
	Description *string `json:"desc"`
	// User's Role Name | 用户的角色名称
	RoleName []string `json:"roleName"`
	// Department Name | 部门名称
	DepartmentName string `json:"departmentName,optional"`
}

// The permission code for front end permission control | 权限码： 用于前端权限控制
// swagger:model PermCodeResp
type PermCodeResp struct {
	BaseDataInfo
	// Permission code data | 权限码数据
	Data []string `json:"data"`
}

// Login request | 登录参数
// swagger:model LoginReq
type LoginReq struct {
	// User Name | 用户名
	// required : true
	// max length : 20
	Username string `json:"username" validate:"required,alphanum,max=20"`
	// Password | 密码
	// required : true
	// max length : 30
	// min length : 6
	Password string `json:"password" validate:"required,max=30,min=6"`
	// Captcha ID which store in redis | 验证码编号, 存在redis中
	// required : true
	// max length : 20
	// min length : 20
	CaptchaId string `json:"captchaId"  validate:"required,len=20"`
	// The Captcha which users input | 用户输入的验证码
	// required : true
	// max length : 5
	// min length : 5
	Captcha string `json:"captcha" validate:"required,len=5"`
}

// Log in by email request | 邮箱登录参数
// swagger:model LoginByEmailReq
type LoginByEmailReq struct {
	// The user's email address | 用户的邮箱
	// required : true
	// max length : 100
	Email string `json:"email" validate:"required,email,max=100"`
	// The Captcha which users input | 用户输入的验证码
	// max length : 5
	// min length : 5
	Captcha string `json:"captcha,optional" validate:"omitempty,len=5"`
}

// Log in by SMS request | 短信登录参数
// swagger:model LoginBySmsReq
type LoginBySmsReq struct {
	// The user's mobile phone number | 用户的手机号码
	// required : true
	// max length : 20
	PhoneNumber string `json:"phoneNumber"  validate:"required,numeric,max=20"`
	// The Captcha which users input | 用户输入的验证码
	// max length : 5
	// min length : 5
	Captcha string `json:"captcha,optional" validate:"omitempty,len=5"`
}

// The log in response data | 登录返回数据
// swagger:model LoginResp
type LoginResp struct {
	BaseDataInfo
	// The log in information | 登陆返回的数据信息
	Data LoginInfo `json:"data"`
}

// The profile information | 个人信息
// swagger:model ProfileInfo
type ProfileInfo struct {
	// user's nickname | 用户的昵称
	// max length : 10
	Nickname *string `json:"nickname" validate:"omitempty,alphanumunicode,max=10"`
	// The user's avatar path | 用户的头像路径
	// max length : 300
	Avatar *string `json:"avatar" validate:"omitempty,max=300"`
	// User's mobile phone number | 用户的手机号码
	// max length : 18
	Mobile *string `json:"mobile" validate:"omitempty,eq=|numeric,max=18"`
	// The user's email address | 用户的邮箱
	// max length : 100
	Email *string `json:"email" validate:"omitempty,email,max=100"`
}

// The profile response data | 个人信息返回数据
// swagger:model ProfileResp
type ProfileResp struct {
	BaseDataInfo
	// The profile information | 个人信息
	Data ProfileInfo `json:"data"`
}

// Reset password by email request | 通过邮箱重置密码请求
// swagger:model ResetPasswordByEmailReq
type ResetPasswordByEmailReq struct {
	Email    string `json:"email" validate:"email"`
	Captcha  string `json:"captcha"`
	Password string `json:"password"`
}

// Reset password by SMS request | 通过短信重置密码请求
// swagger:model ResetPasswordBySmsReq
type ResetPasswordBySmsReq struct {
	PhoneNumber string `json:"phoneNumber"`
	Captcha     string `json:"captcha"`
	Password    string `json:"password"`
}

// Refresh token response data | 刷新令牌响应数据
// swagger:model RefreshTokenResp
type RefreshTokenResp struct {
	BaseDataInfo
	// The token information | 令牌信息
	Data RefreshTokenInfo `json:"data"`
}

// Refresh token information | 刷新令牌信息
// swagger:model RefreshTokenInfo
type RefreshTokenInfo struct {
	Token     string `json:"token"`
	ExpiredAt int64  `json:"expiredAt"`
}

// The response data of menu information | 菜单信息
// swagger:model MenuInfo
type MenuInfo struct {
	BaseIDInfo
	// Translated Name | 国际化展示名称
	Trans string `json:"trans,optional"`
	// Level | 菜单层级
	Level *uint32 `json:"level,optional"`
	// ParentId | 父级菜单ID
	ParentId *uint64 `json:"parentId,optional"`
	// Path | 菜单访问路径
	Path *string `json:"path,optional"`
	// Menu name | 菜单名称
	Name *string `json:"name,optional"`
	// Redirect | 跳转地址
	Redirect *string `json:"redirect,optional"`
	// Component | 组件地址
	Component *string `json:"component,optional"`
	// Sort | 排序
	Sort *uint32 `json:"sort,optional"`
	// Disabled | 是否启用
	Disabled *bool `json:"disabled,optional"`
	// Meta | 菜单meta数据
	Meta Meta `json:"meta"`
	// MenuType | 菜单类型
	MenuType *uint32 `json:"menuType,optional"`
	// Service Name | 服务名称
	ServiceName *string `json:"serviceName,optional"`
	// Permission symbol | 权限标识
	Permission *string `json:"permission,optional"`
}

// The meta data of menu | 菜单的meta数据
// swagger:model Meta
type Meta struct {
	// Menu title show in page | 菜单显示名
	// Max length: 50
	Title *string `json:"title" validate:"max=50"`
	// Menu Icon | 菜单图标
	// Max length: 50
	Icon *string `json:"icon" validate:"max=50"`
	// Hide menu | 隐藏菜单
	HideMenu *bool `json:"hideMenu" validate:"omitempty,boolean"`
	// If hide the breadcrumb | 隐藏面包屑
	HideBreadcrumb *bool `json:"hideBreadcrumb,optional" validate:"omitempty,boolean"`
	// Do not keep alive the tab | 不缓存Tab
	IgnoreKeepAlive *bool `json:"ignoreKeepAlive,optional" validate:"omitempty,boolean"`
	// Hide the tab header | 当前路由不在标签页显示
	HideTab *bool `json:"hideTab,optional" validate:"omitempty,boolean"`
	// Iframe path | 内嵌iframe的地址
	FrameSrc *string `json:"frameSrc,optional"`
	// The route carries parameters or not | 如果该路由会携带参数，且需要在tab页上面显示。则需要设置为true
	CarryParam *bool `json:"carryParam,optional" validate:"omitempty,boolean"`
	// Hide children menu or not | 隐藏所有子菜单
	HideChildrenInMenu *bool `json:"hideChildrenInMenu,optional" validate:"omitempty,boolean"`
	// Affix tab | 是否固定标签
	Affix *bool `json:"affix,optional" validate:"omitempty,boolean"`
	// The maximum number of pages the router can open | 动态路由可打开Tab页数
	DynamicLevel *uint32 `json:"dynamicLevel" validate:"number,lt=30"`
	// The real path of the route without dynamic part | 动态路由的实际Path, 即去除路由的动态部分
	RealPath *string `json:"realPath,optional"`
}

// The response data of menu list | 菜单列表返回数据
// swagger:model MenuListResp
type MenuListResp struct {
	BaseDataInfo
	// Menu list data | Menu列表数据
	Data MenuListInfo `json:"data"`
}

// Menu list data | Menu列表数据
// swagger:model MenuListInfo
type MenuListInfo struct {
	BaseListInfo
	// The menu list data | 菜单列表数据
	Data []MenuInfo `json:"data"`
}

// Menu information response | 菜单信息返回体
// swagger:model MenuInfoResp
type MenuInfoResp struct {
	BaseDataInfo
	// Menu information | Menu数据
	Data MenuInfo `json:"data"`
}

// Menu information plain | 菜单信息无嵌套
// swagger:model MenuPlainInfo
type MenuPlainInfo struct {
	Id *uint64 `json:"id,optional"`
	// Create date | 创建日期
	CreatedAt *int64 `json:"createdAt,optional"`
	// Update date | 更新日期
	UpdatedAt *int64 `json:"updatedAt,optional"`
	// Translated Name | 国际化展示名称
	Trans *string `json:"trans,optional"`
	// Level | 菜单层级
	// max : 20
	Level *uint32 `json:"level,optional" validate:"omitempty,lt=20"`
	// ParentId | 父级菜单ID
	ParentId *uint64 `json:"parentId,optional"`
	// Path | 菜单访问路径
	// max length : 200
	Path *string `json:"path,optional" validate:"omitempty,max=200"`
	// Menu name | 菜单名称
	// max length : 100
	Name *string `json:"name,optional" validate:"omitempty,max=100"`
	// Redirect | 跳转地址
	// max length : 300
	Redirect *string `json:"redirect,optional" validate:"omitempty,max=300"`
	// Component | 组件地址
	// max length : 80
	Component *string `json:"component,optional" validate:"omitempty,max=80"`
	// Sort | 排序
	// max : 10000
	Sort *uint32 `json:"sort,optional" validate:"omitempty,lt=10000"`
	// Disabled | 是否启用
	Disabled *bool `json:"disabled,optional"`
	// MenuType | 菜单类型
	// max : 10
	MenuType *uint32 `json:"menuType,optional" validate:"omitempty,lt=10"`
	// Service Name | 服务名称
	ServiceName *string `json:"serviceName,optional"`
	// Permission symbol | 权限标识
	Permission *string `json:"permission,optional"`
	// Menu title show in page | 菜单显示名
	// max length : 50
	Title *string `json:"title" validate:"omitempty,max=50"`
	// Menu Icon | 菜单图标
	// max length : 50
	Icon *string `json:"icon" validate:"omitempty,max=50"`
	// Hide menu | 隐藏菜单
	HideMenu *bool `json:"hideMenu" validate:"omitempty,boolean"`
	// If hide the breadcrumb | 隐藏面包屑
	HideBreadcrumb *bool `json:"hideBreadcrumb,optional" validate:"omitempty,boolean"`
	// Do not keep alive the tab | 不缓存Tab
	IgnoreKeepAlive *bool `json:"ignoreKeepAlive,optional" validate:"omitempty,boolean"`
	// Hide the tab header | 当前路由不在标签页显示
	HideTab *bool `json:"hideTab,optional" validate:"omitempty,boolean"`
	// Iframe path | 内嵌iframe的地址
	// max length : 300
	FrameSrc *string `json:"frameSrc,optional" validate:"omitempty,max=300"`
	// The route carries parameters or not | 如果该路由会携带参数，且需要在tab页上面显示。则需要设置为true
	CarryParam *bool `json:"carryParam,optional" validate:"omitempty,boolean"`
	// Hide children menu or not | 隐藏所有子菜单
	HideChildrenInMenu *bool `json:"hideChildrenInMenu,optional" validate:"omitempty,boolean"`
	// Affix tab | 是否固定标签
	Affix *bool `json:"affix,optional" validate:"omitempty,boolean"`
	// The maximum number of pages the router can open | 动态路由可打开Tab页数
	DynamicLevel *uint32 `json:"dynamicLevel,optional" validate:"omitempty,number,lt=30"`
	// The real path of the route without dynamic part | 动态路由的实际Path, 即去除路由的动态部分
	RealPath *string `json:"realPath,optional"`
}

// Menu list data | 菜单列表数据
type MenuPlainInfoList struct {
	BaseListInfo
	// The menu list data | 菜单列表数据
	Data []MenuPlainInfo `json:"data"`
}

// Menu list data response | 菜单列表数据返回体
// swagger:model MenuPlainInfoListResp
type MenuPlainInfoListResp struct {
	BaseDataInfo
	// Menu list data | Menu列表数据
	Data MenuPlainInfoList `json:"data"`
}

// The information of captcha | 验证码数据
// swagger:model CaptchaInfo
type CaptchaInfo struct {
	CaptchaId string `json:"captchaId"`
	ImgPath   string `json:"imgPath"`
}

// The response data of captcha | 验证码返回数据
// swagger:model CaptchaResp
type CaptchaResp struct {
	BaseDataInfo
	// The menu authorization data | 菜单授权信息数据
	Data CaptchaInfo `json:"data"`
}

// The email captcha request | 邮箱验证码请求参数
// swagger:model EmailCaptchaReq
type EmailCaptchaReq struct {
	// The email address | 邮箱地址
	Email string `json:"email"`
}

// The sms captcha request | 短信验证码请求参数
// swagger:model SmsCaptchaReq
type SmsCaptchaReq struct {
	// The phone number | 电话号码
	PhoneNumber string `json:"phoneNumber"`
}

// The API information | API信息
// swagger:model ApiInfo
type ApiInfo struct {
	BaseIDInfo
	// Translated Name | 多语言名称
	Trans string `json:"trans,optional"`
	// API path | API路径
	// min length : 1
	// max length : 80
	Path *string `json:"path,optional" validate:"omitempty,min=1,max=80"`
	// API Description | API 描述
	// max length : 100
	Description *string `json:"description,optional" validate:"omitempty,max=100"`
	// API group | API分组
	// min length : 1
	// max length : 80
	Group *string `json:"group,optional" validate:"omitempty,min=1,max=80"`
	// API request method e.g. POST | API请求类型 如POST
	// min length : 3
	// max length : 7
	Method *string `json:"method,optional" validate:"omitempty,uppercase,min=3,max=7"`
	// Whether is required | 是否是必须的 api
	IsRequired *bool `json:"isRequired,optional"`
	// Service name | 服务名称
	ServiceName *string `json:"serviceName,optional"`
}

// The response data of API list | API列表数据
// swagger:model ApiListResp
type ApiListResp struct {
	BaseDataInfo
	// API list data | API 列表数据
	Data ApiListInfo `json:"data"`
}

// API list data | API 列表数据
// swagger:model ApiListInfo
type ApiListInfo struct {
	BaseListInfo
	// The API list data | API列表数据
	Data []ApiInfo `json:"data"`
}

// Get API list request params | API列表请求参数
// swagger:model ApiListReq
type ApiListReq struct {
	PageInfo
	// API path | API路径
	// max length : 200
	Path *string `json:"path,optional" validate:"omitempty,max=200"`
	// API Description | API 描述
	// max length : 100
	Description *string `json:"description,optional" validate:"omitempty,max=100"`
	// API group | API分组
	// max length : 80
	Group *string `json:"group,optional" validate:"omitempty,max=80"`
	// API request method e.g. POST | API请求类型 如POST
	// min length : 3
	// max length : 7
	Method *string `json:"method,optional" validate:"omitempty,uppercase,min=3,max=7"`
	// Whether is required | 是否是必须的 api
	IsRequired *bool `json:"isRequired,optional"`
	// Service name | 服务名称
	ServiceName *string `json:"serviceName,optional"`
}

// API information response | API信息返回体
// swagger:model ApiInfoResp
type ApiInfoResp struct {
	BaseDataInfo
	// API information | API数据
	Data ApiInfo `json:"data"`
}

// The response data of api authorization | API授权数据
// swagger:model ApiAuthorityInfo
type ApiAuthorityInfo struct {
	// API path | API 路径
	// required : true
	// max length : 80
	Path string `json:"path" validate="required,max=80"`
	// API method | API请求方法
	// required : true
	// min length : 3
	// max length : 4
	Method string `json:"method" validate="required,min=3,max=4"`
}

// Create or update api authorization information request | 创建或更新API授权信息
// swagger:model CreateOrUpdateApiAuthorityReq
type CreateOrUpdateApiAuthorityReq struct {
	// Role ID | 角色ID
	// required : true
	// max : 1000
	RoleId uint64 `json:"roleId" validate:"required,lt=1000"`
	// API authorization list | API授权列表数据
	// Required: true
	Data []ApiAuthorityInfo `json:"data"`
}

// The response data of api authorization list | API授权列表返回数据
// swagger:model ApiAuthorityListResp
type ApiAuthorityListResp struct {
	BaseDataInfo
	// The api authorization list data | API授权列表数据
	Data ApiAuthorityListInfo `json:"data"`
}

// The  data of api authorization list | API授权列表数据
// swagger:model ApiAuthorityListInfo
type ApiAuthorityListInfo struct {
	BaseListInfo
	// The api authorization list data | API授权列表数据
	Data []ApiAuthorityInfo `json:"data"`
}

// Create or update menu authorization information request params | 创建或更新菜单授权信息参数
// swagger:model MenuAuthorityInfoReq
type MenuAuthorityInfoReq struct {
	// role ID | 角色ID
	// required : true
	// max : 1000
	RoleId uint64 `json:"roleId" validate:"required,lt=1000"`
	// menu ID array | 菜单ID数组
	// required : true
	MenuIds []uint64 `json:"menuIds" validate:"required"`
}

// Menu authorization response data | 菜单授权信息数据
// swagger:model MenuAuthorityInfoResp
type MenuAuthorityInfoResp struct {
	BaseDataInfo
	// The menu authorization data | 菜单授权信息数据
	Data MenuAuthorityInfoReq `json:"data"`
}

// The response data of dictionary information | 字典信息
// swagger:model DictionaryInfo
type DictionaryInfo struct {
	BaseIDInfo
	// Translated Name | 字典多语言名称
	Trans string `json:"trans,optional"`
	// Title | 字典多语言名称
	// min length : 1
	// max length : 50
	Title *string `json:"title,optional" validate:"omitempty,min=1,max=50"`
	// Name | 字典名称
	// min length : 1
	// max length : 50
	Name *string `json:"name,optional" validate:"omitempty,min=1,max=50"`
	// Status | 状态
	// max : 20
	Status *uint32 `json:"status,optional" validate:"omitempty,lt=20"`
	// Description of dictionary | 字典描述
	// max length : 200
	Desc *string `json:"desc,optional" validate:"omitempty,max=200"`
}

// The response data of dictionary list | 字典列表数据
// swagger:model DictionaryListResp
type DictionaryListResp struct {
	BaseDataInfo
	// Dictionary list data | 字典列表数据
	Data DictionaryListInfo `json:"data"`
}

// Dictionary list data | 字典列表数据
// swagger:model DictionaryListInfo
type DictionaryListInfo struct {
	BaseListInfo
	// The API list data | 字典列表数据
	Data []DictionaryInfo `json:"data"`
}

// Get dictionary list request params | 字典列表请求参数
// swagger:model DictionaryListReq
type DictionaryListReq struct {
	PageInfo
	// Name | 字典名称
	// max length : 50
	Name *string `json:"name,optional" validate:"omitempty,max=50"`
}

// Dictionary information response | 字典信息返回体
// swagger:model DictionaryInfoResp
type DictionaryInfoResp struct {
	BaseDataInfo
	// Dictionary information | 字典数据
	Data DictionaryInfo `json:"data"`
}

// The response data of oauth provider information | 第三方信息
// swagger:model OauthProviderInfo
type OauthProviderInfo struct {
	BaseIDInfo
	// Provider name | 第三方提供商名称
	// max length : 30
	Name *string `json:"name,optional" validate:"omitempty,max=30"`
	// ClientId | 客户端ID
	// max length : 80
	ClientId *string `json:"clientId,optional" validate:"omitempty,max=80"`
	// ClientSecret | 客户端密钥
	// max length : 100
	ClientSecret *string `json:"clientSecret,optional" validate:"omitempty,max=100"`
	// Redirect URL| 跳转地址
	// max length : 300
	RedirectUrl *string `json:"redirectUrl,optional" validate:"omitempty,max=300"`
	// Scopes | 授权范围
	// max length : 100
	Scopes *string `json:"scopes,optional" validate:"omitempty,max=100"`
	// Authority URL | 授权地址
	// max length : 300
	AuthUrl *string `json:"authUrl,optional" validate:"omitempty,max=300"`
	// The URL to get token | 获取Token的地址
	// max length : 300
	TokenUrl *string `json:"tokenUrl,optional" validate:"omitempty,max=300"`
	// The type of auth | 鉴权方式
	// max : 20
	AuthStyle *uint64 `json:"authStyle,optional" validate:"omitempty,lt=20"`
	// The URL to get user information | 获取信息地址
	// max length : 300
	InfoUrl *string `json:"infoUrl,optional" validate:"omitempty,max=300"`
}

// The response data of oauth provider list | 第三方列表数据
// swagger:model OauthProviderListResp
type OauthProviderListResp struct {
	BaseDataInfo
	// OauthProvider list data | 第三方列表数据
	Data OauthProviderListInfo `json:"data"`
}

// OauthProvider list data | 第三方列表数据
// swagger:model OauthProviderListInfo
type OauthProviderListInfo struct {
	BaseListInfo
	// The API list data | 第三方列表数据
	Data []OauthProviderInfo `json:"data"`
}

// Get oauth provider list request params | 第三方列表请求参数
// swagger:model OauthProviderListReq
type OauthProviderListReq struct {
	PageInfo
	// Name | 第三方提供商名称
	// max length : 30
	Name *string `json:"name,optional" validate:"omitempty,max=30"`
}

// Oauth provider information response | 第三方信息返回体
// swagger:model OauthProviderInfoResp
type OauthProviderInfoResp struct {
	BaseDataInfo
	// OauthProvider information | 第三方数据
	Data OauthProviderInfo `json:"data"`
}

// Oauth log in request | Oauth 登录请求
// swagger:model OauthLoginReq
type OauthLoginReq struct {
	// State code to avoid hack | 状态码，请求前后相同避免安全问题
	// required : true
	// max length : 30
	State string `json:"state" validate:"required,max=30"`
	// Provider name | 提供商名字
	// Example: google
	// required : true
	// max length : 40
	Provider string `json:"provider" validate:"required,max=40"`
}

// Redirect response | 跳转网址返回信息
// swagger:model RedirectResp
type RedirectResp struct {
	BaseDataInfo
	// Redirect information | 跳转网址
	Data RedirectInfo `json:"data"`
}

// Redirect information | 跳转网址
// swagger:model RedirectInfo
type RedirectInfo struct {
	// Redirect URL | 跳转网址
	URL string `json:"URL"`
}

// The oauth callback response data | Oauth回调数据
// swagger:model CallbackResp
type CallbackResp struct {
	// User's UUID | 用户的UUID
	UserId string `json:"userId"`
	// User's role information| 用户的角色信息
	// in: body
	Role RoleInfoSimple `json:"role"`
	// Token for authorization | 验证身份的token
	Token string `json:"token"`
	// Expire timestamp | 过期时间戳
	Expire uint64 `json:"expire"`
}

// The response data of token information | 令牌信息
// swagger:model TokenInfo
type TokenInfo struct {
	BaseUUIDInfo
	// Status | 状态
	Status *uint32 `json:"status,optional"`
	// User's UUID | 用户的UUID
	Uuid *string `json:"uuid,optional"`
	// Token | 用户的Token
	Token *string `json:"token,optional"`
	// Source | Token 来源
	Source *string `json:"source,optional"`
	// Username | 用户名
	Username *string `json:"username,optional"`
	// ExpiredAt | 过期时间
	ExpiredAt *int64 `json:"expiredAt,optional"`
}

// The response data of token list | 令牌列表数据
// swagger:model TokenListResp
type TokenListResp struct {
	BaseDataInfo
	// Token list data | 令牌列表数据
	Data TokenListInfo `json:"data"`
}

// Token list data | Token列表数据
// swagger:model TokenListInfo
type TokenListInfo struct {
	BaseListInfo
	// The API list data | 令牌列表数据
	Data []TokenInfo `json:"data"`
}

// Get token list request params | 令牌列表请求参数
// swagger:model TokenListReq
type TokenListReq struct {
	PageInfo
	// Username | 用户名
	Username *string `json:"username,optional"`
	// Nickname | 昵称
	Nickname *string `json:"nickname,optional"`
	// Email | 邮箱
	Email *string `json:"email,optional"`
	// UUID
	Uuid *string `json:"uuid,optional"`
}

// Token information response | Token信息返回体
// swagger:model TokenInfoResp
type TokenInfoResp struct {
	BaseDataInfo
	// Token information | Token数据
	Data TokenInfo `json:"data"`
}

// The response data of department information | 部门信息
// swagger:model DepartmentInfo
type DepartmentInfo struct {
	BaseIDInfo
	// Translated Name | 展示名称
	Trans string `json:"trans,optional"`
	// Status | 状态
	// max : 20
	Status *uint32 `json:"status,optional" validate:"omitempty,lt=20"`
	// Sort | 排序
	// max : 10000
	Sort *uint32 `json:"sort,optional" validate:"omitempty,lt=10000"`
	// Name | 部门名称
	// min length : 1
	// max length : 50
	Name *string `json:"name,optional" validate:"omitempty,min=1,max=50"`
	// Ancestors | 父级部门列表
	// max length : 200
	Ancestors *string `json:"ancestors,optional" validate:"omitempty,max=200"`
	// Leader | 部门负责人
	// max length : 20
	Leader *string `json:"leader,optional" validate:"omitempty,max=20"`
	// Phone | 电话号码
	// max length : 18
	Phone *string `json:"phone,optional" validate:"omitempty,max=18"`
	// Email | 邮箱
	// max length : 70
	Email *string `json:"email,optional" validate:"omitempty,max=70"`
	// Remark | 备注
	// max length : 200
	Remark *string `json:"remark,optional" validate:"omitempty,max=200"`
	// ParentId | 父级 ID
	ParentId *uint64 `json:"parentId,optional"`
}

// The response data of department list | 部门列表数据
// swagger:model DepartmentListResp
type DepartmentListResp struct {
	BaseDataInfo
	// Department list data | 部门列表数据
	Data DepartmentListInfo `json:"data"`
}

// Department list data | 部门列表数据
// swagger:model DepartmentListInfo
type DepartmentListInfo struct {
	BaseListInfo
	// The API list data | 部门列表数据
	Data []DepartmentInfo `json:"data"`
}

// Get department list request params | 部门列表请求参数
// swagger:model DepartmentListReq
type DepartmentListReq struct {
	PageInfo
	// Name | 部门名称
	// max length : 50
	Name *string `json:"name,optional" validate:"omitempty,max=50"`
	// Leader | 部门负责人
	// max length : 20
	Leader *string `json:"leader,optional" validate:"omitempty,max=20"`
	// Status | 状态
	// max : 20
	Status *uint32 `json:"status,optional" validate:"omitempty,lt=20"`
}

// Department information response | 部门信息返回体
// swagger:model DepartmentInfoResp
type DepartmentInfoResp struct {
	BaseDataInfo
	// Department information | 部门数据
	Data DepartmentInfo `json:"data"`
}

// The response data of position information | 职位信息
// swagger:model PositionInfo
type PositionInfo struct {
	BaseIDInfo
	// Translated Name | 展示名称
	Trans string `json:"trans,optional"`
	// Status | 状态
	// max : 20
	Status *uint32 `json:"status,optional" validate:"omitempty,lt=20"`
	// Sort | 排序
	// max : 10000
	Sort *uint32 `json:"sort,optional" validate:"omitempty,lt=10000"`
	// Name | 职位名称
	// max length : 50
	Name *string `json:"name,optional" validate:"omitempty,max=50"`
	// Code | 职位代码
	// max length : 20
	Code *string `json:"code,optional" validate:"omitempty,max=20"`
	// Remark | 备注
	// max length : 200
	Remark *string `json:"remark,optional" validate:"omitempty,max=200"`
}

// The response data of position list | 职位列表数据
// swagger:model PositionListResp
type PositionListResp struct {
	BaseDataInfo
	// Position list data | 职位列表数据
	Data PositionListInfo `json:"data"`
}

// Position list data | 职位列表数据
// swagger:model PositionListInfo
type PositionListInfo struct {
	BaseListInfo
	// The API list data | 职位列表数据
	Data []PositionInfo `json:"data"`
}

// Get position list request params | 职位列表请求参数
// swagger:model PositionListReq
type PositionListReq struct {
	PageInfo
	// Name | 职位名称
	// max length : 50
	Name *string `json:"name,optional" validate:"omitempty,max=50"`
	// Code | 职位代码
	// max length : 20
	Code *string `json:"code,optional" validate:"omitempty,max=20"`
	// Remark | 备注
	// max length : 200
	Remark *string `json:"remark,optional" validate:"omitempty,max=200"`
}

// Position information response | 职位信息返回体
// swagger:model PositionInfoResp
type PositionInfoResp struct {
	BaseDataInfo
	// Position information | 职位数据
	Data PositionInfo `json:"data"`
}

// The response data of dictionary detail information | 字典键值信息
// swagger:model DictionaryDetailInfo
type DictionaryDetailInfo struct {
	BaseIDInfo
	// Status | 状态
	// max : 20
	Status *uint32 `json:"status,optional" validate:"omitempty,lt=20"`
	// Title | 显示名称
	// max length : 50
	Title *string `json:"title,optional" validate:"omitempty,max=50"`
	// Key | 键
	// max length : 80
	Key *string `json:"key,optional" validate:"omitempty,max=80"`
	// Value | 值
	// max length : 100
	Value *string `json:"value,optional" validate:"omitempty,max=100"`
	// Dictionary ID | 所属字典ID
	DictionaryId *uint64 `json:"dictionaryId,optional"`
	// Sort | 排序
	// max : 10000
	Sort *uint32 `json:"sort,optional" validate:"omitempty,lt=10000"`
	// Title I18n Trans | 标题国际化后
	Trans string `json:"trans,optional"`
}

// The response data of dictionary detail list | 字典键值列表数据
// swagger:model DictionaryDetailListResp
type DictionaryDetailListResp struct {
	BaseDataInfo
	// DictionaryDetail list data | 字典键值列表数据
	Data DictionaryDetailListInfo `json:"data"`
}

// DictionaryDetail list data | 字典键值列表数据
// swagger:model DictionaryDetailListInfo
type DictionaryDetailListInfo struct {
	BaseListInfo
	// The API list data | 字典键值列表数据
	Data []DictionaryDetailInfo `json:"data"`
}

// Get dictionary detail list request params | 字典键值列表请求参数
// swagger:model DictionaryDetailListReq
type DictionaryDetailListReq struct {
	PageInfo
	// Key | 键
	// max length : 80
	Key *string `json:"key,optional" validate:"omitempty,max=80"`
	// Dictionary ID | 所属字典ID
	DictionaryId *uint64 `json:"dictionaryId,optional"`
}

// DictionaryDetail information response | 字典键值信息返回体
// swagger:model DictionaryDetailInfoResp
type DictionaryDetailInfoResp struct {
	BaseDataInfo
	// DictionaryDetail information | 字典键值数据
	Data DictionaryDetailInfo `json:"data"`
}

// Dictionary name request | 字典名称请求
// swagger:parameters GetDictionaryDetailByDictionaryName
type DictionaryNameReq struct {
	// in:path
	Name *string `json:"name,optional" path:"name"`
}

// The response data of task information | 定时任务信息
// swagger:model TaskInfo
type TaskInfo struct {
	BaseIDInfo
	// Status | 状态
	// max : 20
	Status *uint32 `json:"status,optional" validate:"omitempty,lt=20"`
	// Name | 任务名称
	// max length : 50
	Name *string `json:"name,optional" validate:"omitempty,max=50"`
	// TaskGroup | 任务分组
	// max length : 40
	TaskGroup *string `json:"taskGroup,optional" validate:"omitempty,max=40"`
	// CronExpression | 定时任务 Cron 表达式
	// max length : 80
	CronExpression *string `json:"cronExpression,optional" validate:"omitempty,max=80"`
	// Pattern | 任务的标识 (用于区分和确定要执行的任务)
	// max length : 100
	Pattern *string `json:"pattern,optional" validate:"omitempty,max=100"`
	// Payload | 任务需要数据(JSON 字符串)
	Payload *string `json:"payload,optional"`
}

// The response data of task list | 定时任务列表数据
// swagger:model TaskListResp
type TaskListResp struct {
	BaseDataInfo
	// Task list data | 定时任务列表数据
	Data TaskListInfo `json:"data"`
}

// Task list data | 定时任务列表数据
// swagger:model TaskListInfo
type TaskListInfo struct {
	BaseListInfo
	// The API list data | 定时任务列表数据
	Data []TaskInfo `json:"data"`
}

// Get task list request params | 定时任务列表请求参数
// swagger:model TaskListReq
type TaskListReq struct {
	PageInfo
	// Name | 任务名称
	Name *string `json:"name,optional"`
	// TaskGroup | 任务分组
	TaskGroup *string `json:"taskGroup,optional"`
}

// Task information response | 定时任务信息返回体
// swagger:model TaskInfoResp
type TaskInfoResp struct {
	BaseDataInfo
	// Task information | 定时任务数据
	Data TaskInfo `json:"data"`
}

// The response data of task log information | 任务日志信息
// swagger:model TaskLogInfo
type TaskLogInfo struct {
	// ID
	Id *uint64 `json:"id"`
	// StartedAt | 任务开始时间
	StartedAt *int64 `json:"startedAt,optional"`
	// FinishedAt | 任务完成时间
	FinishedAt *int64 `json:"finishedAt,optional"`
	// Result | 任务结果
	Result *uint32 `json:"result,optional"`
}

// The response data of task log list | 任务日志列表数据
// swagger:model TaskLogListResp
type TaskLogListResp struct {
	BaseDataInfo
	// TaskLog list data | 任务日志列表数据
	Data TaskLogListInfo `json:"data"`
}

// TaskLog list data | 任务日志列表数据
// swagger:model TaskLogListInfo
type TaskLogListInfo struct {
	BaseListInfo
	// The API list data | 任务日志列表数据
	Data []TaskLogInfo `json:"data"`
}

// Get task log list request params | 任务日志列表请求参数
// swagger:model TaskLogListReq
type TaskLogListReq struct {
	PageInfo
	// TaskId | 任务ID
	TaskId *uint64 `json:"taskId"`
	// Result | 任务结果
	Result *uint32 `json:"result"`
}

// TaskLog information response | 任务日志信息返回体
// swagger:model TaskLogInfoResp
type TaskLogInfoResp struct {
	BaseDataInfo
	// TaskLog information | 任务日志数据
	Data TaskLogInfo `json:"data"`
}

// The response data of email log information | 电子邮件日志信息
// swagger:model EmailLogInfo
type EmailLogInfo struct {
	BaseUUIDInfo
	// Target | 目标邮箱
	Target *string `json:"target,optional"`
	// Subject | 主题
	Subject *string `json:"subject,optional"`
	// Content | 内容
	Content *string `json:"content,optional"`
	// SendStatus | 发送状态
	SendStatus *uint32 `json:"sendStatus,optional"`
	// Provider | 服务提供商
	Provider *string `json:"provider,optional"`
}

// The response data of email log list | 电子邮件日志列表数据
// swagger:model EmailLogListResp
type EmailLogListResp struct {
	BaseDataInfo
	// EmailLog list data | 电子邮件日志列表数据
	Data EmailLogListInfo `json:"data"`
}

// EmailLog list data | 电子邮件日志列表数据
// swagger:model EmailLogListInfo
type EmailLogListInfo struct {
	BaseListInfo
	// The API list data | 电子邮件日志列表数据
	Data []EmailLogInfo `json:"data"`
}

// Get email log list request params | 电子邮件日志列表请求参数
// swagger:model EmailLogListReq
type EmailLogListReq struct {
	PageInfo
	// Target | 目标邮箱
	Target *string `json:"target,optional"`
	// Subject | 主题
	Subject *string `json:"subject,optional"`
	// Provider | 服务提供商
	Provider *string `json:"provider,optional"`
	// Send Status | 发送状态
	SendStatus *uint32 `json:"sendStatus,optional"`
}

// EmailLog information response | 电子邮件日志信息返回体
// swagger:model EmailLogInfoResp
type EmailLogInfoResp struct {
	BaseDataInfo
	// EmailLog information | 电子邮件日志数据
	Data EmailLogInfo `json:"data"`
}

// The response data of sms log information | 短信日志信息
// swagger:model SmsLogInfo
type SmsLogInfo struct {
	BaseUUIDInfo
	// Phone number | 手机号码
	PhoneNumber *string `json:"phoneNumber,optional"`
	// Content | 短信内容
	Content *string `json:"content,optional"`
	// Send status | 发送状态
	SendStatus *uint32 `json:"sendStatus,optional"`
	// Provider | 服务商
	Provider *string `json:"provider,optional"`
}

// The response data of sms log list | 短信日志列表数据
// swagger:model SmsLogListResp
type SmsLogListResp struct {
	BaseDataInfo
	// SmsLog list data | 短信日志列表数据
	Data SmsLogListInfo `json:"data"`
}

// SmsLog list data | 短信日志列表数据
// swagger:model SmsLogListInfo
type SmsLogListInfo struct {
	BaseListInfo
	// The API list data | 短信日志列表数据
	Data []SmsLogInfo `json:"data"`
}

// Get sms log list request params | 短信日志列表请求参数
// swagger:model SmsLogListReq
type SmsLogListReq struct {
	PageInfo
	// Phone number | 手机号码
	PhoneNumber *string `json:"phoneNumber,optional"`
	// Content | 短信内容
	Content *string `json:"content,optional"`
	// Provider | 服务商
	Provider *string `json:"provider,optional"`
	// Send Status | 发送状态
	SendStatus *uint32 `json:"sendStatus,optional"`
}

// SmsLog information response | 短信日志信息返回体
// swagger:model SmsLogInfoResp
type SmsLogInfoResp struct {
	BaseDataInfo
	// SmsLog information | 短信日志数据
	Data SmsLogInfo `json:"data"`
}

// The response data of sms provider information | 短信配置信息
// swagger:model SmsProviderInfo
type SmsProviderInfo struct {
	BaseIDInfo
	// Name | 名称
	Name *string `json:"name,optional"`
	// Secret id | 秘钥 ID
	SecretId *string `json:"secretId,optional"`
	// Secret key | 秘钥 Key
	SecretKey *string `json:"secretKey,optional"`
	// Region | 区域
	Region *string `json:"region,optional"`
	// Is default provider | 是否默认服务商
	IsDefault *bool `json:"isDefault,optional"`
}

// The response data of sms provider list | 短信配置列表数据
// swagger:model SmsProviderListResp
type SmsProviderListResp struct {
	BaseDataInfo
	// SmsProvider list data | 短信配置列表数据
	Data SmsProviderListInfo `json:"data"`
}

// SmsProvider list data | 短信配置列表数据
// swagger:model SmsProviderListInfo
type SmsProviderListInfo struct {
	BaseListInfo
	// The API list data | 短信配置列表数据
	Data []SmsProviderInfo `json:"data"`
}

// Get sms provider list request params | 短信配置列表请求参数
// swagger:model SmsProviderListReq
type SmsProviderListReq struct {
	PageInfo
	// Name | 名称
	Name *string `json:"name,optional"`
}

// SmsProvider information response | 短信配置信息返回体
// swagger:model SmsProviderInfoResp
type SmsProviderInfoResp struct {
	BaseDataInfo
	// SmsProvider information | 短信配置数据
	Data SmsProviderInfo `json:"data"`
}

// The response data of email provider information | 邮箱服务配置信息
// swagger:model EmailProviderInfo
type EmailProviderInfo struct {
	BaseIDInfo
	// Name | 名称
	Name *string `json:"name,optional"`
	// AuthType | 认证类型
	AuthType *uint32 `json:"authType,optional"`
	// Email address | 邮箱地址
	EmailAddr *string `json:"emailAddr,optional"`
	// Password | 密码
	Password *string `json:"password,optional"`
	// HostName | 主机名
	HostName *string `json:"hostName,optional"`
	// Identify | 身份标识
	Identify *string `json:"identify,optional"`
	// Secret | 密钥
	Secret *string `json:"secret,optional"`
	// Port | 端口
	Port *uint32 `json:"port,optional"`
	// Tls | 是否启用TLS
	Tls *bool `json:"tls,optional"`
	// Is default provider | 是否默认
	IsDefault *bool `json:"isDefault,optional"`
	// Sender name | 发件人名称
	SenderName *string `json:"senderName,optional"`
}

// The response data of email provider list | 邮箱服务配置列表数据
// swagger:model EmailProviderListResp
type EmailProviderListResp struct {
	BaseDataInfo
	// EmailProvider list data | 邮箱服务配置列表数据
	Data EmailProviderListInfo `json:"data"`
}

// EmailProvider list data | 邮箱服务配置列表数据
// swagger:model EmailProviderListInfo
type EmailProviderListInfo struct {
	BaseListInfo
	// The API list data | 邮箱服务配置列表数据
	Data []EmailProviderInfo `json:"data"`
}

// Get email provider list request params | 邮箱服务配置列表请求参数
// swagger:model EmailProviderListReq
type EmailProviderListReq struct {
	PageInfo
	// Name | 名称
	Name *string `json:"name,optional"`
	// Email address | 邮箱地址
	EmailAddr *string `json:"emailAddr,optional"`
}

// EmailProvider information response | 邮箱服务配置信息返回体
// swagger:model EmailProviderInfoResp
type EmailProviderInfoResp struct {
	BaseDataInfo
	// EmailProvider information | 邮箱服务配置数据
	Data EmailProviderInfo `json:"data"`
}

// swagger:model SendSmsReq
type SendSmsReq struct {
	// Phone number | 电话号码
	PhoneNumber string `json:"phoneNumber"`
	// The parameters | 参数
	Params string `json:"params"`
	// The template ID  | 模板 ID
	TemplateId *string `json:"templateId,optional"`
	// The app ID | App ID
	AppId *string `json:"appId,optional"`
	// The signuture name | 签名名称
	SignName *string `json:"signName,optional"`
	// The email provider | 邮件服务提供商
	Provider *string `json:"provider,optional"`
}

// swagger:model SendEmailReq
type SendEmailReq struct {
	// Target email address | 目标邮箱地址
	Target string `json:"target"`
	// The email subject | 邮件标题
	Subject string `json:"subject"`
	// The email content | 邮件内容
	Content string `json:"content"`
	// The email provider | 邮件服务提供商
	Provider *string `json:"provider,optional"`
}

// The response data of configuration information | 参数配置信息
// swagger:model ConfigurationInfo
type ConfigurationInfo struct {
	BaseIDInfo
	// Sort Number | 排序编号
	Sort *uint32 `json:"sort,optional"`
	// State true: normal false: ban | 状态 true 正常 false 禁用
	State *bool `json:"state,optional"`
	// Configurarion name | 配置名称
	Name *string `json:"name,optional"`
	// Configuration key | 配置的键名
	Key *string `json:"key,optional"`
	// Configuraion value | 配置的值
	Value *string `json:"value,optional"`
	// Configuration category | 配置的分类
	Category *string `json:"category,optional"`
	// Remark | 备注
	Remark *string `json:"remark,optional"`
}

// The response data of configuration list | 参数配置列表数据
// swagger:model ConfigurationListResp
type ConfigurationListResp struct {
	BaseDataInfo
	// Configuration list data | 参数配置列表数据
	Data ConfigurationListInfo `json:"data"`
}

// Configuration list data | 参数配置列表数据
// swagger:model ConfigurationListInfo
type ConfigurationListInfo struct {
	BaseListInfo
	// The API list data | Configuration列表数据
	Data []ConfigurationInfo `json:"data"`
}

// Get configuration list request params | 参数配置列表请求参数
// swagger:model ConfigurationListReq
type ConfigurationListReq struct {
	PageInfo
	// Name
	Name *string `json:"name,optional"`
	// Key
	Key *string `json:"key,optional"`
	// Category
	Category *string `json:"category,optional"`
}

// Configuration information response | 参数配置信息返回体
// swagger:model ConfigurationInfoResp
type ConfigurationInfoResp struct {
	BaseDataInfo
	// Configuration information | 参数配置数据
	Data ConfigurationInfo `json:"data"`
}
