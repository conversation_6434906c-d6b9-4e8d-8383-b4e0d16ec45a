// Code generated by goctl. DO NOT EDIT.
package types

// The basic response with data | 基础带数据信息
// swagger:model BaseDataInfo
type BaseDataInfo struct {
	// Error code | 错误代码
	Code int `json:"code"`
	// Message | 提示信息
	Msg string `json:"msg"`
	// Data | 数据
	Data string `json:"data,omitempty"`
}

// The basic response with data | 基础带数据信息
// swagger:model BaseListInfo
type BaseListInfo struct {
	// The total number of data | 数据总数
	Total uint64 `json:"total"`
	// Data | 数据
	Data string `json:"data,omitempty"`
}

// The basic response without data | 基础不带数据信息
// swagger:model BaseMsgResp
type BaseMsgResp struct {
	// Error code | 错误代码
	Code int `json:"code"`
	// Message | 提示信息
	Msg string `json:"msg"`
}

// The page request parameters | 列表请求参数
// swagger:model PageInfo
type PageInfo struct {
	// Page number | 第几页
	// required : true
	// min : 0
	Page uint64 `json:"page" validate:"required,number,gt=0"`
	// Page size | 单页数据行数
	// required : true
	// max : 100000
	PageSize uint64 `json:"pageSize" validate:"required,number,lt=100000"`
}

// Basic ID request | 基础ID参数请求
// swagger:model IDReq
type IDReq struct {
	// ID
	// Required: true
	Id uint64 `json:"id" validate:"number"`
}

// Basic IDs request | 基础ID数组参数请求
// swagger:model IDsReq
type IDsReq struct {
	// IDs
	// Required: true
	Ids []uint64 `json:"ids"`
}

// Basic ID request | 基础ID地址参数请求
// swagger:model IDPathReq
type IDPathReq struct {
	// ID
	// Required: true
	Id uint64 `path:"id"`
}

// Basic ID request (int32) | 基础ID参数请求 (int32)
// swagger:model IDInt32Req
type IDInt32Req struct {
	// ID
	// Required: true
	Id int32 `json:"id" validate:"number"`
}

// Basic IDs request (int32) | 基础ID数组参数请求 (int32)
// swagger:model IDsInt32Req
type IDsInt32Req struct {
	// IDs
	// Required: true
	Ids []int32 `json:"ids"`
}

// Basic ID request (int32) | 基础ID地址参数请求 (int32)
// swagger:model IDInt32PathReq
type IDInt32PathReq struct {
	// ID
	// Required: true
	Id int32 `path:"id"`
}

// Basic ID request (uint32) | 基础ID参数请求 (uint32)
// swagger:model IDUint32Req
type IDUint32Req struct {
	// ID
	// Required: true
	Id uint32 `json:"id" validate:"number"`
}

// Basic IDs request (uint32) | 基础ID数组参数请求 (uint32)
// swagger:model IDsUint32Req
type IDsUint32Req struct {
	// IDs
	// Required: true
	Ids []uint32 `json:"ids"`
}

// Basic ID request (uint32) | 基础ID地址参数请求 (uint32)
// swagger:model IDUint32PathReq
type IDUint32PathReq struct {
	// ID
	// Required: true
	Id uint32 `path:"id"`
}

// Basic ID request (int64) | 基础ID参数请求 (int64)
// swagger:model IDInt64Req
type IDInt64Req struct {
	// ID
	// Required: true
	Id int64 `json:"id" validate:"number"`
}

// Basic IDs request (int64) | 基础ID数组参数请求 (int64)
// swagger:model IDsInt64Req
type IDsInt64Req struct {
	// IDs
	// Required: true
	Ids []int64 `json:"ids"`
}

// Basic ID request (int64) | 基础ID地址参数请求 (int64)
// swagger:model IDInt64PathReq
type IDInt64PathReq struct {
	// ID
	// Required: true
	Id int64 `path:"id"`
}

// Basic UUID request in path | 基础UUID地址参数请求
// swagger:model UUIDPathReq
type UUIDPathReq struct {
	// ID
	// Required: true
	Id string `path:"id"`
}

// Basic UUID request | 基础UUID参数请求
// swagger:model UUIDReq
type UUIDReq struct {
	// ID
	// required : true
	// max length : 36
	// min length : 36
	Id string `json:"id" validate:"required,len=36"`
}

// Basic UUID array request | 基础UUID数组参数请求
// swagger:model UUIDsReq
type UUIDsReq struct {
	// Ids
	// Required: true
	Ids []string `json:"ids"`
}

// The base ID response data | 基础ID信息
// swagger:model BaseIDInfo
type BaseIDInfo struct {
	// ID
	Id *uint64 `json:"id,optional"`
	// Create date | 创建日期
	CreatedAt *int64 `json:"createdAt,optional"`
	// Update date | 更新日期
	UpdatedAt *int64 `json:"updatedAt,optional"`
}

// The base ID response data (int64) | 基础ID信息 (int64)
// swagger:model BaseIDInt64Info
type BaseIDInt64Info struct {
	// ID
	Id *int64 `json:"id,optional"`
	// Create date | 创建日期
	CreatedAt *int64 `json:"createdAt,optional"`
	// Update date | 更新日期
	UpdatedAt *int64 `json:"updatedAt,optional"`
}

// The base ID response data (int32) | 基础ID信息 (int32)
// swagger:model BaseIDInt32Info
type BaseIDInt32Info struct {
	// ID
	Id *int32 `json:"id,optional"`
	// Create date | 创建日期
	CreatedAt *int64 `json:"createdAt,optional"`
	// Update date | 更新日期
	UpdatedAt *int64 `json:"updatedAt,optional"`
}

// The base ID response data (uint32) | 基础ID信息 (uint32)
// swagger:model BaseIDUint32Info
type BaseIDUint32Info struct {
	// ID
	Id *uint32 `json:"id,optional"`
	// Create date | 创建日期
	CreatedAt *int64 `json:"createdAt,optional"`
	// Update date | 更新日期
	UpdatedAt *int64 `json:"updatedAt,optional"`
}

// The base UUID response data | 基础UUID信息
// swagger:model BaseUUIDInfo
type BaseUUIDInfo struct {
	// ID
	Id *string `json:"id,optional"`
	// Create date | 创建日期
	CreatedAt *int64 `json:"createdAt,optional"`
	// Update date | 更新日期
	UpdatedAt *int64 `json:"updatedAt,optional"`
}

// Empty request | 无参数请求
// swagger:model EmptyReq
type EmptyReq struct {
}

// The response data of configuration information | 参数配置信息
// swagger:model ConfigurationInfo
type ConfigurationInfo struct {
	BaseIDInfo
	// Sort Number | 排序编号
	Sort *uint32 `json:"sort,optional"`
	// State true: normal false: ban | 状态 true 正常 false 禁用
	State *bool `json:"state,optional"`
	// Configurarion name | 配置名称
	Name *string `json:"name,optional"`
	// Configuration key | 配置的键名
	Key *string `json:"key,optional"`
	// Configuraion value | 配置的值
	Value *string `json:"value,optional"`
	// Configuration category | 配置的分类
	Category *string `json:"category,optional"`
	// Remark | 备注
	Remark *string `json:"remark,optional"`
}

// The response data of configuration list | 参数配置列表数据
// swagger:model ConfigurationListResp
type ConfigurationListResp struct {
	BaseDataInfo
	// Configuration list data | 参数配置列表数据
	Data ConfigurationListInfo `json:"data"`
}

// Configuration list data | 参数配置列表数据
// swagger:model ConfigurationListInfo
type ConfigurationListInfo struct {
	BaseListInfo
	// The API list data | Configuration列表数据
	Data []ConfigurationInfo `json:"data"`
}

// Get configuration list request params | 参数配置列表请求参数
// swagger:model ConfigurationListReq
type ConfigurationListReq struct {
	PageInfo
	// Name
	Name *string `json:"name,optional"`
	// Key
	Key *string `json:"key,optional"`
	// Category
	Category *string `json:"category,optional"`
}

// Configuration information response | 参数配置信息返回体
// swagger:model ConfigurationInfoResp
type ConfigurationInfoResp struct {
	BaseDataInfo
	// Configuration information | 参数配置数据
	Data ConfigurationInfo `json:"data"`
}

// Get public system configuration request params | 获取公开系统参数请求参数
// swagger:model ConfigurationtReq
type ConfigurationtReq struct {
	// Name
	Name *string `json:"name,optional"`
}

// Get public system configuration response | 获取公开系统参数返回体
// swagger:model ConfigurationResp
type ConfigurationResp struct {
	BaseDataInfo
	// Configuration information | 参数配置数据
	Data ConfigurationInfo `json:"data"`
}

type User struct {
	UserId           int64  `json:"id"`               // 用户id
	Nickname         string `json:"nickname"`         // 用户昵称
	Mobile           string `json:"mobile"`           // 手机
	Email            string `json:"email"`            // 邮箱
	RealNameVerified int32  `json:"realNameVerified"` // 实名认证状态 0:未认证 1:已认证
	RealName         string `json:"realName"`         // 真实姓名
	IdCard           string `json:"idCard"`           // 身份证号
	Avatar           string `json:"avatar"`           // 头像
	Info             string `json:"info"`             // 个人介绍
	Sex              int64  `json:"sex"`              // 性别
	Roles            string `json:"roles"`            // 角色
	BackgroundImage  string `json:"backgroundImage"`  // 个人中心背景图片
	Password         string `json:"password"`         // 密码
	HomePage         string `json:"homePage"`         // 个人主页
	Description      string `json:"description"`      // 个人描述
	Score            int    `json:"score"`            // 积分
	DelState         int    `json:"delState"`         // 状态
	TopicCount       int    `json:"topicCount"`       // 帖子数量
	CommentCount     int    `json:"commentCount"`     // 跟帖数量
	FollowCount      int    `json:"followCount"`      // 关注数量
	FansCount        int    `json:"fansCount"`        // 粉丝数量
	LikeCount        int    `json:"likeCount"`        // 点赞数量
	ForbiddenEndTime int64  `json:"forbiddenEndTime"` // 禁言开始时间
	MutedEndTime     int64  `json:"mutedEndTime"`     // 禁言结束时间
	CreateTime       int64  `json:"createTime"`       // 注册时间
}

type AuthTokenData struct {
	AccessToken  string `json:"accessToken"`
	AccessExpire int64  `json:"accessExpire"`
	RefreshAfter int64  `json:"refreshAfter"`
}

// swagger:model VerificationReq
type VerificationReq struct {
	Mobile     string `json:"mobile"`
	TemplateId string `json:"templateId,optional"`
}

// swagger:model VerificationResp
type VerificationResp struct {
	BaseDataInfo
	Data VerificationRespData `json:"data"`
}

type VerificationRespData struct {
	Id  string `json:"id"`
	Msg string `json:"msg"`
}

// swagger:model GetSendSmsCodeReq
type GetSendSmsCodeReq struct {
	Mobile string `json:"mobile"`
}

// swagger:model GetSendSmsCodeResp
type GetSendSmsCodeResp struct {
	BaseDataInfo
	Data GetSendSmsCodeRespData `json:"data"`
}

type GetSendSmsCodeRespData struct {
	Mobile string `json:"mobile"`
	Code   string `json:"code"`
}

// swagger:model GeetestVerifyReq
type GeetestVerifyReq struct {
	LotNumber     string `json:"lotNumber"`
	CaptchaOutput string `json:"captchaOutput"`
	PassToken     string `json:"passToken"`
	GenTime       string `json:"genTime"`
}

// swagger:model GeetestVerifyResp
type GeetestVerifyResp struct {
	BaseDataInfo
	Data GeetestVerifyRespData `json:"data"`
}

type GeetestVerifyRespData struct {
	Success bool `json:"success"`
}

// swagger:model RegisterReq
type RegisterReq struct {
	Mobile     string `json:"mobile"`
	Password   string `json:"password"`
	VerifyCode string `json:"verifyCode"`
}

// swagger:model RegisterResp
type RegisterResp struct {
	BaseDataInfo
	Data AuthTokenData `json:"data"`
}

// swagger:model VerifyLoginReq
type VerifyLoginReq struct {
	Mobile     string `json:"mobile"`
	VerifyCode string `json:"verifyCode"`
}

// swagger:model VerifyLoginByAuthTypeReq
type VerifyLoginByAuthTypeReq struct {
	Mobile     string `json:"mobile"`
	VerifyCode string `json:"verifyCode"`
	AuthType   string `json:"authType"`
}

// swagger:model LoginReq
type LoginReq struct {
	Mobile   string `json:"mobile"`
	Password string `json:"password"`
}

// swagger:model LoginResp
type LoginResp struct {
	BaseDataInfo
	Data AuthTokenData `json:"data"`
}

// swagger:model WXMiniAuthReq
type WXMiniAuthReq struct {
	Code          string `json:"code"`
	IV            string `json:"iv"`
	EncryptedData string `json:"encryptedData"`
}

// swagger:model WXMiniAuthResp
type WXMiniAuthResp struct {
	BaseDataInfo
	Data AuthTokenData `json:"data"`
}

// swagger:model WXAuthReq
type WXAuthReq struct {
	Code string `json:"code"`
}

// swagger:model WXAuthResp
type WXAuthResp struct {
	BaseDataInfo
	Data WXAuthRespData `json:"data"`
}

type WXAuthRespData struct {
	Registered   bool   `json:"registered"`
	AuthKey      string `json:"authKey"`
	AuthType     string `json:"authType"`
	Avatar       string `json:"avatar"`
	NickName     string `json:"nickname"`
	AccessToken  string `json:"accessToken"`
	AccessExpire int64  `json:"accessExpire"`
	RefreshAfter int64  `json:"refreshAfter"`
}

// swagger:model RegisterByAuthIdReq
type RegisterByAuthIdReq struct {
	Mobile     string `json:"mobile"`
	Password   string `json:"password"`
	AuthType   string `json:"authType"`
	AuthKey    string `json:"authKey"`
	Avatar     string `json:"avatar"`
	NickName   string `json:"nickname"`
	VerifyCode string `json:"verifyCode"`
}

// swagger:model BindWxByMobileReq
type BindWxByMobileReq struct {
	Mobile     string `json:"mobile"`
	AuthKey    string `json:"authKey"`
	AuthType   string `json:"authType"`
	VerifyCode string `json:"verifyCode"`
}

// swagger:model UserInfoReq
type UserInfoReq struct {
}

// swagger:model UserInfoByUserIdReq
type UserInfoByUserIdReq struct {
	UserId int64 `json:"userId"`
}

// swagger:model UserInfoByUserIdResp
type UserInfoByUserIdResp struct {
	BaseDataInfo
	Data UserInfoByUserIdRespData `json:"data"`
}

type UserInfoByUserIdRespData struct {
	IsFans   bool `json:"isFans"`
	IsFollow bool `json:"isFollow"`
	UserInfo User `json:"userInfo"`
}

// swagger:model UserInfoByMobileReq
type UserInfoByMobileReq struct {
	Mobile string `json:"mobile"`
}

// swagger:model UserInfoByNameReq
type UserInfoByNameReq struct {
	Nickname string `json:"nickname"`
}

// swagger:model AppletUserInfoResp
type AppletUserInfoResp struct {
	BaseDataInfo
	Data AppletUserInfoRespData `json:"data"`
}

type AppletUserInfoRespData struct {
	UserInfo User `json:"userInfo"`
}

type SetUserEmailRespData struct {
	Email string `json:"email"`
}

// swagger:model SetUserEmailReq
type SetUserEmailReq struct {
	UserId int64  `json:"userId"`
	Email  string `json:"email"`
	Code   string `json:"code"`
}

// swagger:model SetUserEmailResp
type SetUserEmailResp struct {
	BaseDataInfo
	Data SetUserEmailRespData `json:"data"`
}

type RealNameAuthRespData struct {
	RealNameVerified int32 `json:"RealNameVerified"`
}

// swagger:model RealNameAuthReq
type RealNameAuthReq struct {
	UserId   int64  `json:"userId"`
	RealName string `json:"realName"`
	IdCard   string `json:"idCard"`
}

// swagger:model RealNameAuthResp
type RealNameAuthResp struct {
	BaseDataInfo
	Data RealNameAuthRespData `json:"data"`
}

// swagger:model SetPasswordReq
type SetPasswordReq struct {
	Password   string `json:"password"`
	RePassword string `json:"rePassword"`
}

// swagger:model SetPasswordResp
type SetPasswordResp struct {
	BaseDataInfo
	Data AuthTokenData `json:"data"`
}

// swagger:model UpdatePasswordReq
type UpdatePasswordReq struct {
	VerifyCode string `json:"verifyCode"`
	Password   string `json:"password"`
	RePassword string `json:"rePassword"`
}

// swagger:model UpdatePasswordResp
type UpdatePasswordResp struct {
	BaseDataInfo
	Data AuthTokenData `json:"data"`
}

// swagger:model SetNicknameReq
type SetNicknameReq struct {
	Nickname string `json:"nickname"`
}

// swagger:model SetNicknameResp
type SetNicknameResp struct {
	BaseDataInfo
	Data SetNicknameRespData `json:"data"`
}

type SetNicknameRespData struct {
	Nickname string `json:"nickname"`
}

// swagger:model SetAvatarReq
type SetAvatarReq struct {
	Avatar string `json:"avatar"`
}

// swagger:model SetAvatarResp
type SetAvatarResp struct {
	BaseDataInfo
	Data SetAvatarRespData `json:"data"`
}

type SetAvatarRespData struct {
	Avatar string `json:"avatar"`
}

// swagger:model SetUserInfoReq
type SetUserInfoReq struct {
	Info string `json:"info"`
}

// swagger:model SetAppletUserInfoResp
type SetAppletUserInfoResp struct {
	BaseDataInfo
	Data SetAppletUserInfoRespData `json:"data"`
}

type SetAppletUserInfoRespData struct {
	Info string `json:"info"`
}

// swagger:model SetUserSexReq
type SetUserSexReq struct {
	Sex int64 `json:"sex"`
}

// swagger:model SetUserSexResp
type SetUserSexResp struct {
	BaseDataInfo
	Data SetUserSexRespData `json:"data"`
}

type SetUserSexRespData struct {
	Sex int64 `json:"sex"`
}

// swagger:model SetUserHomePageReq
type SetUserHomePageReq struct {
	HomePage string `json:"homePage"`
}

// swagger:model SetUserHomePageResp
type SetUserHomePageResp struct {
	BaseDataInfo
	Data SetUserHomePageRespData `json:"data"`
}

type SetUserHomePageRespData struct {
	HomePage string `json:"homePage"`
}

// swagger:model SetUserDescriptionReq
type SetUserDescriptionReq struct {
	Description string `json:"description"`
}

// swagger:model SetUserDescriptionResp
type SetUserDescriptionResp struct {
	BaseDataInfo
	Data SetUserDescriptionRespData `json:"data"`
}

type SetUserDescriptionRespData struct {
	Description string `json:"description"`
}

// swagger:model SetBackgroundImageReq
type SetBackgroundImageReq struct {
	BackgroundImage string `json:"backgroundImage"`
}

// swagger:model SetBackgroundImageResp
type SetBackgroundImageResp struct {
	BaseDataInfo
	Data SetBackgroundImageRespData `json:"data"`
}

type SetBackgroundImageRespData struct {
	BackgroundImage string `json:"backgroundImage"`
}

// 创建用户移动端sso登录session
// swagger:model CreateUserMobileSsoLoginSessionReq
type CreateUserMobileSsoLoginSessionReq struct {
	DeviceId string `json:"deviceId"`
}

// swagger:model CreateUserMobileSsoLoginSessionResp
type CreateUserMobileSsoLoginSessionResp struct {
	BaseDataInfo
	Data CreateUserMobileSsoLoginSessionRespData `json:"data"`
}

type CreateUserMobileSsoLoginSessionRespData struct {
	SessionId string `json:"sessionId"`
}

// 获取用户移动端sso登录ticket
// swagger:model GetUserMobileSsoTicketReq
type GetUserMobileSsoTicketReq struct {
	DeviceId       string `json:"deviceId"`
	AppId          string `json:"appId"`
	AppPackageName string `json:"appPackageName"`
}

// swagger:model GetUserMobileSsoTicketResp
type GetUserMobileSsoTicketResp struct {
	BaseDataInfo
	Data GetUserMobileSsoTicketRespData `json:"data"`
}

type GetUserMobileSsoTicketRespData struct {
	Ticket   string `json:"ticket"`
	Nickname string `json:"nickname"`
	Avatar   string `json:"avatar"`
}

// 获取用户移动端sso登录用户信息
// swagger:model GetUserMobileSsoUserLoginInfoReq
type GetUserMobileSsoUserLoginInfoReq struct {
	GetUserMobileSsoTicketReq
	Ticket string `json:"ticket"`
}

// swagger:model GetUserMobileSsoUserLoginInfoResp
type GetUserMobileSsoUserLoginInfoResp struct {
	BaseDataInfo
	Data AuthTokenData `json:"data"`
}

// 删除用户移动端sso登录session
// swagger:model DeleteUserMobileSsoLoginSessionReq
type DeleteUserMobileSsoLoginSessionReq struct {
	GetUserMobileSsoTicketReq
	DeleteAll int64 `json:"deleteAll,optional"`
}

// swagger:model DeleteUserMobileSsoLoginSessionResp
type DeleteUserMobileSsoLoginSessionResp struct {
	BaseDataInfo
}

// The permission code for front end permission control | 权限码： 用于前端权限控制
// swagger:model PermCodeResp
type PermCodeResp struct {
	BaseDataInfo
	// Permission code data | 权限码数据
	Data []string `json:"data"`
}

// swagger:model SendEmailCodeReq
type SendEmailCodeReq struct {
	Email     string `json:"email"`
	ProductId uint64 `json:"productId"`
}

// swagger:model SendEmailCodeResp
type SendEmailCodeResp struct {
	BaseDataInfo
	Data SendEmailCodeData `json:"data"`
}

type SendEmailCodeData struct {
	Status bool `json:"status"`
}

// swagger:model VerifyEmailCodeReq
type VerifyEmailCodeReq struct {
	Email string `json:"email"`
	Code  string `json:"code"`
}

// swagger:model VerifyEmailCodeResp
type VerifyEmailCodeResp struct {
	BaseDataInfo
	Data SendEmailCodeData `json:"data"`
}

// swagger:model GetUserSigReq
type GetUserSigReq struct {
	UserId string `json:"userId"`
}

// swagger:model GetUserSigResp
type GetUserSigResp struct {
	BaseDataInfo
	Data GetUserSigRespData `json:"data"`
}

type GetUserSigRespData struct {
	UserSig string `json:"userSig"`
}

// swagger:model ThumbupReq
type ThumbupReq struct {
	BizId  string `json:"bizId"`  // 业务id
	ObjId  int64  `json:"objId"`  // 点赞对象id
	UserId int64  `json:"userId"` // 用户id
	// required : true
	LikeType int64 `json:"likeType" validate:"required,oneof=1 2"` // 类型 1:点赞  2:取消点赞 3:点踩
}

// swagger:model ThumbupResp
type ThumbupResp struct {
	BaseDataInfo
	Data ThumbupRespData `json:"data"`
}

type ThumbupRespData struct {
	BizId      string `json:"bizId"`      // 业务id
	ObjId      int64  `json:"objId"`      // 点赞对象id
	LikeNum    int64  `json:"likeNum"`    // 点赞数
	DislikeNum int64  `json:"dislikeNum"` // 点踩数
}

// swagger:model IsThumbupReq
type IsThumbupReq struct {
	BizId    string `json:"bizId"`    // 业务id
	TargetId int64  `json:"targetId"` // 点赞对象id
	UserId   int64  `json:"userId"`   // 用户id
}

// swagger:model IsThumbupResp
type IsThumbupResp struct {
	BaseDataInfo
	Data IsThumbupRespData `json:"data"`
}

type IsThumbupRespData struct {
	UserThumbup UserThumbup `json:"userThumbup"`
}

type UserThumbup struct {
	UserId      int64 `json:"userId"`
	ThumbupTime int64 `json:"thumbupTime"`
	LikeType    int64 `json:"likeType"`
}

type LikeRecordItem struct {
	Id         int64    `json:"id"`         // 主键ID
	BizId      string   `json:"bizId"`      // 业务ID
	ObjId      int64    `json:"objId"`      // 评论目标id
	UserId     int64    `json:"userId"`     // 评论用户ID
	UserInfo   UserInfo `json:"userInfo"`   // 评论用户信息
	CreateTime int64    `json:"createTime"` // 创建时间
	UpdateTime int64    `json:"updateTime"` // 最后修改时间
}

// swagger:model ArticleLikeListReq
type ArticleLikeListReq struct {
	BizId    string `json:"bizId"`    // 业务ID
	ObjId    int64  `json:"objId"`    // 评论目标id
	Cursor   int64  `json:"cursor"`   // 游标
	PageSize int64  `json:"pageSize"` // 游标
	SortType int64  `json:"sortType"`
}

// swagger:model ArticleLikeListResp
type ArticleLikeListResp struct {
	BaseDataInfo
	Data ArticleLikeListRespData `json:"data"`
}

type ArticleLikeListRespData struct {
	Items  []LikeRecordItem `json:"items"`
	Cursor int64            `json:"cursor"` // 游标
	IsEnd  bool             `json:"isEnd"`  // 是否结束
}

// swagger:model FollowUserInfo
type FollowUserInfo struct {
	Id       int64  `json:"id"`       // 被关注者ID
	UserId   int64  `json:"userId"`   // 被关注者ID
	NickName string `json:"nickName"` // 被关注者昵称
	Avatar   string `json:"avatar"`   // 被关注者头像
	IsFollow bool   `json:"isFollow"` // 是否对方关注对方
	IsFans   bool   `json:"isFans"`   // 是否被关注
}

// swagger:model FansUserInfo
type FansUserInfo struct {
	Id       int64  `json:"id"`       // 被关注者ID
	UserId   int64  `json:"userId"`   // 粉丝ID
	NickName string `json:"nickName"` // 粉丝昵称
	Avatar   string `json:"avatar"`   // 粉丝头像
	IsFollow bool   `json:"isFollow"` // 是否对方关注对方
	IsFans   bool   `json:"isFans"`   // 是否被关注
}

// swagger:model FollowReq
type FollowReq struct {
	UserId         int64 `json:"userId"`         // 关注者
	FollowedUserId int64 `json:"followedUserId"` // 被关注者
}

// 添加 BaseDataInfo
// swagger:model FollowResp
type FollowResp struct {
	BaseDataInfo
	Data FollowRespData `json:"data"`
}

type FollowRespData struct {
	UserId      int64 `json:"userId"`      // 关注者
	FollowCount int64 `json:"followCount"` // 关注总数
	FansCount   int64 `json:"fansCount"`   // 粉丝总数
}

// swagger:model UnFollowReq
type UnFollowReq struct {
	UserId         int64 `json:"userId"`         // 关注者
	FollowedUserId int64 `json:"followedUserId"` // 被关注者
}

// 添加 BaseDataInfo
// swagger:model UnFollowResp
type UnFollowResp struct {
	BaseDataInfo
	Data FollowRespData `json:"data"`
}

// swagger:model FollowListReq
type FollowListReq struct {
	Id       int64 `json:"id"` // 查询每页最后一个用户id
	UserId   int64 `json:"userId"`
	Cursor   int64 `json:"cursor"` //游标
	PageSize int64 `json:"pageSize"`
}

type FollowItem struct {
	Id             int64          `json:"id"`
	UserId         int64          `json:"userId"`
	FollowedUserId int64          `json:"followedUserId"` // 被关注者id
	FollowUserInfo FollowUserInfo `json:"followUserInfo"`
	FansCount      int64          `json:"fansCount"` // 被关注者总数
	CreateTime     int64          `json:"createTime"`
}

// swagger:model FollowListResp
type FollowListResp struct {
	BaseDataInfo
	Data FollowListRespData `json:"data"`
}

type FollowListRespData struct {
	Items  []FollowItem `json:"followItem"`
	Cursor int64        `json:"cursor"` // 游标
	IsEnd  bool         `json:"isEnd"`  // 是否结束
	Id     int64        `json:"userId"` // 查询每页最后一个用户id
}

// swagger:model FansListReq
type FansListReq struct {
	UserId   int64 `json:"userId"`
	Cursor   int64 `json:"cursor"` // 游标
	PageSize int64 `json:"pageSize"`
	Id       int64 `json:"id"` // 从那个id开始查询
}

type FansItem struct {
	Id           int64        `json:"id"`
	UserId       int64        `json:"userId"`
	FansUserId   int64        `json:"fansUserId"` // 粉丝id
	FansUserInfo FansUserInfo `json:"fansUserInfo"`
	FansCount    int64        `json:"fansCount"` // 粉丝总数
	CreateTime   int64        `json:"createTime"`
}

// swagger:model FansListResp
type FansListResp struct {
	BaseDataInfo
	Data FansListRespData `json:"data"`
}

type FansListRespData struct {
	Items  []FansItem `json:"fansItem"`
	Cursor int64      `json:"cursor"` // 游标
	IsEnd  bool       `json:"isEnd"`  // 是否结束
	Id     int64      `json:"userId"` // 查询每页最后一个用户id
}

// swagger:model FromUserInfo
type FromUserInfo struct {
	Id              int64  `json:"id"`              // 用户id
	Nickname        string `json:"nickname"`        // 用户昵称
	Mobile          string `json:"mobile"`          // 手机
	Avatar          string `json:"avatar"`          // 头像
	Info            string `json:"info"`            // 个人介绍
	Sex             int64  `json:"sex"`             // 性别
	BackgroundImage string `json:"backgroundImage"` // 个人中心背景图片
	HomePage        string `json:"homePage"`        // 个人主页
	Description     string `json:"description"`     // 个人描述
}

type MessageItem struct {
	Id           int64        `json:"id"`     // 主键
	FromId       int64        `json:"fromId"` // 消息发送人ID
	FromUserInfo FromUserInfo `json:"fromUserInfo"`
	UserId       int64        `json:"userId"`       // 消息接收人ID
	Title        string       `json:"title"`        // 消息标题
	Content      string       `json:"content"`      // 消息内容
	QuoteContent string       `json:"quoteContent"` // 引用内容
	Type         int64        `json:"type"`         // 消息类型
	ExtraData    string       `json:"extraData"`    // 扩展数据
	Status       int64        `json:"status"`       // 状态：0：未读、1：已读
	CreateTime   int64        `json:"createTime"`   // 创建时间
	UpdateTime   int64        `json:"updateTime"`   // 更新时间
}

// swagger:model ReadMessageReq
type ReadMessageReq struct {
	Id int64 `json:"id"` // 消息ID
}

// swagger:model ReadMessageResp
type ReadMessageResp struct {
	BaseDataInfo
	Data ReadMessageRespData `json:"data"`
}

type ReadMessageRespData struct {
	Status bool `json:"status"` // 阅读消息
}

// swagger:model GetMessageListReq
type GetMessageListReq struct {
	Cursor   int64 `json:"cursor"`
	PageSize int64 `json:"pageSize"`
	MsgType  int64 `json:"msgType"`
}

// swagger:model GetMessageListResp
type GetMessageListResp struct {
	BaseDataInfo
	Data GetMessageListRespData `json:"data"`
}

type GetMessageListRespData struct {
	Cursor int64         `json:"cursor"`
	IsEnd  bool          `json:"isEnd"`
	Items  []MessageItem `json:"items"`
}

type ProductItem struct {
	ID           int64  `json:"id"`
	UserID       int64  `json:"userId"`
	Title        string `json:"title"`
	Sn           string `json:"sn"`
	Color        string `json:"color"`
	Merchant     string `json:"merchant"`
	Language     string `json:"language"`
	RegisterTime string `json:"registerTime"`
}

// swagger:model AddUserProductReq
type AddUserProductReq struct {
	Sn string `json:"sn"`
}

// swagger:model AddUserProductResp
type AddUserProductResp struct {
	BaseDataInfo
	Data AddUserProductRespData `json:"data"`
}

type AddUserProductRespData struct {
	ID           int64  `json:"id"`
	UserID       int64  `json:"userId"`
	Title        string `json:"title"`
	Sn           string `json:"sn"`
	Color        string `json:"color"`
	Merchant     string `json:"merchant"`
	Language     string `json:"language"`
	RegisterTime string `json:"registerTime"`
}

// swagger:model GetUserProductListReq
type GetUserProductListReq struct {
	Cursor   int64 `json:"cursor"`
	PageSize int64 `json:"pageSize"`
	LastId   int64 `json:"lastId"`
}

// swagger:model GetUserProductListResp
type GetUserProductListResp struct {
	BaseDataInfo
	Data GetUserProductListRespData `json:"data"`
}

type GetUserProductListRespData struct {
	Cursor int64         `json:"cursor"`
	IsEnd  bool          `json:"isEnd"`
	LastId int64         `json:"lastId"`
	Items  []ProductItem `json:"items"`
}

// swagger:model CommonResp
type CommonResp struct {
	BaseDataInfo
	Data CommonRespData `json:"data"`
}

type CommonRespData struct {
	Message string `json:"message"`
}

type FileUrl struct {
	Url string `json:"url"`
}

type UploadFiles struct {
	FileUrls []string `json:"fileUrls"`
}

// swagger:model UploadCoverResp
type UploadCoverResp struct {
	BaseDataInfo
	Data FileUrl `json:"data"`
}

// swagger:model TecentCloudUploadTokenReq
type TecentCloudUploadTokenReq struct {
	FileType string `json:"fileType"`
}

// swagger:model TecentCloudUploadTokenResp
type TecentCloudUploadTokenResp struct {
	BaseDataInfo
	Data TecentCloudUploadTokenData `json:"data"`
}

type TecentCloudUploadTokenData struct {
	TmpSecretID    string `json:"tmpSecretId"`    // 临时密钥ID
	TmpSecretKey   string `json:"tmpSecretKey"`   // 临时密钥Key
	SessionToken   string `json:"sessionToken"`   // 临时会话Token
	StartTime      int64  `json:"startTime"`      // 开始时间
	ExpiredTime    int64  `json:"expiredTime"`    // 过期时间
	ExpireDuration int64  `json:"expireDuration"` // 过期时间（秒）
	Bucket         string `json:"bucket"`         // 存储桶名称
	Region         string `json:"region"`         // 地区
	AllowPrefix    string `json:"allowPrefix"`    // 允许的前缀
	CosHost        string `json:"cosHost"`        // COS主机地址
	CustomDomain   string `json:"customDomain"`   // 自定义域名
}

// swagger:model TecentCloudUploadPostPolicyReq
type TecentCloudUploadPostPolicyReq struct {
	FileType string `json:"fileType"`
	FileExt  string `json:"fileExt"`
}

// swagger:model TecentCloudUploadPostPolicyResp
type TecentCloudUploadPostPolicyResp struct {
	BaseDataInfo
	Data TecentCloudUploadPostPolicyData `json:"data"`
}

type TecentCloudUploadPostPolicyData struct {
	CosHost        string `json:"cosHost"`        // COS主机地址
	CustomDomain   string `json:"customDomain"`   // 自定义域名
	CosKey         string `json:"cosKey"`         // COS密钥
	Policy         string `json:"policy"`         // 策略
	QSignAlgorithm string `json:"qSignAlgorithm"` // 签名算法
	QAk            string `json:"qAk"`            // 访问密钥
	QKeyTime       string `json:"qKeyTime"`       // 密钥时间
	QSignature     string `json:"qSignature"`     // 签名
	FileURL        string `json:"fileURL"`        // 文件URL
	SecurityToken  string `json:"securityToken"`  // 如果 SecretId、SecretKey 是临时密钥，要返回对应的 sessionToken 的值
}

// swagger:model UploadFilesResp
type UploadFilesResp struct {
	BaseDataInfo
	Data UploadFilesRespData `json:"data"`
}

type UploadFilesRespData struct {
	Data UploadFiles `json:"data"`
}

// swagger:model PublishReq
type PublishReq struct {
	// required : true
	Title string `json:"title" validate:"required"`
	// required : true
	Content string `json:"content" validate:"required"`
	// required : true
	Description string `json:"description" validate:"required"`
	// required : true
	Cover string `json:"cover" validate:"required"`
	// required : true
	Videos string `json:"videos" validate:"required"`
	// required : true
	GroupId int64  `json:"groupId" validate:"required,numeric"`
	TagIds  string `json:"tagIds"`
	// required : true
	Type int64 `json:"type" validate:"required,oneof=1 2"`
}

// swagger:model PublishResp
type PublishResp struct {
	BaseDataInfo
	ArticleId int64           `json:"articleId"`
	Data      PublishRespData `json:"data"`
}

type PublishRespData struct {
	Data ArticleItem `json:"data"`
}

// swagger:model UserArticleListsReq
type UserArticleListsReq struct {
	// required : true
	UserId int64 `json:"userId" validate:"required"`
	// min : 0
	Cursor int64 `json:"cursor" validate:"gte=0"`
	// min : 0
	PageSize int64 `json:"pageSize,optional" validate:"gte=0"`
	SortType int32 `json:"sortType" validate:"oneof=0 1 2"`
	// min : 0
	ArticleId int64 `json:"articleId" validate:"gte=0"`
}

// swagger:model ArticleListsReq
type ArticleListsReq struct {
	Cursor    int64 `json:"cursor"`
	PageSize  int64 `json:"pageSize,optional"`
	SortType  int32 `json:"sortType"`
	ArticleId int64 `json:"articleId"` // 文章id
}

// swagger:model ArticleListsByGroupIdReq
type ArticleListsByGroupIdReq struct {
	Cursor    int64 `json:"cursor"`
	PageSize  int64 `json:"pageSize,optional"`
	SortType  int32 `json:"sortType"`
	ArticleId int64 `json:"articleId"` // 文章id
	GroupId   int64 `json:"groupId"`   // 圈子id
}

// swagger:model ArticleListsByTagIdReq
type ArticleListsByTagIdReq struct {
	Cursor    int64 `json:"cursor"`
	PageSize  int64 `json:"pageSize,optional"`
	SortType  int32 `json:"sortType"`
	ArticleId int64 `json:"articleId"` // 文章id
	TagId     int64 `json:"tagId"`     // 标签id
}

// swagger:model ArticleListsResp
type ArticleListsResp struct {
	BaseDataInfo
	Data ArticleListsRespData `json:"data"`
}

type ArticleListsRespData struct {
	IsEnd       bool          `json:"isEnd"`
	Cursor      int64         `json:"cursor"`
	ArticleId   int64         `json:"articleId"`
	Articles    []ArticleItem `json:"articles"`
	TopArticles []ArticleItem `json:"topArticles"`
}

// swagger:model ArticleDeleteReq
type ArticleDeleteReq struct {
	UserId int64 `json:"userId,optional"`
	// required : true
	ArticleId int64 `json:"articleId" validate:"required"`
}

// swagger:model ArticleDetailReq
type ArticleDetailReq struct {
	// required : true
	ArticleId int64 `json:"articleId" validate:"required"`
}

// swagger:model ArticleDetailResp
type ArticleDetailResp struct {
	BaseDataInfo
	Data ArticleItemData `json:"data"`
}

type ArticleItemData struct {
	Data ArticleItem `json:"data"`
}

type ArticleItem struct {
	Id           int64            `json:"id"`
	Title        string           `json:"title"`
	Cover        string           `json:"cover"`
	Videos       string           `json:"videos"`
	Content      string           `json:"content"`
	Description  string           `json:"description"`
	LikeCount    int64            `json:"likeCount"`
	LikeInfo     []LikeRecordItem `json:"likeInfo"`
	LikeType     int64            `json:"likeType"`
	IsLike       int64            `json:"isLike"`
	CommentCount int64            `json:"commentCount"`
	AuthorId     int64            `json:"authorId"`
	AuthorInfo   AuthorInfo       `json:"authorInfo"`
	CollectCount int64            `json:"collectCount"` // 收藏数
	ViewCount    int64            `json:"viewCount"`    // 浏览数
	ShareCount   int64            `json:"shareCount"`   // 分享数
	Type         int64            `json:"type"`
	TagIds       string           `json:"tagIds"` // 文章标签
	TagList      []TagInfo        `json:"tagsList"`
	GroupId      int64            `json:"groupId"` // 圈子ID
	GroupInfo    GroupInfo        `json:"groupInfo"`
	Status       int64            `json:"status"`    // 状态 0:待审核 1:审核不通过 2:可见
	TopStatus    int64            `json:"topStatus"` // 置顶状态 0:不置顶 1:置顶
	PublishTime  int64            `json:"publishTime"`
	CreateTime   int64            `json:"createTime"`
	UpdateTime   int64            `json:"updateTime"`
}

// swagger:model AuthorInfo
type AuthorInfo struct {
	Id               int64  `json:"id"`               // 用户id
	Nickname         string `json:"nickname"`         // 用户昵称
	NickName         string `json:"nickName"`         // 用户昵称
	Mobile           string `json:"mobile"`           // 手机
	Avatar           string `json:"avatar"`           // 头像
	Info             string `json:"info"`             // 个人介绍
	Sex              int64  `json:"sex"`              // 性别
	Roles            string `json:"roles"`            // 角色
	BackgroundImage  string `json:"backgroundImage"`  // 个人中心背景图片
	Password         string `json:"password"`         // 密码
	HomePage         string `json:"homePage"`         // 个人主页
	Description      string `json:"description"`      // 个人描述
	Score            int    `json:"score"`            // 积分
	DelState         int    `json:"delState"`         // 状态
	TopicCount       int    `json:"topicCount"`       // 帖子数量
	CommentCount     int    `json:"commentCount"`     // 跟帖数量
	FollowCount      int    `json:"followCount"`      // 关注数量
	FansCount        int    `json:"fansCount"`        // 粉丝数量
	ForbiddenEndTime int64  `json:"forbiddenEndTime"` // 禁言结束时间
	MutedEndTime     int64  `json:"mutedEndTime"`     // 禁言结束时间
	IsFollow         bool   `json:"isFollow"`         // 是否关注
}

// swagger:model ArticleDraftListsReq
type ArticleDraftListsReq struct {
	Cursor   int64 `json:"cursor"`
	PageSize int64 `json:"pageSize,optional"`
	SortType int32 `json:"sortType"`
	LastId   int64 `json:"lastId"`
	Type     int32 `json:"type,optional"`
}

// swagger:model ArticleDraftListsResp
type ArticleDraftListsResp struct {
	BaseDataInfo
	Data ArticleDraftListsRespData `json:"data"`
}

type ArticleDraftListsRespData struct {
	Cursor   int64         `json:"cursor"`
	PageSize int64         `json:"pageSize,optional"`
	IsEnd    bool          `json:"isEnd"`
	LastId   int64         `json:"lastId"`
	Data     []ArticleItem `json:"data"`
}

// swagger:model ArticlSaveDraftReq
type ArticlSaveDraftReq struct {
	Id          int64  `json:"id"`
	Type        int64  `json:"type"` // 文章类型
	Title       string `json:"title"`
	Content     string `json:"content"`
	Description string `json:"description"`
	Cover       string `json:"cover"`
	GroupId     int64  `json:"groupId"`
	TagIds      string `json:"tagIds"`
}

// swagger:model ArticlSaveDraftResp
type ArticlSaveDraftResp struct {
	BaseDataInfo
	Data ArticleItem `json:"data"`
}

// swagger:model ImageAuditedCallbackReq
type ImageAuditedCallbackReq struct {
}

// swagger:model VideoAuditedCallbackReq
type VideoAuditedCallbackReq struct {
	Code    int                      `json:"code"`
	Message string                   `json:"message"`
	Data    VideoAuditedCallbackData `json:"data"`
}

type VideoAuditedCallbackData struct {
	URL             string        `json:"url"`
	Result          int           `json:"result"`
	ForbiddenStatus int           `json:"forbidden_status"`
	TraceID         string        `json:"trace_id"`
	Event           string        `json:"event"`
	PornInfo        PornInfo      `json:"porn_info"`
	TerroristInfo   TerroristInfo `json:"terrorist_info"`
	PoliticsInfo    PoliticsInfo  `json:"politics_info"`
	AdsInfo         AdsInfo       `json:"ads_info"`
	DataID          string        `json:"data_id"`
}

// swagger:model PornInfo
type PornInfo struct {
	HitFlag int    `json:"hit_flag"`
	Score   int    `json:"score"`
	Label   string `json:"label"`
	Count   int    `json:"count"`
}

// swagger:model TerroristInfo
type TerroristInfo struct {
	HitFlag int    `json:"hit_flag"`
	Score   int    `json:"score"`
	Label   string `json:"label"`
	Count   int    `json:"count"`
}

// swagger:model PoliticsInfo
type PoliticsInfo struct {
	HitFlag int    `json:"hit_flag"`
	Score   int    `json:"score"`
	Label   string `json:"label"`
	Count   int    `json:"count"`
}

// swagger:model AdsInfo
type AdsInfo struct {
	HitFlag int    `json:"hit_flag"`
	Score   int    `json:"score"`
	Label   string `json:"label"`
	Count   int    `json:"count"`
}

// swagger:model AuditedCallbackResp
type AuditedCallbackResp struct {
	Msg string `json:"msg"`
}

// 管理员删除文章
// swagger:model AdminArticleDeleteReq
type AdminArticleDeleteReq struct {
	// required : true
	ArticleId uint64 `json:"articleId" validate:"required"`
}

// 管理员删除评论
// swagger:model AdminDelReplyReq
type AdminDelReplyReq struct {
	// required : true
	ReplyId uint64 `json:"replyId" validate:"required"`
}

// 管理员封禁用户
// swagger:model AdminBanUserReq
type AdminBanUserReq struct {
	// required : true
	UserId uint64 `json:"userId" validate:"required"`
	// required : true
	Time int64 `json:"time" validate:"required"`
}

// 管理员解封用户
// swagger:model AdminUnbanUserReq
type AdminUnbanUserReq struct {
	// required : true
	UserId uint64 `json:"userId" validate:"required"`
}

// 管理员禁言用户
// swagger:model AdminMuteUserReq
type AdminMuteUserReq struct {
	// required : true
	UserId uint64 `json:"userId" validate:"required"`
	// required : true
	Time int64 `json:"time" validate:"required"`
}

// 管理员解禁用户
// swagger:model AdminUnmuteUserReq
type AdminUnmuteUserReq struct {
	// required : true
	UserId uint64 `json:"userId" validate:"required"`
}

// swagger:model ReplyReq
type ReplyReq struct {
	BizId         string `json:"bizId"`
	TargetId      int64  `json:"targetId"`
	ReplyUserId   int64  `json:"replyUserId"`
	BeReplyUserId int64  `json:"beReplyUserId"`
	ParentId      int64  `json:"parentId"`
	Content       string `json:"content"`
}

// swagger:model ReplyResp
type ReplyResp struct {
	BaseDataInfo
	Data ReplyRespData `json:"data"`
}

type ReplyRespData struct {
	Item ReplyItem `json:"item"`
}

type ReplyItem struct {
	Id               int64            `json:"id"`               // 主键ID
	BizId            string           `json:"bizId"`            // 业务ID
	TargetId         int64            `json:"targetId"`         // 评论目标id
	ReplyArticleInfo ReplyArticleInfo `json:"replyArticleInfo"` // 评论文章信息
	ReplyUserId      int64            `json:"replyUserId"`      // 评论用户ID
	ReplyUserInfo    ReplyUserInfo    `json:"replyUserInfo"`    // 评论用户信息
	BeReplyUserId    int64            `json:"beReplyUserId"`    // 被回复用户ID
	ParentId         int64            `json:"parentId"`         // 父评论ID
	Content          string           `json:"content"`          // 内容
	Status           int64            `json:"status"`           // 状态 0:正常 1:删除
	LikeNum          int64            `json:"likeNum"`          // 点赞数
	IsLike           int64            `json:"isLike"`
	CreateTime       int64            `json:"createTime"` // 创建时间
	UpdateTime       int64            `json:"updateTime"` // 最后修改时间
}

// swagger:model ReplyUserInfo
type ReplyUserInfo struct {
	Id              int64  `json:"id"`              // 用户id
	UserId          int64  `json:"userId"`          // 用户id
	Nickname        string `json:"nickname"`        // 用户昵称
	Mobile          string `json:"mobile"`          // 手机
	Avatar          string `json:"avatar"`          // 头像
	Info            string `json:"info"`            // 个人介绍
	Sex             int64  `json:"sex"`             // 性别
	BackgroundImage string `json:"backgroundImage"` // 个人中心背景图片
	HomePage        string `json:"homePage"`        // 个人主页
	Description     string `json:"description"`     // 个人描述
}

// swagger:model UserReplyListReq
type UserReplyListReq struct {
	UserId   int64 `json:"userId"`
	Cursor   int64 `json:"cursor"`   // 游标
	PageSize int64 `json:"pageSize"` // 游标
	SortType int64 `json:"sortType"`
}

// swagger:model ReplyListReq
type ReplyListReq struct {
	BizId    string `json:"bizId"`    // 业务ID
	TargetId int64  `json:"targetId"` // 评论目标id
	Cursor   int64  `json:"cursor"`   // 游标
	PageSize int64  `json:"pageSize"` // 游标
	SortType int64  `json:"sortType"`
}

// swagger:model ReplyListResp
type ReplyListResp struct {
	BaseDataInfo
	Data ReplyListRespData `json:"data"`
}

type ReplyListRespData struct {
	Items  []ReplyItem `json:"items"`
	Cursor int64       `json:"cursor"` // 游标
	IsEnd  bool        `json:"isEnd"`  // 是否结束
	Id     int64       `json:"id"`     // 查询每页最后一个用户id
}

// swagger:model DelReplyReq
type DelReplyReq struct {
	UserId  int64 `json:"userId"`
	ReplyId int64 `json:"replyId"`
}

// swagger:model DelReplyResp
type DelReplyResp struct {
	BaseDataInfo
	Data DelReplyRespData `json:"data"`
}

type DelReplyRespData struct {
	ReplyId int64 `json:"replyId"`
	Status  int64 `json:"status"`
}

// swagger:model ReplyArticleInfo
type ReplyArticleInfo struct {
	Id    int64  `json:"id"`    // 主键ID
	Title string `json:"title"` // 文章标题
	Cover string `json:"cover"` // 封面图
}

type ViewRecordItem struct {
	Id           int64        `json:"id"`           // 主键ID
	BizId        string       `json:"bizId"`        // 业务ID
	ObjId        int64        `json:"objId"`        // 评论目标id
	UserId       int64        `json:"userId"`       // 评论用户ID
	ViewUserInfo ViewUserInfo `json:"viewUserInfo"` // 评论用户信息
	CreateTime   int64        `json:"createTime"`   // 创建时间
	UpdateTime   int64        `json:"updateTime"`   // 最后修改时间
}

// swagger:model ViewUserInfo
type ViewUserInfo struct {
	Id              int64  `json:"id"`              // 用户id
	Nickname        string `json:"nickname"`        // 用户昵称
	Mobile          string `json:"mobile"`          // 手机
	Avatar          string `json:"avatar"`          // 头像
	Info            string `json:"info"`            // 个人介绍
	Sex             int64  `json:"sex"`             // 性别
	BackgroundImage string `json:"backgroundImage"` // 个人中心背景图片
	HomePage        string `json:"homePage"`        // 个人主页
	Description     string `json:"description"`     // 个人描述
}

// swagger:model ViewListReq
type ViewListReq struct {
	BizId    string `json:"bizId"`    // 业务ID
	ObjId    int64  `json:"objId"`    // 评论目标id
	Cursor   int64  `json:"cursor"`   // 游标
	PageSize int64  `json:"pageSize"` // 游标
	SortType int64  `json:"sortType"`
}

// swagger:model ViewListResp
type ViewListResp struct {
	BaseDataInfo
	Data ViewListRespData `json:"data"`
}

type ViewListRespData struct {
	Items  []ViewRecordItem `json:"items"`
	Cursor int64            `json:"cursor"` // 游标
	IsEnd  bool             `json:"isEnd"`  // 是否结束
}

// swagger:model AddArticleViewRecordReq
type AddArticleViewRecordReq struct {
	BizId string `json:"bizId"` // 业务id
	ObjId int64  `json:"objId"` // 点赞对象id
}

// swagger:model AddArticleViewRecordResp
type AddArticleViewRecordResp struct {
	BaseDataInfo
	Data AddArticleViewRecordRespData `json:"data"`
}

type AddArticleViewRecordRespData struct {
	BizId   string `json:"bizId"`   // 业务id
	ObjId   int64  `json:"objId"`   // 点赞对象id
	ViewNum int64  `json:"viewNum"` // 点赞数
}

// The response data of group information | Group信息
// swagger:model GroupInfo
type GroupInfo struct {
	Id *uint64 `json:"id,optional"`
	// 圈子名称
	Name *string `json:"name,optional"`
	// 圈子描述
	Desc *string `json:"desc,optional"`
	// 圈子图标
	IconUrl *string `json:"iconUrl,optional"`
	// 圈子背景图
	CoverUrl *string `json:"coverUrl,optional"`
	// 用户ID
	UserId *uint64 `json:"userId,optional"`
	// 主题数
	TopicNum *int64 `json:"topicNum,optional"`
	// 关注数
	FollowNum *int64 `json:"followNum,optional"`
	IsFollow  *bool  `json:"isFollow,optional"`
	IsMember  *bool  `json:"isMember,optional"`
	MemberNum *int64 `json:"memberNum,optional"`
	// 创建时间
	CreateTime *int64 `json:"createTime,optional"`
	// 最后修改时间
	UpdateTime *int64 `json:"updateTime,optional"`
	// tags
	Tags []TagInfo `json:"tags,optional"`
}

// The response data of group list | Group列表数据
// swagger:model GroupListResp
type GroupListResp struct {
	BaseDataInfo
	// Group list data | Group列表数据
	Data GroupListInfo `json:"data"`
}

// Group list data | Group列表数据
// swagger:model GroupListInfo
type GroupListInfo struct {
	BaseListInfo
	// The API list data | Group列表数据
	Data []GroupInfo `json:"data"`
}

// Get group list request params | Group列表请求参数
// swagger:model GroupListReq
type GroupListReq struct {
	PageInfo
	// Name
	Name *string `json:"name,optional"`
	// 用户ID
	UserId *uint64 `json:"userId,optional"`
}

// Group information response | Group信息返回体
// swagger:model GroupInfoResp
type GroupInfoResp struct {
	BaseDataInfo
	// Group information | Group数据
	Data GroupInfo `json:"data"`
}

// The response data of tag information | Tag信息
// swagger:model TagInfo
type TagInfo struct {
	Id *uint64 `json:"id,optional"`
	// 标签名称
	Name *string `json:"name,optional"`
	// 标签描述
	Desc *string `json:"desc,optional"`
	// 话题类型（1、普通 2、官方）
	Type *int32 `json:"type,optional"`
	// 圈子图标
	IconUrl *string `json:"iconUrl,optional"`
	// 圈子背景图
	CoverUrl *string `json:"coverUrl,optional"`
	// 主题数
	TopicNum *int64 `json:"topicNum,optional"`
	// 关注数
	FollowNum *int64 `json:"followNum,optional"`
	// 参与数
	MemberNum *int64 `json:"memberNum,optional"`
	// 创建时间
	CreateTime *int64 `json:"createTime,optional"`
	// 最后修改时间
	UpdateTime *int64 `json:"updateTime,optional"`
	IsFollow   *bool  `json:"isFollow,optional"`
	IsMember   *bool  `json:"isMember,optional"`
}

// The response data of tag list | Tag列表数据
// swagger:model TagListResp
type TagListResp struct {
	BaseDataInfo
	// Tag list data | Tag列表数据
	Data TagListInfo `json:"data"`
}

// Tag list data | Tag列表数据
// swagger:model TagListInfo
type TagListInfo struct {
	BaseListInfo
	// tagName是否存
	Exist *bool `json:"exist,optional"`
	// The API list data | Tag列表数据
	Data []TagInfo `json:"data"`
}

// Get tag list request params | Tag列表请求参数
// swagger:model TagListReq
type TagListReq struct {
	PageInfo
	// Name
	Name *string `json:"name,optional"`
	// Desc
	Desc *string `json:"desc,optional"`
	// IconUrl
	IconUrl *string `json:"iconUrl,optional"`
}

// Tag information response | Tag信息返回体
// swagger:model TagInfoResp
type TagInfoResp struct {
	BaseDataInfo
	// Tag information | Tag数据
	Data TagInfo `json:"data"`
}

// The response data of group member information | GroupMember信息
// swagger:model GroupMemberInfo
type GroupMemberInfo struct {
	Id *uint64 `json:"id,optional"`
	// 用户ID
	UserId *int64 `json:"userId,optional"`
	// 圈子ID
	GroupId *int64 `json:"groupId,optional"`
	// 状态（0、退出 1、加入）
	Status *uint32 `json:"status,optional"`
	// 创建时间
	CreateTime *int64 `json:"createTime,optional"`
	// 最后修改时间
	UpdateTime *int64     `json:"updateTime,optional"`
	AuthorInfo AuthorInfo `json:"authorInfo,optional"`
}

// The response data of group member list | GroupMember列表数据
// swagger:model GroupMemberListResp
type GroupMemberListResp struct {
	BaseDataInfo
	// GroupMember list data | GroupMember列表数据
	Data GroupMemberListInfo `json:"data"`
}

// GroupMember list data | GroupMember列表数据
// swagger:model GroupMemberListInfo
type GroupMemberListInfo struct {
	BaseListInfo
	// The API list data | GroupMember列表数据
	Data []GroupMemberInfo `json:"data"`
}

// Get group member list request params | GroupMember列表请求参数
// swagger:model GroupMemberListReq
type GroupMemberListReq struct {
	GroupId *uint64 `json:"groupId,optional"`
	PageInfo
}

// GroupMember information response | GroupMember信息返回体
// swagger:model GroupMemberInfoResp
type GroupMemberInfoResp struct {
	BaseDataInfo
	// GroupMember information | GroupMember数据
	Data GroupMemberInfo `json:"data"`
}

// The response data of group follow information | GroupFollow信息
// swagger:model GroupFollowInfo
type GroupFollowInfo struct {
	Id *uint64 `json:"id,optional"`
	// 用户ID
	UserId *uint64 `json:"userId,optional"`
	// 圈子ID
	GroupId *uint64 `json:"groupId,optional"`
	// 状态（0、退出 1、加入）
	Status *uint32 `json:"status,optional"`
	// 创建时间
	CreateTime *int64 `json:"createTime,optional"`
	// 最后修改时间
	UpdateTime *int64 `json:"updateTime,optional"`
}

// The response data of group follow list | GroupFollow列表数据
// swagger:model GroupFollowListResp
type GroupFollowListResp struct {
	BaseDataInfo
	// GroupFollow list data | GroupFollow列表数据
	Data GroupFollowListInfo `json:"data"`
}

// GroupFollow list data | GroupFollow列表数据
// swagger:model GroupFollowListInfo
type GroupFollowListInfo struct {
	BaseListInfo
	// The API list data | GroupFollow列表数据
	Data []GroupFollowInfo `json:"data"`
}

// Get group follow list request params | GroupFollow列表请求参数
// swagger:model GroupFollowListReq
type GroupFollowListReq struct {
	PageInfo
}

// GroupFollow information response | GroupFollow信息返回体
// swagger:model GroupFollowInfoResp
type GroupFollowInfoResp struct {
	BaseDataInfo
	// GroupFollow information | GroupFollow数据
	Data GroupFollowInfo `json:"data"`
}

// The response data of banner information | Banner信息
// swagger:model BannerInfo
type BannerInfo struct {
	Id *uint64 `json:"id,optional"`
	// 标题
	Title *string `json:"title,optional"`
	// 副标题
	SubTitle *string `json:"subTitle,optional"`
	// 轮播图地址
	CoverUrl *string `json:"coverUrl,optional"`
	// 跳转链接
	JumpLink *string `json:"jumpLink,optional"`
	// 所属模块（1、社区模块 2、服务模块）
	Module *int32 `json:"module,optional"`
	// 类型（1、文章 2、商品 3、外链）
	Type *int32 `json:"type,optional"`
	// 创建时间
	CreateTime *int64 `json:"createTime,optional"`
	// 更新时间
	UpdateTime *int64 `json:"updateTime,optional"`
	// 底色类型
	BaseColor *int64 `json:"baseColor,optional"`
	// 轮播图间隔时间
	Interval *int64 `json:"interval,optional"`
	// 轮播图持续时间
	Duration *int64 `json:"duration,optional"`
	// 排序
	Sort *int32 `json:"sort,optional"`
}

// The response data of banner list | Banner列表数据
// swagger:model BannerListResp
type BannerListResp struct {
	BaseDataInfo
	// Banner list data | Banner列表数据
	Data BannerListInfo `json:"data"`
}

// Banner list data | Banner列表数据
// swagger:model BannerListInfo
type BannerListInfo struct {
	BaseListInfo
	// The API list data | Banner列表数据
	Data []BannerInfo `json:"data"`
}

// Get banner list request params | Banner列表请求参数
// swagger:model BannerListReq
type BannerListReq struct {
	PageInfo
	// Title
	Title *string `json:"title,optional"`
	// SubTitle
	SubTitle *string `json:"subTitle,optional"`
	// CoverUrl
	CoverUrl *string `json:"coverUrl,optional"`
	// 所属模块（1、社区模块 2、服务模块）
	Module *int32 `json:"module,optional"`
}

// Banner information response | Banner信息返回体
// swagger:model BannerInfoResp
type BannerInfoResp struct {
	BaseDataInfo
	// Banner information | Banner数据
	Data BannerInfo `json:"data"`
}

// The response data of city information | City信息
// swagger:model CityInfo
type CityInfo struct {
	Id *uint64 `json:"id,optional"`
	// Code
	Code *string `json:"code,optional"`
	// Name
	Name *string `json:"name,optional"`
	// ProvinceCode
	ProvinceCode *string `json:"provinceCode,optional"`
	// Level
	Level *int32 `json:"level,optional"`
	// Geocode
	Geocode *string `json:"geocode,optional"`
	// Latitude
	Latitude *string `json:"latitude,optional"`
	// Longitude
	Longitude *string `json:"longitude,optional"`
	// Sort
	Sort *int32 `json:"sort,optional"`
	// ParentCode
	ParentCode *string `json:"parentCode,optional"`
}

// The response data of city list | City列表数据
// swagger:model CityListResp
type CityListResp struct {
	BaseDataInfo
	// City list data | City列表数据
	Data CityListInfo `json:"data"`
}

// City list data | City列表数据
// swagger:model CityListInfo
type CityListInfo struct {
	BaseListInfo
	// The API list data | City列表数据
	Data []CityInfo `json:"data"`
}

// Get city list request params | City列表请求参数
// swagger:model CityListReq
type CityListReq struct {
	PageInfo
	// Code
	Code *string `json:"code,optional"`
	// Name
	Name *string `json:"name,optional"`
	// ProvinceCode
	ProvinceCode *string `json:"provinceCode,optional"`
	// Geocode
	Geocode *string `json:"geocode,optional"`
	// Latitude
	Latitude *string `json:"latitude,optional"`
	// Longitude
	Longitude *string `json:"longitude,optional"`
	// ParentCode
	ParentCode *string `json:"parentCode,optional"`
}

// City information response | City信息返回体
// swagger:model CityInfoResp
type CityInfoResp struct {
	BaseDataInfo
	// City information | City数据
	Data CityInfo `json:"data"`
}

// The response data of street information | Street信息
// swagger:model StreetInfo
type StreetInfo struct {
	Id *uint64 `json:"id,optional"`
	// Code
	Code *string `json:"code,optional"`
	// Name
	Name *string `json:"name,optional"`
	// AreasCode
	AreasCode *string `json:"areasCode,optional"`
	// ProvinceCode
	ProvinceCode *string `json:"provinceCode,optional"`
	// Level
	Level *int32 `json:"level,optional"`
	// Geocode
	Geocode *string `json:"geocode,optional"`
	// Latitude
	Latitude *string `json:"latitude,optional"`
	// Sort
	Sort *int32 `json:"sort,optional"`
	// Longitude
	Longitude *string `json:"longitude,optional"`
	// ParentCode
	ParentCode *string `json:"parentCode,optional"`
}

// The response data of street list | Street列表数据
// swagger:model StreetListResp
type StreetListResp struct {
	BaseDataInfo
	// Street list data | Street列表数据
	Data StreetListInfo `json:"data"`
}

// Street list data | Street列表数据
// swagger:model StreetListInfo
type StreetListInfo struct {
	BaseListInfo
	// The API list data | Street列表数据
	Data []StreetInfo `json:"data"`
}

// Get street list request params | Street列表请求参数
// swagger:model StreetListReq
type StreetListReq struct {
	PageInfo
	// Code
	Code *string `json:"code,optional"`
	// Name
	Name *string `json:"name,optional"`
	// AreasCode
	AreasCode *string `json:"areasCode,optional"`
	// ProvinceCode
	ProvinceCode *string `json:"provinceCode,optional"`
	// Geocode
	Geocode *string `json:"geocode,optional"`
	// Latitude
	Latitude *string `json:"latitude,optional"`
	// Longitude
	Longitude *string `json:"longitude,optional"`
	// ParentCode
	ParentCode *string `json:"parentCode,optional"`
}

// Street information response | Street信息返回体
// swagger:model StreetInfoResp
type StreetInfoResp struct {
	BaseDataInfo
	// Street information | Street数据
	Data StreetInfo `json:"data"`
}

// The response data of area information | Area信息
// swagger:model AreaInfo
type AreaInfo struct {
	Id *uint64 `json:"id,optional"`
	// Code
	Code *string `json:"code,optional"`
	// Name
	Name *string `json:"name,optional"`
	// CityCode
	CityCode *string `json:"cityCode,optional"`
	// ProvinceCode
	ProvinceCode *string `json:"provinceCode,optional"`
	// Level
	Level *int32 `json:"level,optional"`
	// Geocode
	Geocode *string `json:"geocode,optional"`
	// Latitude
	Latitude *string `json:"latitude,optional"`
	// Sort
	Sort *int32 `json:"sort,optional"`
	// Longitude
	Longitude *string `json:"longitude,optional"`
	// ParentCode
	ParentCode *string `json:"parentCode,optional"`
}

// The response data of area list | Area列表数据
// swagger:model AreaListResp
type AreaListResp struct {
	BaseDataInfo
	// Area list data | Area列表数据
	Data AreaListInfo `json:"data"`
}

// Area list data | Area列表数据
// swagger:model AreaListInfo
type AreaListInfo struct {
	BaseListInfo
	// The API list data | Area列表数据
	Data []AreaInfo `json:"data"`
}

// Get area list request params | Area列表请求参数
// swagger:model AreaListReq
type AreaListReq struct {
	PageInfo
	// Code
	Code *string `json:"code,optional"`
	// Name
	Name *string `json:"name,optional"`
	// CityCode
	CityCode *string `json:"cityCode,optional"`
	// ProvinceCode
	ProvinceCode *string `json:"provinceCode,optional"`
	// Geocode
	Geocode *string `json:"geocode,optional"`
	// Latitude
	Latitude *string `json:"latitude,optional"`
	// Longitude
	Longitude *string `json:"longitude,optional"`
	// ParentCode
	ParentCode *string `json:"parentCode,optional"`
}

// Area information response | Area信息返回体
// swagger:model AreaInfoResp
type AreaInfoResp struct {
	BaseDataInfo
	// Area information | Area数据
	Data AreaInfo `json:"data"`
}

// The response data of province information | Province信息
// swagger:model ProvinceInfo
type ProvinceInfo struct {
	Id *uint64 `json:"id,optional"`
	// Code
	Code *string `json:"code,optional"`
	// Name
	Name *string `json:"name,optional"`
	// Level
	Level *int32 `json:"level,optional"`
	// Geocode
	Geocode *string `json:"geocode,optional"`
	// Latitude
	Latitude *string `json:"latitude,optional"`
	// Longitude
	Longitude *string `json:"longitude,optional"`
	// Sort
	Sort *int32 `json:"sort,optional"`
	// ParentCode
	ParentCode *string `json:"parentCode,optional"`
}

// The response data of province list | Province列表数据
// swagger:model ProvinceListResp
type ProvinceListResp struct {
	BaseDataInfo
	// Province list data | Province列表数据
	Data ProvinceListInfo `json:"data"`
}

// Province list data | Province列表数据
// swagger:model ProvinceListInfo
type ProvinceListInfo struct {
	BaseListInfo
	// The API list data | Province列表数据
	Data []ProvinceInfo `json:"data"`
}

// Get province list request params | Province列表请求参数
// swagger:model ProvinceListReq
type ProvinceListReq struct {
	PageInfo
	// Code
	Code *string `json:"code,optional"`
	// Name
	Name *string `json:"name,optional"`
	// Geocode
	Geocode *string `json:"geocode,optional"`
	// Latitude
	Latitude *string `json:"latitude,optional"`
	// Longitude
	Longitude *string `json:"longitude,optional"`
	// ParentCode
	ParentCode *string `json:"parentCode,optional"`
}

// Province information response | Province信息返回体
// swagger:model ProvinceInfoResp
type ProvinceInfoResp struct {
	BaseDataInfo
	// Province information | Province数据
	Data ProvinceInfo `json:"data"`
}

// The response data of mainten info information | MaintenInfo信息
// swagger:model MaintenInfoInfo
type MaintenInfoInfo struct {
	Id *uint64 `json:"id,optional"`
	// CreateTime
	CreateTime *int64 `json:"createTime,optional"`
	// UpdateTime
	UpdateTime *int64 `json:"updateTime,optional"`
	// DeleteTime
	DeleteTime *int64 `json:"deleteTime,optional"`
	// 维修类型
	MalfuncType *int32 `json:"malfuncType,optional"`
	// 故障类型
	FaultType *int32 `json:"faultType,optional"`
	// 取件地址
	PickupAddress *string `json:"pickupAddress,optional"`
	// 回寄地址
	ReturnAddress *string `json:"returnAddress,optional"`
	// 故障描述
	MalfuncRemark *string `json:"malfuncRemark,optional"`
	// 类型 0:快递 1:邮寄
	ExpressType *int32 `json:"expressType,optional"`
	// 联系电话
	Mobile *string `json:"mobile,optional"`
	// sn号
	Sn *string `json:"sn,optional"`
	// 维修订单号
	OrderNo *string `json:"orderNo,optional"`
	// 下单用户ID
	UserId *uint64 `json:"userId,optional"`
	// 关联产品ID
	ProductId *uint64 `json:"productId,optional"`
}

// The response data of mainten info list | MaintenInfo列表数据
// swagger:model MaintenInfoListResp
type MaintenInfoListResp struct {
	BaseDataInfo
	// MaintenInfo list data | MaintenInfo列表数据
	Data MaintenInfoListInfo `json:"data"`
}

// MaintenInfo list data | MaintenInfo列表数据
// swagger:model MaintenInfoListInfo
type MaintenInfoListInfo struct {
	BaseListInfo
	// The API list data | MaintenInfo列表数据
	Data []MaintenInfoInfo `json:"data"`
}

// Get mainten info list request params | MaintenInfo列表请求参数
// swagger:model MaintenInfoListReq
type MaintenInfoListReq struct {
	PageInfo
	// PickupAddress
	PickupAddress *string `json:"pickupAddress,optional"`
	// ReturnAddress
	ReturnAddress *string `json:"returnAddress,optional"`
	// MalfuncRemark
	MalfuncRemark *string `json:"malfuncRemark,optional"`
}

// MaintenInfo information response | MaintenInfo信息返回体
// swagger:model MaintenInfoInfoResp
type MaintenInfoInfoResp struct {
	BaseDataInfo
	// MaintenInfo information | MaintenInfo数据
	Data MaintenInfoInfo `json:"data"`
}

// The response data of segment information | Segment信息
// swagger:model SegmentInfo
type SegmentInfo struct {
	Id *uint64 `json:"id,optional"`
	// 栏目名称
	Name *string `json:"name,optional"`
	// 排序
	Sequence *int32 `json:"sequence,optional"`
	// 创建时间
	CreateTime *int64 `json:"createTime,optional"`
	// 更新时间
	UpdateTime *int64 `json:"updateTime,optional"`
}

// The response data of segment list | Segment列表数据
// swagger:model SegmentListResp
type SegmentListResp struct {
	BaseDataInfo
	// Segment list data | Segment列表数据
	Data SegmentListInfo `json:"data"`
}

// Segment list data | Segment列表数据
// swagger:model SegmentListInfo
type SegmentListInfo struct {
	BaseListInfo
	// The API list data | Segment列表数据
	Data []SegmentInfo `json:"data"`
}

// Get segment list request params | Segment列表请求参数
// swagger:model SegmentListReq
type SegmentListReq struct {
	PageInfo
	// Name
	Name *string `json:"name,optional"`
}

// Segment information response | Segment信息返回体
// swagger:model SegmentInfoResp
type SegmentInfoResp struct {
	BaseDataInfo
	// Segment information | Segment数据
	Data SegmentInfo `json:"data"`
}

// The response data of article information | Article信息
// swagger:model ArticleInfo
type ArticleInfo struct {
	Id *uint64 `json:"id,optional"`
	// 标题
	Title *string `json:"title,optional"`
	// 内容
	Content *string `json:"content,optional"`
	// 封面
	Cover *string `json:"cover,optional"`
	// 描述
	Description *string `json:"description,optional"`
	// 作者ID
	AuthorId *uint64 `json:"authorId,optional"`
	// 作者信息
	AuthorInfo *AuthorInfo `json:"authorInfo,optional"`
	// 标签ID
	TagIds *string `json:"tagIds,optional"`
	// 圈子ID
	GroupId *int64 `json:"groupId,optional"`
	// 状态 0:待审核 1:审核不通过 2:可见 3:用户删除 4:草稿
	Status *uint32 `json:"status,optional"`
	// 类型 1、文章 2、动态
	Type *int32 `json:"type,optional"`
	// 置顶状态
	TopStatus *int32 `json:"topStatus,optional"`
	// 评论数
	CommentNum *int64 `json:"commentNum,optional"`
	// 点赞数
	LikeNum *int64 `json:"likeNum,optional"`
	// 收藏数
	CollectNum *int64 `json:"collectNum,optional"`
	// 浏览数
	ViewNum *int64 `json:"viewNum,optional"`
	// 分享数
	ShareNum *int64 `json:"shareNum,optional"`
	// 发布时间
	PublishTime *int64 `json:"publishTime,optional"`
	// 创建时间
	CreateTime *int64 `json:"createTime,optional"`
	// 最后修改时间
	UpdateTime *int64 `json:"updateTime,optional"`
}

// The response data of article list | Article列表数据
// swagger:model ArticleListResp
type ArticleListResp struct {
	BaseDataInfo
	// Article list data | Article列表数据
	Data ArticleListInfo `json:"data"`
}

// Article list data | Article列表数据
// swagger:model ArticleListInfo
type ArticleListInfo struct {
	BaseListInfo
	// The API list data | Article列表数据
	Data []ArticleInfo `json:"data"`
}

// Get article list request params | Article列表请求参数
// swagger:model ArticleListReq
type ArticleListReq struct {
	PageInfo
	// Title
	Title *string `json:"title,optional"`
	// Content
	Content *string `json:"content,optional"`
	// Cover
	Cover *string `json:"cover,optional"`
}

// Article information response | Article信息返回体
// swagger:model ArticleInfoResp
type ArticleInfoResp struct {
	BaseDataInfo
	// Article information | Article数据
	Data ArticleInfo `json:"data"`
}

// The response data of user information | User信息
// swagger:model UserInfo
type UserInfo struct {
	Id *uint64 `json:"id,optional"`
	// CreateTime
	CreateTime *int64 `json:"createTime,optional"`
	// UpdateTime
	UpdateTime *int64 `json:"updateTime,optional"`
	// DeleteTime
	DeleteTime *int64 `json:"deleteTime,optional"`
	// 禁言结束时间
	ForbiddenEndTime *int64 `json:"forbiddenEndTime,optional"`
	// DelState
	DelState *int32 `json:"delState,optional"`
	// 版本号
	Version *int64 `json:"version,optional"`
	// Mobile
	Mobile *string `json:"mobile,optional"`
	// Password
	Password *string `json:"password,optional"`
	// Nickname
	Nickname *string `json:"nickname,optional"`
	// 性别 0:男 1:女
	Sex *int32 `json:"sex,optional"`
	// Avatar
	Avatar *string `json:"avatar,optional"`
	// Info
	Info *string `json:"info,optional"`
	// BackgroundImage
	BackgroundImage *string `json:"backgroundImage,optional"`
	// HomePage
	HomePage *string `json:"homePage,optional"`
	// Description
	Description *string `json:"description,optional"`
	// Score
	Score *int64 `json:"score,optional"`
	// TopicCount
	TopicCount *int64 `json:"topicCount,optional"`
	// CommentCount
	CommentCount *int64 `json:"commentCount,optional"`
	// FollowCount
	FollowCount *int64 `json:"followCount,optional"`
	// FansCount
	FansCount *int64 `json:"fansCount,optional"`
	// LikeCount
	LikeCount *int64 `json:"likeCount,optional"`
	// Roles
	Roles *string `json:"roles,optional"`
}

// The response data of user list | User列表数据
// swagger:model UserListResp
type UserListResp struct {
	BaseDataInfo
	// User list data | User列表数据
	Data UserListInfo `json:"data"`
}

// User list data | User列表数据
// swagger:model UserListInfo
type UserListInfo struct {
	BaseListInfo
	// The API list data | User列表数据
	Data []UserInfo `json:"data"`
}

// Get user list request params | User列表请求参数
// swagger:model UserListReq
type UserListReq struct {
	PageInfo
	// Consignee
	Consignee *string `json:"consignee,optional"`
	// Mobile
	Mobile *string `json:"mobile,optional"`
	// Address
	Address *string `json:"address,optional"`
	// Password
	Password *string `json:"password,optional"`
	// Nickname
	Nickname *string `json:"nickname,optional"`
}

// User information response | User信息返回体
// swagger:model UserInfoResp
type UserInfoResp struct {
	BaseDataInfo
	// User information | User数据
	Data UserInfo `json:"data"`
}

// The response data of segment article record information | SegmentArticleRecord信息
// swagger:model SegmentArticleRecordInfo
type SegmentArticleRecordInfo struct {
	Id *uint64 `json:"id,optional"`
	// 栏目ID
	SegmentId *uint64 `json:"segmentId,optional"`
	// 文章ID
	ArticleId *uint64 `json:"articleId,optional"`
	// 文章内容
	ArticleInfo *ArticleInfo `json:"article,optional"`
	// 类型
	Type *int32 `json:"type,optional"`
	// 创建时间
	CreateTime *int64 `json:"createTime,optional"`
	// 更新时间
	UpdateTime *int64 `json:"updateTime,optional"`
}

// The response data of segment article record list | SegmentArticleRecord列表数据
// swagger:model SegmentArticleRecordListResp
type SegmentArticleRecordListResp struct {
	BaseDataInfo
	// SegmentArticleRecord list data | SegmentArticleRecord列表数据
	Data SegmentArticleRecordListInfo `json:"data"`
}

// SegmentArticleRecord list data | SegmentArticleRecord列表数据
// swagger:model SegmentArticleRecordListInfo
type SegmentArticleRecordListInfo struct {
	BaseListInfo
	// The API list data | SegmentArticleRecord列表数据
	Data []SegmentArticleRecordInfo `json:"data"`
}

// Get segment article record list request params | SegmentArticleRecord列表请求参数
// swagger:model SegmentArticleRecordListReq
type SegmentArticleRecordListReq struct {
	PageInfo
	// SegmentId
	SegmentId *uint64 `json:"segmentId,optional"`
	// Type
	Type *int32 `json:"type,optional"`
}

// SegmentArticleRecord information response | SegmentArticleRecord信息返回体
// swagger:model SegmentArticleRecordInfoResp
type SegmentArticleRecordInfoResp struct {
	BaseDataInfo
	// SegmentArticleRecord information | SegmentArticleRecord数据
	Data SegmentArticleRecordInfo `json:"data"`
}

// The response data of user addres information | UserAddres信息
// swagger:model UserAddresInfo
type UserAddresInfo struct {
	BaseIDInfo
	// 用户id
	UserId *int64 `json:"userId,optional"`
	// 收货人
	Consignee *string `json:"consignee,optional"`
	// 手机号码
	Mobile *string `json:"mobile,optional"`
	// 收货地址
	Address *string `json:"address,optional"`
	// 是否默认
	Default *int32 `json:"default,optional"`
	// 省
	Province *string `json:"province,optional"`
	// 市
	City *string `json:"city,optional"`
	// 县/区
	Area *string `json:"area,optional"`
	// 街道
	Street *string `json:"street,optional"`
	// 性别
	Gender *int32 `json:"gender,optional"`
	// 详细地址
	RecordAddress *string `json:"recordAddress,optional"`
}

// The response data of user addres list | UserAddres列表数据
// swagger:model UserAddresListResp
type UserAddresListResp struct {
	BaseDataInfo
	// UserAddres list data | UserAddres列表数据
	Data UserAddresListInfo `json:"data"`
}

// UserAddres list data | UserAddres列表数据
// swagger:model UserAddresListInfo
type UserAddresListInfo struct {
	BaseListInfo
	// The API list data | UserAddres列表数据
	Data []UserAddresInfo `json:"data"`
}

// Get user addres list request params | UserAddres列表请求参数
// swagger:model UserAddresListReq
type UserAddresListReq struct {
	PageInfo
	// Consignee
	Consignee *string `json:"consignee,optional"`
	// Mobile
	Mobile *string `json:"mobile,optional"`
	// Address
	Address *string `json:"address,optional"`
}

// UserAddres information response | UserAddres信息返回体
// swagger:model UserAddresInfoResp
type UserAddresInfoResp struct {
	BaseDataInfo
	// UserAddres information | UserAddres数据
	Data UserAddresInfo `json:"data"`
}

// The response data of product information | Product信息
// swagger:model ProductInfo
type ProductInfo struct {
	Id *uint64 `json:"id,optional"`
	// UserId
	UserId *uint64 `json:"userId,optional"`
	// Title
	Title *string `json:"title,optional"`
	// Sn
	Sn *string `json:"sn,optional"`
	// Merchant
	Merchant *string `json:"merchant,optional"`
	// Color
	Color *string `json:"color,optional"`
	// Language
	Language *string `json:"language,optional"`
	// RegisterTime
	RegisterTime *int64 `json:"registerTime,optional"`
	// CreateTime
	CreateTime *int64 `json:"createTime,optional"`
	// UpdateTime
	UpdateTime *int64 `json:"updateTime,optional"`
}

// The response data of product list | Product列表数据
// swagger:model ProductListResp
type ProductListResp struct {
	BaseDataInfo
	// Product list data | Product列表数据
	Data ProductListInfo `json:"data"`
}

// Product list data | Product列表数据
// swagger:model ProductListInfo
type ProductListInfo struct {
	BaseListInfo
	// The API list data | Product列表数据
	Data []ProductInfo `json:"data"`
}

// Get product list request params | Product列表请求参数
// swagger:model ProductListReq
type ProductListReq struct {
	PageInfo
	// Title
	Title *string `json:"title,optional"`
	// Sn
	Sn *string `json:"sn,optional"`
	// Merchant
	Merchant *string `json:"merchant,optional"`
}

// Product information response | Product信息返回体
// swagger:model ProductInfoResp
type ProductInfoResp struct {
	BaseDataInfo
	// Product information | Product数据
	Data ProductInfo `json:"data"`
}

// The response data of app version information | AppVersion信息
// swagger:model AppVersionInfo
type AppVersionInfo struct {
	BaseIDInfo
	// 版本更新内容 支持<br>自动换行
	Describe *string `json:"describe,optional"`
	// 状态
	Status *uint32 `json:"status,optional"`
	// 版本名称 manifest里的版本名称
	EditionName *string `json:"editionName,optional"`
	// 本号 最重要的manifest里的版本号 （检查更新主要以服务器返回的edition_number版本号是否大于当前app的版本号来实现是否更新）
	EditionNumber *int32 `json:"editionNumber,optional"`
	// apk、wgt包下载地址或者应用市场地址  安卓应用市场 market://details?id=xxxx 苹果store itms-apps://itunes.apple.com/cn/app/xxxxxx
	EditionUrl string `json:"editionUrl,optional"`
	// 是否强制更新 0代表否 1代表是
	EditionForce *int32 `json:"editionForce,optional"`
	// 0是整包升级（apk或者appstore或者安卓应用市场） 1是wgt升级
	PackageType *int32 `json:"packageType,optional"`
	// 是否发行  0否 1是 为了控制上架应用市场审核时不能弹出热更新框
	EditionIssue *int32 `json:"editionIssue,optional"`
	// 是否静默更新 0代表否 1代表是
	EditionSilence *int32 `json:"editionSilence,optional"`
}

// swagger:model AppVersionInfoReq
type AppVersionInfoReq struct {
	AppVersionInfo
}

// The response data of app version list | AppVersion列表数据
// swagger:model AppVersionListResp
type AppVersionListResp struct {
	BaseDataInfo
	// AppVersion list data | AppVersion列表数据
	Data AppVersionListInfo `json:"data"`
}

// AppVersion list data | AppVersion列表数据
// swagger:model AppVersionListInfo
type AppVersionListInfo struct {
	BaseListInfo
	// The API list data | AppVersion列表数据
	Data []AppVersionInfo `json:"data"`
}

// Get app version list request params | AppVersion列表请求参数
// swagger:model AppVersionListReq
type AppVersionListReq struct {
	PageInfo
}

// AppVersion information response | AppVersion信息返回体
// swagger:model AppVersionInfoResp
type AppVersionInfoResp struct {
	BaseDataInfo
	// AppVersion information | AppVersion数据
	Data AppVersionInfo `json:"data"`
}

// AppVersionCompare
// swagger:model AppVersionCompareReq
type AppVersionCompareReq struct {
	EditionNumber *int32  `json:"editionNumber,optional"`
	Appid         *string `json:"appid,optional"`
	Platform      *string `json:"platform,optional"` //android或者ios
	ChannelId     *string `json:"channelId,optional"`
}

// The response data of tag follow information | TagFollow信息
// swagger:model TagFollowInfo
type TagFollowInfo struct {
	Id *uint64 `json:"id,optional"`
	// 用户ID
	UserId *uint64 `json:"userId,optional"`
	// 圈子ID
	TagId *uint64 `json:"tagId,optional"`
	// 状态（0、退出 1、加入）
	Status *uint32 `json:"status,optional"`
	// 创建时间
	CreateTime *int64 `json:"createTime,optional"`
	// 最后修改时间
	UpdateTime *int64 `json:"updateTime,optional"`
}

// The response data of tag follow list | TagFollow列表数据
// swagger:model TagFollowListResp
type TagFollowListResp struct {
	BaseDataInfo
	// TagFollow list data | TagFollow列表数据
	Data TagFollowListInfo `json:"data"`
}

// TagFollow list data | TagFollow列表数据
// swagger:model TagFollowListInfo
type TagFollowListInfo struct {
	BaseListInfo
	// The API list data | TagFollow列表数据
	Data []TagFollowInfo `json:"data"`
}

// Get tag follow list request params | TagFollow列表请求参数
// swagger:model TagFollowListReq
type TagFollowListReq struct {
	PageInfo
}

// TagFollow information response | TagFollow信息返回体
// swagger:model TagFollowInfoResp
type TagFollowInfoResp struct {
	BaseDataInfo
	// TagFollow information | TagFollow数据
	Data IDReq `json:"data"`
}

// The response data of mall product information | 商品信息
// swagger:model MallProductInfo
type MallProductInfo struct {
	BaseIDInfo
	// 商品编码
	ProductCode *string `json:"productCode,optional"`
	// 商品名称
	Name *string `json:"name,optional"`
	// 商品简介
	Brief *string `json:"brief,optional"`
	// 商品详细描述
	Description *string `json:"description,optional"`
	// 封面图片
	CoverImage *string `json:"coverImage,optional"`
	// 商品图片
	Images *string `json:"images,optional"`
	// 分类ID
	CategoryId *uint64 `json:"categoryId,optional"`
	// 商品类型:1实物,2虚拟
	Type *int32 `json:"type,optional"`
	// 货币类型
	Currency *string `json:"currency,optional"`
	// 总库存(所有SKU库存之和)
	TotalStock *int64 `json:"totalStock,optional"`
	// 状态:0下架,1上架,2预售,3缺货
	Status *uint32 `json:"status,optional"`
}

// The response data of mall product list | 商品信息列表数据
// swagger:model MallProductListResp
type MallProductListResp struct {
	BaseDataInfo
	// The mall product list data | 商品信息列表数据
	Data MallProductListInfo `json:"data"`
}

// The mall product list data | 商品信息列表数据
// swagger:model MallProductListInfo
type MallProductListInfo struct {
	BaseListInfo
	// The mall product list data | 商品信息列表数据
	Data []MallProductInfo `json:"data"`
}

// Get mall product list request params | 商品列表请求参数
// swagger:model MallProductListReq
type MallProductListReq struct {
	PageInfo
	// 商品名称
	Name *string `json:"name,optional"`
	// 商品编码
	ProductCode *string `json:"productCode,optional"`
	// 商品简介
	Brief *string `json:"brief,optional"`
	// 商品描述
	Description *string `json:"description,optional"`
	// 封面图片
	CoverImage *string `json:"coverImage,optional"`
	// 商品图片
	Images *string `json:"images,optional"`
	// 分类ID
	CategoryId *uint64 `json:"categoryId,optional"`
	// 商品类型:1实物,2虚拟
	Type *int32 `json:"type,optional"`
	// 货币类型
	Currency *string `json:"currency,optional"`
	// 总库存
	TotalStock *int64 `json:"totalStock,optional"`
	// 状态:0下架,1上架,2预售,3缺货
	Status *uint32 `json:"status,optional"`
	// 商品ID
	ProductId *uint64 `json:"productId,optional"`
	// 配额类型
	QuotaType *int32 `json:"quotaType,optional"`
	// 配额值
	QuotaValue *int64 `json:"quotaValue,optional"`
	// 单价
	UnitPrice *int64 `json:"unitPrice,optional"`
	// 最小购买量
	MinPurchase *int64 `json:"minPurchase,optional"`
	// 最大购买量
	MaxPurchase *int64 `json:"maxPurchase,optional"`
	// 属性
	Attributes *string `json:"attributes,optional"`
	// 价格
	Price *int64 `json:"price,optional"`
	// SKU编码
	SkuCode *string `json:"skuCode,optional"`
}

// The mall product information response | 商品信息返回体
// swagger:model MallProductInfoResp
type MallProductInfoResp struct {
	BaseDataInfo
	// The mall product information | 商品信息数据
	Data MallProductInfo `json:"data"`
}

// Get mall product by App package name request | 通过应用包名获取商品信息请求
// swagger:model AppPackageNameReq
type AppPackageNameReq struct {
	// 应用包名
	// required : true
	AppPackageName *string `json:"appPackageName" validate:"required"`
}

// The product information response | 商品信息返回体
// swagger:model MallProductResp
type MallProductResp struct {
	BaseDataInfo
	Data MallProductRespData `json:"data"`
}

// The product information response data | 商品信息返回体数据
type MallProductRespData struct {
	// 商品ID
	ProductId *uint64 `json:"productId,optional"`
	// 商品编码
	ProductCode *string `json:"productCode,optional"`
	// 商品名称
	Name *string `json:"name,optional"`
	// 商品简介
	Brief *string `json:"brief,optional"`
	// 商品详细描述
	Description *string `json:"description,optional"`
	// 封面图片
	CoverImage *string `json:"coverImage,optional"`
	// 商品图片
	Images *string `json:"images,optional"`
	// 商品类型:1实物,2虚拟
	Type *int32 `json:"type,optional"`
	// 货币类型
	Currency *string `json:"currency,optional"`
	// SKU列表
	SkuList []MallProductSkuInfo `json:"skuList,optional"`
	// APP_ID
	AppId *uint64 `json:"appId,optional"`
	// APP_LOGO
	AppLogo *string `json:"appLogo,optional"`
	// APP_NAME
	AppName *string `json:"appName,optional"`
}

// The response data of mall order information | 订单信息
// swagger:model MallOrderInfo
type MallOrderInfo struct {
	BaseIDInfo
	// 订单编号
	OrderNo *string `json:"orderNo,optional"`
	// 用户ID
	UserId *uint64 `json:"userId,optional"`
	// 订单类型:1实物订单,2虚拟订单
	OrderType *int32 `json:"orderType,optional"`
	// 状态:0待支付,1已支付,2已发货,3已完成,4已取消,5已退款,6部分退款
	Status *uint32 `json:"status,optional"`
	// 总金额(分)
	TotalAmount *int64 `json:"totalAmount,optional"`
	// 支付金额(分)
	PayAmount *int64 `json:"payAmount,optional"`
	// 货币类型
	Currency *string `json:"currency,optional"`
	// 支付时间
	PayTime *int64 `json:"payTime,optional"`
	// 备注
	Remark *string `json:"remark,optional"`
	// 订单Item
	Items []*MallOrderItemInfo `json:"items,optional"`
	// 支付渠道
	PayChannel *int32 `json:"payChannel,optional"`
	// 支付渠道订单号
	PayChannelOrderNo *string `json:"payChannelOrderNo,optional"`
	// 设备序列号
	Sn *string `json:"sn,optional"`
}

// The response data of mall order list | 订单列表数据
// swagger:model MallOrderListResp
type MallOrderListResp struct {
	BaseDataInfo
	// The mall order list data | 订单列表数据
	Data MallOrderListInfo `json:"data"`
}

// The mall order list data | 订单列表数据
// swagger:model MallOrderListInfo
type MallOrderListInfo struct {
	BaseListInfo
	// The mall order list data | 订单列表数据
	Data []MallOrderInfo `json:"data"`
}

// Get mall order list request params | 订单列表请求参数
// swagger:model MallOrderListReq
type MallOrderListReq struct {
	PageInfo
	// 订单ID
	OrderId *uint64 `json:"orderId,optional"`
	// 商品ID
	ProductId *uint64 `json:"productId,optional"`
	// SKU ID
	SkuId *uint64 `json:"skuId,optional"`
	// 商品名称
	ProductName *string `json:"productName,optional"`
	// SKU名称
	SkuName *string `json:"skuName,optional"`
	// 购买数量
	Quantity *int32 `json:"quantity,optional"`
	// 单价
	Price *int64 `json:"price,optional"`
	// 总金额
	TotalAmount *int64 `json:"totalAmount,optional"`
	// 配额类型
	QuotaType *int32 `json:"quotaType,optional"`
	// 配额金额
	QuotaAmount *int64 `json:"quotaAmount,optional"`
	// 单价
	UnitPrice *int64 `json:"unitPrice,optional"`
	// 订单编号
	OrderNo *string `json:"orderNo,optional"`
	// 用户ID
	UserId *uint64 `json:"userId,optional"`
	// 订单类型:1实物订单,2虚拟订单
	OrderType *int32 `json:"orderType,optional"`
	// 状态:0待支付,1已支付,2已发货,3已完成,4已取消,5已退款,6部分退款
	Status *uint32 `json:"status,optional"`
	// 支付金额(分)
	PayAmount *int64 `json:"payAmount,optional"`
	// 货币类型
	Currency *string `json:"currency,optional"`
	// 支付时间
	PayTime *int64 `json:"payTime,optional"`
	// 状态:0待支付,1已支付,2已发货,3已完成,4已取消,5已退款,6部分退款
	FromStatus *int32 `json:"fromStatus,optional"`
	// 状态:0待支付,1已支付,2已发货,3已完成,4已取消,5已退款,6部分退款
	ToStatus *int32 `json:"toStatus,optional"`
	// 备注
	Remark *string `json:"remark,optional"`
	// 操作员ID
	OperatorId *uint64 `json:"operatorId,optional"`
	// 操作员名称
	OperatorName *string `json:"operatorName,optional"`
	// 支付渠道
	PayChannel *int32 `json:"payChannel,optional"`
	// 支付渠道订单号
	PayChannelOrderNo *string `json:"payChannelOrderNo,optional"`
	// 设备序列号
	DeviceId *string `json:"deviceId,optional"`
	// 机器码
	MachineCode *string `json:"machineCode,optional"`
}

// The mall order information response | MallOrder信息返回体
// swagger:model MallOrderInfoResp
type MallOrderInfoResp struct {
	BaseDataInfo
	// The mall order information | 订单信息数据
	Data MallOrderInfo `json:"data"`
}

// 创建订单
// swagger:model CreateMallOrderReq
type CreateMallOrderReq struct {
	// 商品列表
	// required : true
	// min length : 1
	Items []CreateOrderItem `json:"items" validate:"required,min=1"`
	// 货币类型
	// required : true
	Currency string `json:"currency" validate:"required"`
	// 订单类型 1实物订单,2虚拟订单,3混合订单
	// required : true
	OrderType int32 `json:"orderType" validate:"required,oneof=1 2 3"`
	// 收货人信息
	UserAddressId uint64 `json:"userAddressId,optional"`
	// 订单备注
	Remark *string `json:"remark,optional"`
	// 设备ID
	DeviceId *string `json:"deviceId,optional"`
	// 机器码
	MachineCode *string `json:"machineCode,optional"`
}

// 创建订单商品项
type CreateOrderItem struct {
	// 商品ID
	// required : true
	ProductId uint64 `json:"productId" validate:"required"`
	// SKU ID
	// required : true
	SkuId uint64 `json:"skuId" validate:"required"`
	// 购买数量
	// required : true
	// min : 0
	Quantity int64 `json:"quantity" validate:"required,gt=0"`
}

// 创建订单响应
// swagger:model CreateMallOrderResp
type CreateMallOrderResp struct {
	BaseDataInfo
	Data CreateOrderRespData `json:"data"`
}

// 创建订单响应数据
type CreateOrderRespData struct {
	// 订单编号
	OrderNo string `json:"orderNo"`
	// 订单金额(分)
	TotalAmount int64 `json:"totalAmount"`
	// 支付金额(分)
	PayAmount int64 `json:"payAmount"`
	// 币种
	Currency string `json:"currency"`
}

// swagger:model MallOrderSubmitReq
type MallOrderSubmitReq struct {
	// 订单编号
	// required : true
	OrderNo string `json:"orderNo" validate:"required"`
	// 支付渠道
	// required : true
	PayChannel int32 `json:"payChannel" validate:"required,oneof=1 2"`
	// 支付渠道额外参数
	ChannelExtras map[string]string `json:"channelExtras,optional"`
	// 交易类型
	TradeType string `json:"tradeType,optional"`
	// 货币类型
	Currency string `json:"currency,optional"`
}

// swagger:model MallOrderSubmitResp
type MallOrderSubmitResp struct {
	BaseDataInfo
	Data MallOrderSubmitRespData `json:"data"`
}

type MallOrderSubmitRespData struct {
	// 订单编号
	OrderNo string `json:"orderNo"`
	// 支付参数
	PayParams string `json:"payParams"`
}

// 查询支付状态请求
// swagger:model QueryPaymentStatusReq
type QueryPaymentStatusReq struct {
	OrderNo string `json:"orderNo"` // 商户订单号
}

// 查询支付状态响应
// swagger:model QueryPaymentStatusResp
type QueryPaymentStatusResp struct {
	BaseDataInfo
	Data QueryPaymentStatusRespData `json:"data"`
}

// 查询支付状态响应数据
type QueryPaymentStatusRespData struct {
	TradeState     uint32  `json:"tradeState"`             // 交易状态 0:待支付 1:已支付 2:已发货 3:已完成 4:已取消 5:已退款 6:部分退款
	TradeStateDesc string  `json:"tradeStateDesc"`         // 交易状态描述
	TransactionId  *string `json:"transactionId,optional"` // 支付渠道交易号
	PayTime        *string `json:"payTime,optional"`       // 支付完成时间
	LicenseKey     *string `json:"licenseKey,optional"`    // 许可证密钥 如果订单类型是虚拟订单，则返回许可证密钥
}

// 匿名下单请求
// swagger:model CreateAnonymousMallOrderReq
type CreateAnonymousMallOrderReq struct {
	CreateMallOrderReq
	// 订单类型 2虚拟订单
	// required : true
	OrderType int32 `json:"orderType" validate:"required,oneof=2"`
	// Receiver | 接收人
	Receiver string `json:"receiver"` // 接收人
	// Verfication Code | 验证码
	VerificationCode string `json:"verificationCode"` // 验证码
}

// 匿名订单列表
// swagger:model AnonymousMallOrderListReq
type AnonymousMallOrderListReq struct {
	PageInfo
	// Email | 邮箱
	Email *string `json:"email,optional"` // 下单邮箱
	// Device Id | 设备ID
	DeviceId *string `json:"deviceId,optional"` // 下单设备ID
	// Machine Code | 机器码
	MachineCode *string `json:"machineCode,optional"` // 下单机器码
}

// 匿名订单
// swagger:model AnonymousMallOrderReq
type AnonymousMallOrderReq struct {
	// 订单编号
	OrderNo string `json:"orderNo"` // 下单订单编号
	// Email | 邮箱
	Email string `json:"email"` // 下单邮箱
	// Device Id | 设备ID
	DeviceId string `json:"deviceId"` // 下单设备ID
	// Machine Code | 机器码
	MachineCode string `json:"machineCode"` // 下单机器码
}

// Payment Notify | 支付回调
// swagger:model NotifyReq
type NotifyReq struct {
	// 渠道编码
	ChannelCode string `path:"channelCode"`
	// 回调数据
	R []byte `json:"r,optional"`
	// HTTP头信息
	Headers string `json:"headers,optional"`
}

// Mock Payment Notify | 模拟支付回调
// swagger:model MockNotifyReq
type MockNotifyReq struct {
	// 渠道编码
	ChannelCode string `path:"channelCode"`
	// 支付结果:SUCCESS,FAIL
	Result string `json:"result"`
	// 订单编号
	OrderNo string `json:"orderNo"`
}

// Alipay Success Request | 支付成功请求
// swagger:model AlipaySuccessReq
type AlipaySuccessReq struct {
	// 订单编号
	OutTradeNo *string `form:"out_trade_no"`
	// 支付宝交易号
	TradeNo *string `form:"trade_no"`
	// 总金额
	TotalAmount *string `form:"total_amount"`
	// 签名
	Sign *string `form:"sign"`
	// 签名类型
	SignType *string `form:"sign_type"`
	// 应用ID
	AppId *string `form:"app_id"`
	// 卖家ID
	SellerId *string `form:"seller_id"`
	// 时间戳
	Timestamp *string `form:"timestamp"`
	// 版本
	Version *string `form:"version"`
}

// The response data of mall product sku information | 商品SKU信息
// swagger:model MallProductSkuInfo
type MallProductSkuInfo struct {
	BaseIDInfo
	// 商品ID
	ProductId *uint64 `json:"productId,optional"`
	// 属性JSON
	Attributes *string `json:"attributes,optional"`
	// 原价(分)
	OriginalPrice *int64 `json:"originalPrice,optional"`
	// 售价(分)
	SellingPrice *int64 `json:"sellingPrice,optional"`
	// 库存
	Stock *int64 `json:"stock,optional"`
	// SKU编码
	SkuCode *string `json:"skuCode,optional"`
	// 状态:0禁用,1启用
	Status *uint32 `json:"status,optional"`
	// 长度(cm)
	Length *int32 `json:"length,optional"`
	// 宽度(cm)
	Width *int32 `json:"width,optional"`
	// 高度(cm)
	Height *int32 `json:"height,optional"`
	// 重量(g)
	Weight *int32 `json:"weight,optional"`
	// 商品类型:0、普通商品,1、license商品
	ProductType *int32 `json:"productType,optional"`
	// 授权天数/用量
	UsageCount *int64 `json:"usageCount,optional"`
	// SKU 名称
	Name *string `json:"name,optional"`
	// 是否特价:0否,1是
	IsOnSpecial *int32 `json:"isOnSpecial,optional"`
	// 特价
	SpecialPrice *int64 `json:"specialPrice,optional"`
	// 特价类型:0无,1新用户特价,2新设备特价,3限时特价
	SpecialSaleType *int32 `json:"specialSaleType,optional"`
	// 限购数量(单个用户/设备)
	SpecialSaleLimit *int64 `json:"specialSaleLimit,optional"`
	// 特价开始时间
	SpecialPriceStartTime *int64 `json:"specialPriceStartTime,optional"`
	// 特价结束时间
	SpecialPriceEndTime *int64 `json:"specialPriceEndTime,optional"`
	// 图片
	Image *string `json:"image,optional"`
	// 特价商品卖完恢复原价:0、不恢复 1、恢复
	SellOutRestoreOrigin *int32 `json:"sellOutRestoreOrigin,optional"`
}

// The response data of mall product sku list | 商品SKU信息列表数据
// swagger:model MallProductSkuListResp
type MallProductSkuListResp struct {
	BaseDataInfo
	// MallProductSku list data | 商品SKU信息列表数据
	Data MallProductSkuListInfo `json:"data"`
}

// The mall product sku list data | 商品SKU信息列表数据
// swagger:model MallProductSkuListInfo
type MallProductSkuListInfo struct {
	BaseListInfo
	// The mall product sku list data | 商品SKU信息列表数据
	Data []MallProductSkuInfo `json:"data"`
}

// Get mall product sku list request params | MallProductSku列表请求参数
// swagger:model MallProductSkuListReq
type MallProductSkuListReq struct {
	PageInfo
	// // DeletedAt
	// DeletedAt  *int64 `json:"deletedAt,optional"`
	// ProductId
	ProductId *uint64 `json:"productId,optional"`
	// 商品类型:0、普通商品,1、license商品
	ProductType *int32 `json:"productType,optional"`
	// 是否特价:0否,1是
	IsOnSpecial *int32 `json:"isOnSpecial,optional"`
}

// The mall product sku information response | 商品SKU信息返回体
// swagger:model MallProductSkuInfoResp
type MallProductSkuInfoResp struct {
	BaseDataInfo
	// The mall product sku information | 商品SKU信息数据
	Data MallProductSkuInfo `json:"data"`
}

// Get Mall Product By DeviceId and MachineCode | 通过设备ID和机器码获取商品SKU信息
// swagger:model GetMallProductByDeviceIdAndMachineCodeReq
type GetMallProductByDeviceIdAndMachineCodeReq struct {
	IDReq
	// 设备ID
	DeviceId *string `json:"deviceId,optional"`
	// 机器码
	MachineCode *string `json:"machineCode,optional"`
}

// The response data of mall order item information | MallOrderItem信息
// swagger:model MallOrderItemInfo
type MallOrderItemInfo struct {
	BaseIDInfo
	// 订单编号
	OrderNo *string `json:"orderNo,optional"`
	// 商品ID
	ProductId *uint64 `json:"productId,optional"`
	// SKU ID
	SkuId *uint64 `json:"skuId,optional"`
	// 商品名称
	ProductName *string `json:"productName,optional"`
	// SKU名称
	SkuName *string `json:"skuName,optional"`
	// 商品简介(下单时)
	ProductBrief *string `json:"productBrief,optional"`
	// 商品详细描述(下单时)
	ProductDescription *string `json:"productDescription,optional"`
	// 商品封面图(下单时)
	ProductCoverImage *string `json:"productCoverImage,optional"`
	// 商品图片(下单时)
	ProductImages *string `json:"productImages,optional"`
	// 商品原价(分)(下单时)
	ProductOriginalPrice *int64 `json:"productOriginalPrice,optional"`
	// 商品售价(分)(下单时)
	ProductSellingPrice *int64 `json:"productSellingPrice,optional"`
	// 货币类型(下单时)
	ProductCurrency *string `json:"productCurrency,optional"`
	// 商品类型:1实物,2虚拟(下单时)
	ProductType *int32 `json:"productType,optional"`
	// SKU属性快照JSON
	SkuAttributesSnapshot *string `json:"skuAttributesSnapshot,optional"`
	// 数量
	Quantity *int64 `json:"quantity,optional"`
	// 单价(分)
	Price *int64 `json:"price,optional"`
	// 总金额(分)
	TotalAmount *int64 `json:"totalAmount,optional"`
	// 配额类型:1 api_calls-API调用次数,2 tokens-Token数量
	QuotaType *int32 `json:"quotaType,optional"`
	// 购买的配额数量
	QuotaAmount *int64 `json:"quotaAmount,optional"`
	// 单价(分/次或分/token)
	UnitPrice *int64 `json:"unitPrice,optional"`
	// 商品SKU CODE
	SkuCode *string `json:"skuCode,optional"`
	// 关联订单商品编号
	ItemNo *string `json:"itemNo,optional"`
}

// The response data of mall order item list | MallOrderItem信息列表数据
// swagger:model MallOrderItemListResp
type MallOrderItemListResp struct {
	BaseDataInfo
	// MallOrderItem list data | MallOrderItem信息列表数据
	Data MallOrderItemListInfo `json:"data"`
}

// The mall order item list data | MallOrderItem信息列表数据
// swagger:model MallOrderItemListInfo
type MallOrderItemListInfo struct {
	BaseListInfo
	// The mall order item list data | MallOrderItem信息列表数据
	Data []MallOrderItemInfo `json:"data"`
}

// Get mall order item list request params | MallOrderItem列表请求参数
// swagger:model MallOrderItemListReq
type MallOrderItemListReq struct {
	PageInfo
	// DeletedAt
	DeletedAt *int64 `json:"deletedAt,optional"`
	// OrderNo
	OrderNo *string `json:"orderNo,optional"`
	// ProductId
	ProductId *uint64 `json:"productId,optional"`
	// SkuId
	SkuId *uint64 `json:"skuId,optional"`
	// ProductName
	ProductName *string `json:"productName,optional"`
	// SkuName
	SkuName *string `json:"skuName,optional"`
	// ProductBrief
	ProductBrief *string `json:"productBrief,optional"`
	// ProductDescription
	ProductDescription *string `json:"productDescription,optional"`
	// ProductCoverImage
	ProductCoverImage *string `json:"productCoverImage,optional"`
	// ProductImages
	ProductImages *string `json:"productImages,optional"`
	// ProductOriginalPrice
	ProductOriginalPrice *int64 `json:"productOriginalPrice,optional"`
	// ProductSellingPrice
	ProductSellingPrice *int64 `json:"productSellingPrice,optional"`
	// ProductCurrency
	ProductCurrency *string `json:"productCurrency,optional"`
	// ProductType
	ProductType *int32 `json:"productType,optional"`
	// SkuAttributesSnapshot
	SkuAttributesSnapshot *string `json:"skuAttributesSnapshot,optional"`
	// Quantity
	Quantity *int64 `json:"quantity,optional"`
	// Price
	Price *int64 `json:"price,optional"`
	// TotalAmount
	TotalAmount *int64 `json:"totalAmount,optional"`
	// QuotaType
	QuotaType *int32 `json:"quotaType,optional"`
	// QuotaAmount
	QuotaAmount *int64 `json:"quotaAmount,optional"`
	// UnitPrice
	UnitPrice *int64 `json:"unitPrice,optional"`
	// SkuCode
	SkuCode *string `json:"skuCode,optional"`
}

// The mall order item information response | MallOrderItem信息返回体
// swagger:model MallOrderItemInfoResp
type MallOrderItemInfoResp struct {
	BaseDataInfo
	// The mall order item information | MallOrderItem信息数据
	Data MallOrderItemInfo `json:"data"`
}

// License | License信息
// swagger:model LicenseInfo
type LicenseInfo struct {
	LicenseKey       string  `json:"licenseKey"`              // License密钥
	MaxDevices       int32   `json:"maxDevices"`              // 最大设备数
	ActivationStatus int32   `json:"activationStatus"`        // 激活状态
	ActivationTime   *string `json:"activationTime,optional"` // 激活时间
}

// Query License | 查询License
// swagger:model QueryLicenseReq
type QueryLicenseReq struct {
	OrderNo string `json:"orderNo"` // 订单号
}

// Query License Response | 查询License响应
// swagger:model QueryLicenseResp
type QueryLicenseResp struct {
	BaseDataInfo
	Data LicenseInfo `json:"data"`
}

// Activate License | 激活License
// swagger:model ActivateLicenseReq
type ActivateLicenseReq struct {
	// required : true
	LicenseKey string `json:"licenseKey" validate:"required"` // License密钥
	HeartbeatReq
}

// Activate License Response | 激活License响应
// swagger:model ActivateLicenseResp
type ActivateLicenseResp struct {
	BaseDataInfo
	Data ActivateLicenseRespData `json:"data"`
}

// Activate License Response Data | 激活License响应数据
type ActivateLicenseRespData struct {
	HeartbeatRespData
}

// Heartbeat Request | 心跳请求
// swagger:model HeartbeatReq
type HeartbeatReq struct {
	MachineCode string `json:"machineCode"` // 机器码
	AppCode     string `json:"appCode"`     // 应用包名
	DeviceType  string `json:"deviceType"`  // 设备类型
	DeviceId    string `json:"deviceId"`    // 设备唯一标识
	Timestamp   int64  `json:"timestamp"`   // 请求时间戳
	Nonce       string `json:"nonce"`       // 随机字符串
	Signature   string `json:"signature"`   // 请求签名
}

// Heartbeat Response | 心跳响应
// swagger:model HeartbeatResp
type HeartbeatResp struct {
	BaseDataInfo
	Data HeartbeatRespData `json:"data"`
}

// Heartbeat Response Data | 心跳响应数据
type HeartbeatRespData struct {
	// Client Expiration Time | 客户端过期时间戳
	ClientExpirationTime int64 `json:"clientExpirationTime"`
	// Server Expiration Time | 服务端过期时间戳
	ServerExpirationTime int64 `json:"serverExpirationTime"`
	// Server Time | 服务器时间戳
	ServerTime int64 `json:"serverTime"`
	// Nonce | 随机字符串
	Nonce string `json:"nonce"`
	// Signature | 签名
	Signature string `json:"signature"`
}

// Get Machine Code | 获取机器码
// swagger:model GetMachineCodeReq
type GetMachineCodeReq struct {
	AppCode    string `json:"appCode"`    // 应用包名
	DeviceType string `json:"deviceType"` // 设备类型
	DeviceId   string `json:"deviceId"`   // 设备唯一标识
}

// Get Machine Code Response | 获取机器码响应
// swagger:model GetMachineCodeResp
type GetMachineCodeResp struct {
	BaseDataInfo
	Data GetMachineCodeRespData `json:"data"`
}

// Get Machine Code Response Data | 获取机器码响应数据
type GetMachineCodeRespData struct {
	GetMachineCodeReq
	// 机器码
	MachineCode string `json:"machineCode"`
	// 公钥
	PublicKey string `json:"publicKey"`
}

// Bind Device | 绑定设备
// swagger:model BindDeviceReq
type BindDeviceReq struct {
	GetMachineCodeReq
	MachineCode string `json:"machineCode"` // 机器码
}

// Unbind Device | 解绑设备
// swagger:model UnbindDeviceReq
type UnbindDeviceReq struct {
	GetMachineCodeReq
	MachineCode string `json:"machineCode"` // 机器码
	Timestamp   int64  `json:"timestamp"`   // 请求时间戳
	Nonce       string `json:"nonce"`       // 随机字符串
	Signature   string `json:"signature"`   // 请求签名
}

// swagger:model ServerTimeResp
type ServerTimeResp struct {
	BaseDataInfo
	Data ServerTimeRespData `json:"data"`
}

type ServerTimeRespData struct {
	// Server Time | 服务器时间戳
	ServerTime int64 `json:"serverTime"`
}

// swagger:model ServerStatusReq
type ServerStatusReq struct {
	MachineCode string `json:"machineCode"` // 机器码
	AppCode     string `json:"appCode"`     // 应用包名
	DeviceType  string `json:"deviceType"`  // 设备类型
	DeviceId    string `json:"deviceId"`    // 设备唯一标识
	Timestamp   int64  `json:"timestamp"`   // 请求时间戳
	Nonce       string `json:"nonce"`       // 随机字符串
	Signature   string `json:"signature"`   // 请求签名
}

// swagger:model ServerStatusResp
type ServerStatusResp struct {
	BaseDataInfo
	Data ServerStatusRespData `json:"data"`
}

type ServerStatusRespData struct {
	// 服务器状态
	Status int32 `json:"status"`
	// Server Time | 服务器时间戳
	ServerTime int64 `json:"serverTime"`
	// Nonce | 随机字符串
	Nonce string `json:"nonce"`
	// Signature | 签名
	Signature string `json:"signature"`
}

// The response data of bolo lexicon information | BoloLexicon信息
// swagger:model BoloLexiconInfo
type BoloLexiconInfo struct {
	BaseIDInfo
	// 词汇内容
	Word *string `json:"word,optional"`
	// 词汇类型
	WordType *string `json:"wordType,optional"`
	// 拼音标注
	Pinyin *string `json:"pinyin,optional"`
	// 词汇释义
	Definition *string `json:"definition,optional"`
	// 状态 1=启用 0=禁用
	Status *uint32 `json:"status,optional"`
	// 创建人ID
	CreatorId *uint64 `json:"creatorId,optional"`
	// 权重（用于排序）
	Weight *int32 `json:"weight,optional"`
	// 标签（逗号分隔）
	Tags *string `json:"tags,optional"`
	// 示例用法
	Example *string `json:"example,optional"`
}

// The response data of bolo lexicon list | BoloLexicon信息列表数据
// swagger:model BoloLexiconListResp
type BoloLexiconListResp struct {
	BaseDataInfo
	// BoloLexicon list data | BoloLexicon信息列表数据
	Data BoloLexiconListInfo `json:"data"`
}

// The bolo lexicon list data | BoloLexicon信息列表数据
// swagger:model BoloLexiconListInfo
type BoloLexiconListInfo struct {
	BaseListInfo
	// The bolo lexicon list data | BoloLexicon信息列表数据
	Data []BoloLexiconInfo `json:"data"`
}

// Get bolo lexicon list request params | BoloLexicon列表请求参数
// swagger:model BoloLexiconListReq
type BoloLexiconListReq struct {
	PageInfo
	// CreatedAt
	CreatedAt *int64 `json:"createdAt,optional"`
	// UpdatedAt
	UpdatedAt *int64 `json:"updatedAt,optional"`
	// DeletedAt
	DeletedAt *int64 `json:"deletedAt,optional"`
	// Word
	Word *string `json:"word,optional"`
	// WordType
	WordType *string `json:"wordType,optional"`
	// Pinyin
	Pinyin *string `json:"pinyin,optional"`
	// Definition
	Definition *string `json:"definition,optional"`
	// Status
	Status *uint32 `json:"status,optional"`
	// CreatorId
	CreatorId *uint64 `json:"creatorId,optional"`
	// Weight
	Weight *int32 `json:"weight,optional"`
	// Tags
	Tags *string `json:"tags,optional"`
	// Example
	Example *string `json:"example,optional"`
}

// The bolo lexicon information response | BoloLexicon信息返回体
// swagger:model BoloLexiconInfoResp
type BoloLexiconInfoResp struct {
	BaseDataInfo
	// The bolo lexicon information | BoloLexicon信息数据
	Data BoloLexiconInfo `json:"data"`
}

// The response data of user agreement information | UserAgreement信息
// swagger:model UserAgreementInfo
type UserAgreementInfo struct {
	Id *uint64 `json:"id,optional"`
	// 协议标题
	Title *string `json:"title,optional"`
	// 协议正文
	Content *string `json:"content,optional"`
	// 唯一标识符
	Key *string `json:"key,optional"`
	// 状态 0 停止1开启
	Status *uint32 `json:"status,optional"`
	// 创建时间
	CreatedAt *int64 `json:"createdAt,optional"`
	// 修改时间
	UpdatedAt *int64 `json:"updatedAt,optional"`
}

// The response data of user agreement list | UserAgreement信息列表数据
// swagger:model UserAgreementListResp
type UserAgreementListResp struct {
	BaseDataInfo
	// UserAgreement list data | UserAgreement信息列表数据
	Data UserAgreementListInfo `json:"data"`
}

// The user agreement list data | UserAgreement信息列表数据
// swagger:model UserAgreementListInfo
type UserAgreementListInfo struct {
	BaseListInfo
	// The user agreement list data | UserAgreement信息列表数据
	Data []UserAgreementInfo `json:"data"`
}

// Get user agreement list request params | UserAgreement列表请求参数
// swagger:model UserAgreementListReq
type UserAgreementListReq struct {
	PageInfo
	// Title
	Title *string `json:"title,optional"`
	// Content
	Content *string `json:"content,optional"`
	// Key
	Key *string `json:"key,optional"`
	// Status
	Status *uint32 `json:"status,optional"`
}

// The user agreement information response | UserAgreement信息返回体
// swagger:model UserAgreementInfoResp
type UserAgreementInfoResp struct {
	BaseDataInfo
	// The user agreement information | UserAgreement信息数据
	Data UserAgreementInfo `json:"data"`
}

// swagger:model GetUserAgreementByKeyReq
type GetUserAgreementByKeyReq struct {
	Key string `json:"key"`
}

// The response data of rental device information | RentalDevice信息
// swagger:model RentalDeviceInfo
type RentalDeviceInfo struct {
	BaseIDInfo
	// 设备序列号SN
	SerialNumber *string `json:"serialNumber,optional"`
	// 设备描述
	Description *string `json:"description,optional"`
	// 设备状态:0禁用,1启用
	DeviceStatus *uint32 `json:"deviceStatus,optional"`
	// 额外信息JSON
	ExtraInfo *string `json:"extraInfo,optional"`
}

// The response data of rental device list | RentalDevice信息列表数据
// swagger:model RentalDeviceListResp
type RentalDeviceListResp struct {
	BaseDataInfo
	// RentalDevice list data | RentalDevice信息列表数据
	Data RentalDeviceListInfo `json:"data"`
}

// The rental device list data | RentalDevice信息列表数据
// swagger:model RentalDeviceListInfo
type RentalDeviceListInfo struct {
	BaseListInfo
	// The rental device list data | RentalDevice信息列表数据
	Data []RentalDeviceInfo `json:"data"`
}

// Get rental device list request params | RentalDevice列表请求参数
// swagger:model RentalDeviceListReq
type RentalDeviceListReq struct {
	PageInfo
	// CreatedAt
	CreatedAt *int64 `json:"createdAt,optional"`
	// UpdatedAt
	UpdatedAt *int64 `json:"updatedAt,optional"`
	// DeletedAt
	DeletedAt *int64 `json:"deletedAt,optional"`
	// SerialNumber
	SerialNumber *string `json:"serialNumber,optional"`
	// Description
	Description *string `json:"description,optional"`
	// DeviceStatus
	DeviceStatus *uint32 `json:"deviceStatus,optional"`
	// ExtraInfo
	ExtraInfo *string `json:"extraInfo,optional"`
}

// The rental device information response | RentalDevice信息返回体
// swagger:model RentalDeviceInfoResp
type RentalDeviceInfoResp struct {
	BaseDataInfo
	// The rental device information | RentalDevice信息数据
	Data RentalDeviceInfo `json:"data"`
}
