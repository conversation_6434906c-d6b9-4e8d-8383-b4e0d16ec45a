// Code generated by goctl. DO NOT EDIT.
package types

// The basic response with data | 基础带数据信息
// swagger:model BaseDataInfo
type BaseDataInfo struct {
	// Error code | 错误代码
	Code int `json:"code"`
	// Message | 提示信息
	Msg string `json:"msg"`
	// Data | 数据
	Data string `json:"data,omitempty"`
}

// The basic response with data | 基础带数据信息
// swagger:model BaseListInfo
type BaseListInfo struct {
	// The total number of data | 数据总数
	Total uint64 `json:"total"`
	// Data | 数据
	Data string `json:"data,omitempty"`
}

// The basic response without data | 基础不带数据信息
// swagger:model BaseMsgResp
type BaseMsgResp struct {
	// Error code | 错误代码
	Code int `json:"code"`
	// Message | 提示信息
	Msg string `json:"msg"`
}

// The page request parameters | 列表请求参数
// swagger:model PageInfo
type PageInfo struct {
	// Page number | 第几页
	// required : true
	// min : 0
	Page uint64 `json:"page" validate:"required,number,gt=0"`
	// Page size | 单页数据行数
	// required : true
	// max : 100000
	PageSize uint64 `json:"pageSize" validate:"required,number,lt=100000"`
}

// Empty request | 无参数请求
// swagger:model EmptyReq
type EmptyReq struct {
}

// Basic ID request | 基础ID参数请求
// swagger:model IDReq
type IDReq struct {
	// ID
	// Required: true
	Id uint64 `json:"id" validate:"number"`
}

// Basic IDs request | 基础ID数组参数请求
// swagger:model IDsReq
type IDsReq struct {
	// IDs
	// Required: true
	Ids []uint64 `json:"ids"`
}

// Basic ID request | 基础ID地址参数请求
// swagger:model IDPathReq
type IDPathReq struct {
	// ID
	// Required: true
	Id uint64 `path:"id"`
}

// Basic ID request (int32) | 基础ID参数请求 (int32)
// swagger:model IDInt32Req
type IDInt32Req struct {
	// ID
	// Required: true
	Id int32 `json:"id" validate:"number"`
}

// Basic IDs request (int32) | 基础ID数组参数请求 (int32)
// swagger:model IDsInt32Req
type IDsInt32Req struct {
	// IDs
	// Required: true
	Ids []int32 `json:"ids"`
}

// Basic ID request (int32) | 基础ID地址参数请求 (int32)
// swagger:model IDInt32PathReq
type IDInt32PathReq struct {
	// ID
	// Required: true
	Id int32 `path:"id"`
}

// Basic ID request (uint32) | 基础ID参数请求 (uint32)
// swagger:model IDUint32Req
type IDUint32Req struct {
	// ID
	// Required: true
	Id uint32 `json:"id" validate:"number"`
}

// Basic IDs request (uint32) | 基础ID数组参数请求 (uint32)
// swagger:model IDsUint32Req
type IDsUint32Req struct {
	// IDs
	// Required: true
	Ids []uint32 `json:"ids"`
}

// Basic ID request (uint32) | 基础ID地址参数请求 (uint32)
// swagger:model IDUint32PathReq
type IDUint32PathReq struct {
	// ID
	// Required: true
	Id uint32 `path:"id"`
}

// Basic ID request (int64) | 基础ID参数请求 (int64)
// swagger:model IDInt64Req
type IDInt64Req struct {
	// ID
	// Required: true
	Id int64 `json:"id" validate:"number"`
}

// Basic IDs request (int64) | 基础ID数组参数请求 (int64)
// swagger:model IDsInt64Req
type IDsInt64Req struct {
	// IDs
	// Required: true
	Ids []int64 `json:"ids"`
}

// Basic ID request (int64) | 基础ID地址参数请求 (int64)
// swagger:model IDInt64PathReq
type IDInt64PathReq struct {
	// ID
	// Required: true
	Id int64 `path:"id"`
}

// Basic UUID request in path | 基础UUID地址参数请求
// swagger:model UUIDPathReq
type UUIDPathReq struct {
	// ID
	// Required: true
	Id string `path:"id"`
}

// Basic UUID request | 基础UUID参数请求
// swagger:model UUIDReq
type UUIDReq struct {
	// ID
	// required : true
	// max length : 36
	// min length : 36
	Id string `json:"id" validate:"required,len=36"`
}

// Basic UUID array request | 基础UUID数组参数请求
// swagger:model UUIDsReq
type UUIDsReq struct {
	// Ids
	// Required: true
	Ids []string `json:"ids"`
}

// The base ID response data | 基础ID信息
// swagger:model BaseIDInfo
type BaseIDInfo struct {
	// ID
	Id *uint64 `json:"id,optional"`
	// Create date | 创建日期
	CreatedAt *int64 `json:"createdAt,optional"`
	// Update date | 更新日期
	UpdatedAt *int64 `json:"updatedAt,optional"`
}

// The base ID response data (int64) | 基础ID信息 (int64)
// swagger:model BaseIDInt64Info
type BaseIDInt64Info struct {
	// ID
	Id *int64 `json:"id,optional"`
	// Create date | 创建日期
	CreatedAt *int64 `json:"createdAt,optional"`
	// Update date | 更新日期
	UpdatedAt *int64 `json:"updatedAt,optional"`
}

// The base ID response data (int32) | 基础ID信息 (int32)
// swagger:model BaseIDInt32Info
type BaseIDInt32Info struct {
	// ID
	Id *int32 `json:"id,optional"`
	// Create date | 创建日期
	CreatedAt *int64 `json:"createdAt,optional"`
	// Update date | 更新日期
	UpdatedAt *int64 `json:"updatedAt,optional"`
}

// The base ID response data (uint32) | 基础ID信息 (uint32)
// swagger:model BaseIDUint32Info
type BaseIDUint32Info struct {
	// ID
	Id *uint32 `json:"id,optional"`
	// Create date | 创建日期
	CreatedAt *int64 `json:"createdAt,optional"`
	// Update date | 更新日期
	UpdatedAt *int64 `json:"updatedAt,optional"`
}

// The base UUID response data | 基础UUID信息
// swagger:model BaseUUIDInfo
type BaseUUIDInfo struct {
	// ID
	Id *string `json:"id,optional"`
	// Create date | 创建日期
	CreatedAt *int64 `json:"createdAt,optional"`
	// Update date | 更新日期
	UpdatedAt *int64 `json:"updatedAt,optional"`
}

// swagger:model HealthCheckResp
type HealthCheckResp struct {
	Status    string            `json:"status"`    // ok | degraded | error
	Timestamp int64             `json:"timestamp"` // Unix时间戳
	Service   string            `json:"service"`   // 服务名称
	Version   string            `json:"version"`   // 版本信息
	Checks    map[string]string `json:"checks"`    // 依赖检查详情
}

// The response data of group follow information | GroupFollow信息
// swagger:model GroupFollowInfo
type GroupFollowInfo struct {
	Id *uint64 `json:"id,optional"`
	// 用户ID
	UserId *uint64 `json:"userId,optional"`
	// 状态（0、退出 1、加入）
	Status *uint32 `json:"status,optional"`
	// 创建时间
	CreateTime *int64 `json:"createTime,optional"`
	// 最后修改时间
	UpdateTime *int64 `json:"updateTime,optional"`
}

// The response data of group follow list | GroupFollow列表数据
// swagger:model GroupFollowListResp
type GroupFollowListResp struct {
	BaseDataInfo
	// GroupFollow list data | GroupFollow列表数据
	Data GroupFollowListInfo `json:"data"`
}

// GroupFollow list data | GroupFollow列表数据
// swagger:model GroupFollowListInfo
type GroupFollowListInfo struct {
	BaseListInfo
	// The API list data | GroupFollow列表数据
	Data []GroupFollowInfo `json:"data"`
}

// Get group follow list request params | GroupFollow列表请求参数
// swagger:model GroupFollowListReq
type GroupFollowListReq struct {
	PageInfo
}

// GroupFollow information response | GroupFollow信息返回体
// swagger:model GroupFollowInfoResp
type GroupFollowInfoResp struct {
	BaseDataInfo
	// GroupFollow information | GroupFollow数据
	Data GroupFollowInfo `json:"data"`
}

// The response data of group member information | GroupMember信息
// swagger:model GroupMemberInfo
type GroupMemberInfo struct {
	Id *uint64 `json:"id,optional"`
	// 用户ID
	UserId *uint64 `json:"userId,optional"`
	// 状态（0、退出 1、加入）
	Status *uint32 `json:"status,optional"`
	// 创建时间
	CreateTime *int64 `json:"createTime,optional"`
	// 最后修改时间
	UpdateTime *int64 `json:"updateTime,optional"`
}

// The response data of group member list | GroupMember列表数据
// swagger:model GroupMemberListResp
type GroupMemberListResp struct {
	BaseDataInfo
	// GroupMember list data | GroupMember列表数据
	Data GroupMemberListInfo `json:"data"`
}

// GroupMember list data | GroupMember列表数据
// swagger:model GroupMemberListInfo
type GroupMemberListInfo struct {
	BaseListInfo
	// The API list data | GroupMember列表数据
	Data []GroupMemberInfo `json:"data"`
}

// Get group member list request params | GroupMember列表请求参数
// swagger:model GroupMemberListReq
type GroupMemberListReq struct {
	PageInfo
}

// GroupMember information response | GroupMember信息返回体
// swagger:model GroupMemberInfoResp
type GroupMemberInfoResp struct {
	BaseDataInfo
	// GroupMember information | GroupMember数据
	Data GroupMemberInfo `json:"data"`
}

// The response data of group information | Group信息
// swagger:model GroupInfo
type GroupInfo struct {
	Id *uint64 `json:"id,optional"`
	// 圈子名称
	// min length : 1
	// max length : 30
	// required : true
	Name *string `json:"name,optional" validate:"min=1,max=30,required"`
	// 圈子描述
	// min length : 1
	// max length : 300
	// required : true
	Desc *string `json:"desc,optional" validate:"min=1,max=300,required"`
	// 圈子图标
	// min length : 1
	// required : true
	IconUrl *string `json:"iconUrl,optional" validate:"min=1,url,required"`
	// 圈子背景图
	// min length : 1
	// required : true
	CoverUrl *string `json:"coverUrl,optional" validate:"min=1,url,required"`
	// 用户ID
	UserId *uint64 `json:"userId,optional"`
	// 置顶标签
	TagIds []int64 `json:"tagIds,optional"`
	// 主题数
	TopicNum *int64 `json:"topicNum,optional"`
	// 关注数
	FollowNum *int64 `json:"followNum,optional"`
	// 创建时间
	CreateTime *int64 `json:"createTime,optional"`
	// 最后修改时间
	UpdateTime *int64 `json:"updateTime,optional"`
}

// The response data of group list | Group列表数据
// swagger:model GroupListResp
type GroupListResp struct {
	BaseDataInfo
	// Group list data | Group列表数据
	Data GroupListInfo `json:"data"`
}

// Group list data | Group列表数据
// swagger:model GroupListInfo
type GroupListInfo struct {
	BaseListInfo
	// The API list data | Group列表数据
	Data []GroupInfo `json:"data"`
}

// Get group list request params | Group列表请求参数
// swagger:model GroupListReq
type GroupListReq struct {
	PageInfo
	// Name
	Name *string `json:"name,optional"`
	// Desc
	Desc *string `json:"desc,optional"`
	// IconUrl
	IconUrl *string `json:"iconUrl,optional"`
}

// Group information response | Group信息返回体
// swagger:model GroupInfoResp
type GroupInfoResp struct {
	BaseDataInfo
	// Group information | Group数据
	Data GroupInfo `json:"data"`
}

// The response data of tag information | Tag信息
// swagger:model TagInfo
type TagInfo struct {
	Id *uint64 `json:"id,optional"`
	// 标签名称
	Name *string `json:"name,optional"`
	// 标签描述
	Desc *string `json:"desc,optional"`
	// 话题类型（1、普通 2、官方）
	Type *int32 `json:"type,optional"`
	// 圈子图标
	IconUrl *string `json:"iconUrl,optional"`
	// 圈子背景图
	CoverUrl *string `json:"coverUrl,optional"`
	// 主题数
	TopicNum *int64 `json:"topicNum,optional"`
	// 关注数
	FollowNum *int64 `json:"followNum,optional"`
	// 参与数
	MemberNum *int64 `json:"memberNum,optional"`
	// 创建时间
	CreateTime *int64 `json:"createTime,optional"`
	// 最后修改时间
	UpdateTime *int64 `json:"updateTime,optional"`
}

// The response data of tag list | Tag列表数据
// swagger:model TagListResp
type TagListResp struct {
	BaseDataInfo
	// Tag list data | Tag列表数据
	Data TagListInfo `json:"data"`
}

// Tag list data | Tag列表数据
// swagger:model TagListInfo
type TagListInfo struct {
	BaseListInfo
	// The API list data | Tag列表数据
	Data []TagInfo `json:"data"`
}

// Get tag list request params | Tag列表请求参数
// swagger:model TagListReq
type TagListReq struct {
	PageInfo
	// Name
	Name *string `json:"name,optional"`
	// Desc
	Desc *string `json:"desc,optional"`
	// IconUrl
	IconUrl *string `json:"iconUrl,optional"`
}

// Tag information response | Tag信息返回体
// swagger:model TagInfoResp
type TagInfoResp struct {
	BaseDataInfo
	// Tag information | Tag数据
	Data TagInfo `json:"data"`
}

// The response data of tag record information | TagRecord信息
// swagger:model TagRecordInfo
type TagRecordInfo struct {
	Id *uint64 `json:"id,optional"`
	// 话题ID
	TagId *uint64 `json:"tagId,optional"`
	// 帖子ID
	ArticleId *uint64 `json:"articleId,optional"`
	// 发帖时圈子ID
	GroupId *uint64 `json:"groupId,optional"`
	// 创建时间
	CreateTime *int64 `json:"createTime,optional"`
	// 最后修改时间
	UpdateTime *int64 `json:"updateTime,optional"`
}

// The response data of tag record list | TagRecord列表数据
// swagger:model TagRecordListResp
type TagRecordListResp struct {
	BaseDataInfo
	// TagRecord list data | TagRecord列表数据
	Data TagRecordListInfo `json:"data"`
}

// TagRecord list data | TagRecord列表数据
// swagger:model TagRecordListInfo
type TagRecordListInfo struct {
	BaseListInfo
	// The API list data | TagRecord列表数据
	Data []TagRecordInfo `json:"data"`
}

// Get tag record list request params | TagRecord列表请求参数
// swagger:model TagRecordListReq
type TagRecordListReq struct {
	PageInfo
	// 发帖时圈子ID
	GroupId *uint64 `json:"groupId,optional"`
	Name    *string `json:"name,optional"`
}

// TagRecord information response | TagRecord信息返回体
// swagger:model TagRecordInfoResp
type TagRecordInfoResp struct {
	BaseDataInfo
	// TagRecord information | TagRecord数据
	Data TagRecordInfo `json:"data"`
}

// The response data of product information | Product信息
// swagger:model ProductInfo
type ProductInfo struct {
	Id *uint64 `json:"id,optional"`
	// UserId
	UserId *uint64 `json:"userId,optional"`
	// Title
	Title *string `json:"title,optional"`
	// Sn
	Sn *string `json:"sn,optional"`
	// Merchant
	Merchant *string `json:"merchant,optional"`
	// Color
	Color *string `json:"color,optional"`
	// Language
	Language *string `json:"language,optional"`
	// RegisterTime
	RegisterTime *int64 `json:"registerTime,optional"`
	// CreateTime
	CreateTime *int64 `json:"createTime,optional"`
	// UpdateTime
	UpdateTime *int64 `json:"updateTime,optional"`
}

// The response data of product list | Product列表数据
// swagger:model ProductListResp
type ProductListResp struct {
	BaseDataInfo
	// Product list data | Product列表数据
	Data ProductListInfo `json:"data"`
}

// Product list data | Product列表数据
// swagger:model ProductListInfo
type ProductListInfo struct {
	BaseListInfo
	// The API list data | Product列表数据
	Data []ProductInfo `json:"data"`
}

// Get product list request params | Product列表请求参数
// swagger:model ProductListReq
type ProductListReq struct {
	PageInfo
	// Title
	Title *string `json:"title,optional"`
	// Sn
	Sn *string `json:"sn,optional"`
	// Merchant
	Merchant *string `json:"merchant,optional"`
}

// Product information response | Product信息返回体
// swagger:model ProductInfoResp
type ProductInfoResp struct {
	BaseDataInfo
	// Product information | Product数据
	Data ProductInfo `json:"data"`
}

// The response data of mainten info information | MaintenInfo信息
// swagger:model MaintenInfoInfo
type MaintenInfoInfo struct {
	Id *uint64 `json:"id,optional"`
	// CreateTime
	CreateTime *int64 `json:"createTime,optional"`
	// UpdateTime
	UpdateTime *int64 `json:"updateTime,optional"`
	// DeleteTime
	DeleteTime *int64 `json:"deleteTime,optional"`
	// 维修类型
	MalfuncType *int32 `json:"malfuncType,optional"`
	// 故障类型
	FaultType *int32 `json:"faultType,optional"`
	// 取件地址
	PickupAddress *string `json:"pickupAddress,optional"`
	// 回寄地址
	ReturnAddress *string `json:"returnAddress,optional"`
	// 故障描述
	MalfuncRemark *string `json:"malfuncRemark,optional"`
	// 类型 0:快递 1:邮寄
	ExpressType *int32 `json:"expressType,optional"`
	// 联系电话
	Mobile *string `json:"mobile,optional"`
	// sn号
	Sn *string `json:"sn,optional"`
	// 维修订单号
	OrderNo *string `json:"orderNo,optional"`
	// 下单用户ID
	UserId *uint64 `json:"userId,optional"`
	// 关联产品ID
	ProductId *uint64 `json:"productId,optional"`
}

// The response data of mainten info list | MaintenInfo列表数据
// swagger:model MaintenInfoListResp
type MaintenInfoListResp struct {
	BaseDataInfo
	// MaintenInfo list data | MaintenInfo列表数据
	Data MaintenInfoListInfo `json:"data"`
}

// MaintenInfo list data | MaintenInfo列表数据
// swagger:model MaintenInfoListInfo
type MaintenInfoListInfo struct {
	BaseListInfo
	// The API list data | MaintenInfo列表数据
	Data []MaintenInfoInfo `json:"data"`
}

// Get mainten info list request params | MaintenInfo列表请求参数
// swagger:model MaintenInfoListReq
type MaintenInfoListReq struct {
	PageInfo
	// PickupAddress
	PickupAddress *string `json:"pickupAddress,optional"`
	// ReturnAddress
	ReturnAddress *string `json:"returnAddress,optional"`
	// MalfuncRemark
	MalfuncRemark *string `json:"malfuncRemark,optional"`
	// 联系电话
	Mobile *string `json:"mobile,optional"`
	// sn号
	Sn *string `json:"sn,optional"`
	// 维修订单号
	OrderNo *string `json:"orderNo,optional"`
	// 下单用户ID
	UserId *uint64 `json:"userId,optional"`
}

// MaintenInfo information response | MaintenInfo信息返回体
// swagger:model MaintenInfoInfoResp
type MaintenInfoInfoResp struct {
	BaseDataInfo
	// MaintenInfo information | MaintenInfo数据
	Data MaintenInfoInfo `json:"data"`
}

// The response data of mainten order information | MaintenOrder信息
// swagger:model MaintenOrderInfo
type MaintenOrderInfo struct {
	Id *uint64 `json:"id,optional"`
	// CreateTime
	CreateTime *int64 `json:"createTime,optional"`
	// UpdateTime
	UpdateTime *int64 `json:"updateTime,optional"`
	// DeleteTime
	DeleteTime *int64 `json:"deleteTime,optional"`
	// DelState
	DelState *int32 `json:"delState,optional"`
	// 版本号
	Version *int64 `json:"version,optional"`
	// 订单号
	Sn *string `json:"sn,optional"`
	// 下单用户id
	UserId *uint64 `json:"userId,optional"`
	// 维修id
	MaintenId *uint64 `json:"maintenId,optional"`
	// 标题
	Title *string `json:"title,optional"`
	// 副标题
	SubTitle *string `json:"subTitle,optional"`
	// 商品图片
	Cover *string `json:"cover,optional"`
	// 商品介绍
	Info *string `json:"info,optional"`
	// 0:不需要邮费 1:需要参数
	NeedExpress *bool `json:"needExpress,optional"`
	// 邮费标准
	ExpressInfo *string `json:"expressInfo,optional"`
	// 邮费价格(分)
	ExpressPrice *int64 `json:"expressPrice,optional"`
	// 维修价格(分)
	MaintenPrice *int64 `json:"maintenPrice,optional"`
	// -1: 已取消 0:待支付 1:已付款 2:已完成 3:已退款 4:已过期
	TradeState *bool `json:"tradeState,optional"`
	// 确认码
	TradeCode *string `json:"tradeCode,optional"`
	// 用户下单备注
	Remark *string `json:"remark,optional"`
	// 订单总价格（邮费总价格+维修总价格）(分)
	OrderTotalPrice *int64 `json:"orderTotalPrice,optional"`
	// 邮费总价格(分)
	ExpressTotalPrice *int64 `json:"expressTotalPrice,optional"`
	// 维修总价格(分)
	MaintenTotalPrice *int64 `json:"maintenTotalPrice,optional"`
}

// The response data of mainten order list | MaintenOrder列表数据
// swagger:model MaintenOrderListResp
type MaintenOrderListResp struct {
	BaseDataInfo
	// MaintenOrder list data | MaintenOrder列表数据
	Data MaintenOrderListInfo `json:"data"`
}

// MaintenOrder list data | MaintenOrder列表数据
// swagger:model MaintenOrderListInfo
type MaintenOrderListInfo struct {
	BaseListInfo
	// The API list data | MaintenOrder列表数据
	Data []MaintenOrderInfo `json:"data"`
}

// Get mainten order list request params | MaintenOrder列表请求参数
// swagger:model MaintenOrderListReq
type MaintenOrderListReq struct {
	PageInfo
	// Sn
	Sn *string `json:"sn,optional"`
	// Title
	Title *string `json:"title,optional"`
	// SubTitle
	SubTitle *string `json:"subTitle,optional"`
}

// MaintenOrder information response | MaintenOrder信息返回体
// swagger:model MaintenOrderInfoResp
type MaintenOrderInfoResp struct {
	BaseDataInfo
	// MaintenOrder information | MaintenOrder数据
	Data MaintenOrderInfo `json:"data"`
}

// The response data of province information | Province信息
// swagger:model ProvinceInfo
type ProvinceInfo struct {
	Id *uint64 `json:"id,optional"`
	// Code
	Code *string `json:"code,optional"`
	// Name
	Name *string `json:"name,optional"`
	// Level
	Level *int32 `json:"level,optional"`
	// Geocode
	Geocode *string `json:"geocode,optional"`
	// Latitude
	Latitude *string `json:"latitude,optional"`
	// Longitude
	Longitude *string `json:"longitude,optional"`
	// Sort
	Sort *int32 `json:"sort,optional"`
	// ParentCode
	ParentCode *string `json:"parentCode,optional"`
}

// The response data of province list | Province列表数据
// swagger:model ProvinceListResp
type ProvinceListResp struct {
	BaseDataInfo
	// Province list data | Province列表数据
	Data ProvinceListInfo `json:"data"`
}

// Province list data | Province列表数据
// swagger:model ProvinceListInfo
type ProvinceListInfo struct {
	BaseListInfo
	// The API list data | Province列表数据
	Data []ProvinceInfo `json:"data"`
}

// Get province list request params | Province列表请求参数
// swagger:model ProvinceListReq
type ProvinceListReq struct {
	PageInfo
	// Code
	Code *string `json:"code,optional"`
	// Name
	Name *string `json:"name,optional"`
	// Geocode
	Geocode *string `json:"geocode,optional"`
	// Latitude
	Latitude *string `json:"latitude,optional"`
	// Longitude
	Longitude *string `json:"longitude,optional"`
	// ParentCode
	ParentCode *string `json:"parentCode,optional"`
}

// Province information response | Province信息返回体
// swagger:model ProvinceInfoResp
type ProvinceInfoResp struct {
	BaseDataInfo
	// Province information | Province数据
	Data ProvinceInfo `json:"data"`
}

// The response data of city information | City信息
// swagger:model CityInfo
type CityInfo struct {
	Id *uint64 `json:"id,optional"`
	// Code
	Code *string `json:"code,optional"`
	// Name
	Name *string `json:"name,optional"`
	// ProvinceCode
	ProvinceCode *string `json:"provinceCode,optional"`
	// Level
	Level *int32 `json:"level,optional"`
	// Geocode
	Geocode *string `json:"geocode,optional"`
	// Latitude
	Latitude *string `json:"latitude,optional"`
	// Longitude
	Longitude *string `json:"longitude,optional"`
	// Sort
	Sort *int32 `json:"sort,optional"`
	// ParentCode
	ParentCode *string `json:"parentCode,optional"`
}

// The response data of city list | City列表数据
// swagger:model CityListResp
type CityListResp struct {
	BaseDataInfo
	// City list data | City列表数据
	Data CityListInfo `json:"data"`
}

// City list data | City列表数据
// swagger:model CityListInfo
type CityListInfo struct {
	BaseListInfo
	// The API list data | City列表数据
	Data []CityInfo `json:"data"`
}

// Get city list request params | City列表请求参数
// swagger:model CityListReq
type CityListReq struct {
	PageInfo
	// Code
	Code *string `json:"code,optional"`
	// Name
	Name *string `json:"name,optional"`
	// ProvinceCode
	ProvinceCode *string `json:"provinceCode,optional"`
	// Geocode
	Geocode *string `json:"geocode,optional"`
	// Latitude
	Latitude *string `json:"latitude,optional"`
	// Longitude
	Longitude *string `json:"longitude,optional"`
	// ParentCode
	ParentCode *string `json:"parentCode,optional"`
}

// City information response | City信息返回体
// swagger:model CityInfoResp
type CityInfoResp struct {
	BaseDataInfo
	// City information | City数据
	Data CityInfo `json:"data"`
}

// The response data of area information | Area信息
// swagger:model AreaInfo
type AreaInfo struct {
	Id *uint64 `json:"id,optional"`
	// Code
	Code *string `json:"code,optional"`
	// Name
	Name *string `json:"name,optional"`
	// CityCode
	CityCode *string `json:"cityCode,optional"`
	// ProvinceCode
	ProvinceCode *string `json:"provinceCode,optional"`
	// Level
	Level *int32 `json:"level,optional"`
	// Geocode
	Geocode *string `json:"geocode,optional"`
	// Latitude
	Latitude *string `json:"latitude,optional"`
	// Sort
	Sort *int32 `json:"sort,optional"`
	// Longitude
	Longitude *string `json:"longitude,optional"`
	// ParentCode
	ParentCode *string `json:"parentCode,optional"`
}

// The response data of area list | Area列表数据
// swagger:model AreaListResp
type AreaListResp struct {
	BaseDataInfo
	// Area list data | Area列表数据
	Data AreaListInfo `json:"data"`
}

// Area list data | Area列表数据
// swagger:model AreaListInfo
type AreaListInfo struct {
	BaseListInfo
	// The API list data | Area列表数据
	Data []AreaInfo `json:"data"`
}

// Get area list request params | Area列表请求参数
// swagger:model AreaListReq
type AreaListReq struct {
	PageInfo
	// Code
	Code *string `json:"code,optional"`
	// Name
	Name *string `json:"name,optional"`
	// CityCode
	CityCode *string `json:"cityCode,optional"`
	// ProvinceCode
	ProvinceCode *string `json:"provinceCode,optional"`
	// Geocode
	Geocode *string `json:"geocode,optional"`
	// Latitude
	Latitude *string `json:"latitude,optional"`
	// Longitude
	Longitude *string `json:"longitude,optional"`
	// ParentCode
	ParentCode *string `json:"parentCode,optional"`
}

// Area information response | Area信息返回体
// swagger:model AreaInfoResp
type AreaInfoResp struct {
	BaseDataInfo
	// Area information | Area数据
	Data AreaInfo `json:"data"`
}

// The response data of street information | Street信息
// swagger:model StreetInfo
type StreetInfo struct {
	Id *uint64 `json:"id,optional"`
	// Code
	Code *string `json:"code,optional"`
	// Name
	Name *string `json:"name,optional"`
	// AreasCode
	AreasCode *string `json:"areasCode,optional"`
	// ProvinceCode
	ProvinceCode *string `json:"provinceCode,optional"`
	// Level
	Level *int32 `json:"level,optional"`
	// Geocode
	Geocode *string `json:"geocode,optional"`
	// Latitude
	Latitude *string `json:"latitude,optional"`
	// Sort
	Sort *int32 `json:"sort,optional"`
	// Longitude
	Longitude *string `json:"longitude,optional"`
	// ParentCode
	ParentCode *string `json:"parentCode,optional"`
}

// The response data of street list | Street列表数据
// swagger:model StreetListResp
type StreetListResp struct {
	BaseDataInfo
	// Street list data | Street列表数据
	Data StreetListInfo `json:"data"`
}

// Street list data | Street列表数据
// swagger:model StreetListInfo
type StreetListInfo struct {
	BaseListInfo
	// The API list data | Street列表数据
	Data []StreetInfo `json:"data"`
}

// Get street list request params | Street列表请求参数
// swagger:model StreetListReq
type StreetListReq struct {
	PageInfo
	// Code
	Code *string `json:"code,optional"`
	// Name
	Name *string `json:"name,optional"`
	// AreasCode
	AreasCode *string `json:"areasCode,optional"`
	// ProvinceCode
	ProvinceCode *string `json:"provinceCode,optional"`
	// Geocode
	Geocode *string `json:"geocode,optional"`
	// Latitude
	Latitude *string `json:"latitude,optional"`
	// Longitude
	Longitude *string `json:"longitude,optional"`
	// ParentCode
	ParentCode *string `json:"parentCode,optional"`
}

// Street information response | Street信息返回体
// swagger:model StreetInfoResp
type StreetInfoResp struct {
	BaseDataInfo
	// Street information | Street数据
	Data StreetInfo `json:"data"`
}

// The response data of user information | User信息
// swagger:model UserInfo
type UserInfo struct {
	Id *uint64 `json:"id,optional"`
	// CreateTime
	CreateTime *int64 `json:"createTime,optional"`
	// UpdateTime
	UpdateTime *int64 `json:"updateTime,optional"`
	// DeleteTime
	DeleteTime *int64 `json:"deleteTime,optional"`
	// 封禁结束时间
	ForbiddenEndTime *int64 `json:"forbiddenEndTime,optional"`
	// 禁言结束时间
	MutedEndTime *int64 `json:"mutedEndTime,optional"`
	// DelState
	DelState *int32 `json:"delState,optional"`
	// 版本号
	Version *int64 `json:"version,optional"`
	// Mobile
	Mobile *string `json:"mobile,optional"`
	// 邮箱
	Emall *string `json:"emall,optional"`
	// 实名认证状态 0:未认证 1:已认证
	RealNameVerified *int32 `json:"realNameVerified,optional"`
	// 真实姓名
	RealName *string `json:"realName,optional"`
	// 身份证号
	IdCard *string `json:"idCard,optional"`
	// Password
	Password *string `json:"password,optional"`
	// Nickname
	Nickname *string `json:"nickname,optional"`
	// 性别 0:男 1:女
	Sex *int32 `json:"sex,optional"`
	// Avatar
	Avatar *string `json:"avatar,optional"`
	// Info
	Info *string `json:"info,optional"`
	// BackgroundImage
	BackgroundImage *string `json:"backgroundImage,optional"`
	// HomePage
	HomePage *string `json:"homePage,optional"`
	// Description
	Description *string `json:"description,optional"`
	// Score
	Score *int64 `json:"score,optional"`
	// TopicCount
	TopicCount *int64 `json:"topicCount,optional"`
	// CommentCount
	CommentCount *int64 `json:"commentCount,optional"`
	// FollowCount
	FollowCount *int64 `json:"followCount,optional"`
	// FansCount
	FansCount *int64 `json:"fansCount,optional"`
	// LikeCount
	LikeCount *int64 `json:"likeCount,optional"`
	// Roles
	Roles *string `json:"roles,optional"`
}

// The response data of user list | User列表数据
// swagger:model UserListResp
type UserListResp struct {
	BaseDataInfo
	// User list data | User列表数据
	Data UserListInfo `json:"data"`
}

// User list data | User列表数据
// swagger:model UserListInfo
type UserListInfo struct {
	BaseListInfo
	// The API list data | User列表数据
	Data []UserInfo `json:"data"`
}

// Get user list request params | User列表请求参数
// swagger:model UserListReq
type UserListReq struct {
	PageInfo
	// Mobile
	Mobile *string `json:"mobile,optional"`
	// Password
	Password *string `json:"password,optional"`
	// Nickname
	Nickname *string `json:"nickname,optional"`
	// Consignee
	Consignee *string `json:"consignee,optional"`
	// Address
	Address *string `json:"address,optional"`
}

// User information response | User信息返回体
// swagger:model UserInfoResp
type UserInfoResp struct {
	BaseDataInfo
	// User information | User数据
	Data UserInfo `json:"data"`
}

// Update user forbidden time request | 更新用户封禁时间请求
// swagger:model UpdateUserForbiddenTimeReq
type UpdateUserForbiddenTimeReq struct {
	ID   *uint64 `json:"id"`
	Time *int64  `json:"time"`
}

// Update user muted time request | 更新用户禁言时间请求
// swagger:model UpdateUserMutedTimeReq
type UpdateUserMutedTimeReq struct {
	ID   *uint64 `json:"id"`
	Time *int64  `json:"time"`
}

// The response data of banner information | Banner信息
// swagger:model BannerInfo
type BannerInfo struct {
	Id *uint64 `json:"id,optional"`
	// 标题
	Title *string `json:"title,optional"`
	// 副标题
	SubTitle *string `json:"subTitle,optional"`
	// 轮播图地址
	CoverUrl *string `json:"coverUrl,optional"`
	// 跳转链接
	JumpLink *string `json:"jumpLink,optional"`
	// 所属模块（1、社区模块 2、服务模块）
	Module *int32 `json:"module,optional"`
	// 类型（1、文章 2、商品 3、外链）
	Type *int32 `json:"type,optional"`
	// 创建时间
	CreateTime *int64 `json:"createTime,optional"`
	// 更新时间
	UpdateTime *int64 `json:"updateTime,optional"`
	// 底色类型
	BaseColor *int64 `json:"baseColor,optional"`
	// 轮播图间隔时间
	Interval *int64 `json:"interval,optional"`
	// 轮播图持续时间
	Duration *int64 `json:"duration,optional"`
	// 排序
	Sort *int32 `json:"sort,optional"`
}

// The response data of banner list | Banner列表数据
// swagger:model BannerListResp
type BannerListResp struct {
	BaseDataInfo
	// Banner list data | Banner列表数据
	Data BannerListInfo `json:"data"`
}

// Banner list data | Banner列表数据
// swagger:model BannerListInfo
type BannerListInfo struct {
	BaseListInfo
	// The API list data | Banner列表数据
	Data []BannerInfo `json:"data"`
}

// Get banner list request params | Banner列表请求参数
// swagger:model BannerListReq
type BannerListReq struct {
	PageInfo
	// Title
	Title *string `json:"title,optional"`
	// SubTitle
	SubTitle *string `json:"subTitle,optional"`
	// CoverUrl
	CoverUrl *string `json:"coverUrl,optional"`
	// Module
	Module *int32 `json:"module,optional"`
}

// Banner information response | Banner信息返回体
// swagger:model BannerInfoResp
type BannerInfoResp struct {
	BaseDataInfo
	// Banner information | Banner数据
	Data BannerInfo `json:"data"`
}

// The response data of article information | Article信息
// swagger:model ArticleInfo
type ArticleInfo struct {
	Id *uint64 `json:"id,optional"`
	// 标题
	Title *string `json:"title,optional"`
	// 内容
	Content *string `json:"content,optional"`
	// 封面
	Cover *string `json:"cover,optional"`
	// 视频
	Videos *string `json:"videos,optional"`
	// 描述
	Description *string `json:"description,optional"`
	// 作者ID
	AuthorId *uint64 `json:"authorId,optional"`
	// 标签ID
	TagIds *string `json:"tagIds,optional"`
	// 圈子ID
	GroupId *int64 `json:"groupId,optional"`
	// 状态 0:待审核 1:审核不通过 2:可见 3:用户删除
	Status *uint32 `json:"status,optional"`
	// 类型 1、文章 2、动态
	Type *int32 `json:"type,optional"`
	// 置顶状态
	TopStatus *int32 `json:"topStatus,optional"`
	// 评论数
	CommentNum *int64 `json:"commentNum,optional"`
	// 点赞数
	LikeNum *int64 `json:"likeNum,optional"`
	// 收藏数
	CollectNum *int64 `json:"collectNum,optional"`
	// 浏览数
	ViewNum *int64 `json:"viewNum,optional"`
	// 分享数
	ShareNum *int64 `json:"shareNum,optional"`
	// 发布时间
	PublishTime *int64 `json:"publishTime,optional"`
	// 创建时间
	CreateTime *int64 `json:"createTime,optional"`
	// 最后修改时间
	UpdateTime *int64 `json:"updateTime,optional"`
	// 圈子信息
	Group *GroupInfo `json:"group,optional"`
	// 话题信息列表
	Tags []TagInfo `json:"tags,optional"`
	// 作者信息
	AuthorName *string `json:"authorName,optional"`
}

// The response data of article list | Article列表数据
// swagger:model ArticleListResp
type ArticleListResp struct {
	BaseDataInfo
	// Article list data | Article列表数据
	Data ArticleListInfo `json:"data"`
}

// Article list data | Article列表数据
// swagger:model ArticleListInfo
type ArticleListInfo struct {
	BaseListInfo
	// The API list data | Article列表数据
	Data []ArticleInfo `json:"data"`
}

// Get article list request params | Article列表请求参数
// swagger:model ArticleListReq
type ArticleListReq struct {
	PageInfo
	// Title
	Title *string `json:"title,optional"`
	// Content
	Content *string `json:"content,optional"`
	// Cover
	Cover *string `json:"cover,optional"`
	// Status
	Status *uint32 `json:"status,optional"`
	// TopStatus
	TopStatus *int32 `json:"topStatus,optional"`
}

// Article information response | Article信息返回体
// swagger:model ArticleInfoResp
type ArticleInfoResp struct {
	BaseDataInfo
	// Article information | Article数据
	Data ArticleInfo `json:"data"`
}

// The response data of segment information | Segment信息
// swagger:model SegmentInfo
type SegmentInfo struct {
	Id *uint64 `json:"id,optional"`
	// 栏目名称
	Name *string `json:"name,optional"`
	// 排序
	Sequence *int32 `json:"sequence,optional"`
	// 创建时间
	CreateTime *int64 `json:"createTime,optional"`
	// 更新时间
	UpdateTime *int64 `json:"updateTime,optional"`
}

// The response data of segment list | Segment列表数据
// swagger:model SegmentListResp
type SegmentListResp struct {
	BaseDataInfo
	// Segment list data | Segment列表数据
	Data SegmentListInfo `json:"data"`
}

// Segment list data | Segment列表数据
// swagger:model SegmentListInfo
type SegmentListInfo struct {
	BaseListInfo
	// The API list data | Segment列表数据
	Data []SegmentInfo `json:"data"`
}

// Get segment list request params | Segment列表请求参数
// swagger:model SegmentListReq
type SegmentListReq struct {
	PageInfo
	// Name
	Name *string `json:"name,optional"`
}

// Segment information response | Segment信息返回体
// swagger:model SegmentInfoResp
type SegmentInfoResp struct {
	BaseDataInfo
	// Segment information | Segment数据
	Data SegmentInfo `json:"data"`
}

// The response data of user addres information | UserAddres信息
// swagger:model UserAddresInfo
type UserAddresInfo struct {
	BaseIDInfo
	// 用户id
	UserId *int64 `json:"userId,optional"`
	// 收货人
	Consignee *string `json:"consignee,optional"`
	// 手机号码
	Mobile *string `json:"mobile,optional"`
	// 收货地址
	Address *string `json:"address,optional"`
	// 是否默认
	Default *int32 `json:"default,optional"`
	// 省
	Province *string `json:"province,optional"`
	// 市
	City *string `json:"city,optional"`
	// 县/区
	Area *string `json:"area,optional"`
	// 街道
	Street *string `json:"street,optional"`
	// 详细地址
	RecordAddress *string `json:"recordAddress,optional"`
}

// The response data of user addres list | UserAddres列表数据
// swagger:model UserAddresListResp
type UserAddresListResp struct {
	BaseDataInfo
	// UserAddres list data | UserAddres列表数据
	Data UserAddresListInfo `json:"data"`
}

// UserAddres list data | UserAddres列表数据
// swagger:model UserAddresListInfo
type UserAddresListInfo struct {
	BaseListInfo
	// The API list data | UserAddres列表数据
	Data []UserAddresInfo `json:"data"`
}

// Get user addres list request params | UserAddres列表请求参数
// swagger:model UserAddresListReq
type UserAddresListReq struct {
	PageInfo
	// Consignee
	Consignee *string `json:"consignee,optional"`
	// Mobile
	Mobile *string `json:"mobile,optional"`
	// Address
	Address *string `json:"address,optional"`
}

// UserAddres information response | UserAddres信息返回体
// swagger:model UserAddresInfoResp
type UserAddresInfoResp struct {
	BaseDataInfo
	// UserAddres information | UserAddres数据
	Data UserAddresInfo `json:"data"`
}

// The response data of segment article record information | SegmentArticleRecord信息
// swagger:model SegmentArticleRecordInfo
type SegmentArticleRecordInfo struct {
	Id *uint64 `json:"id,optional"`
	// 栏目ID
	SegmentId *uint64 `json:"segmentId,optional"`
	// 文章ID
	ArticleId *uint64 `json:"articleId,optional"`
	// 类型
	Type *int32 `json:"type,optional"`
	// 标题
	Title *string `json:"title,optional"`
	// 描述
	Description *string `json:"description,optional"`
	// 创建时间
	CreateTime *int64 `json:"createTime,optional"`
	// 更新时间
	UpdateTime *int64 `json:"updateTime,optional"`
}

// The response data of segment article record list | SegmentArticleRecord列表数据
// swagger:model SegmentArticleRecordListResp
type SegmentArticleRecordListResp struct {
	BaseDataInfo
	// SegmentArticleRecord list data | SegmentArticleRecord列表数据
	Data SegmentArticleRecordListInfo `json:"data"`
}

// SegmentArticleRecord list data | SegmentArticleRecord列表数据
// swagger:model SegmentArticleRecordListInfo
type SegmentArticleRecordListInfo struct {
	BaseListInfo
	// The API list data | SegmentArticleRecord列表数据
	Data []SegmentArticleRecordInfo `json:"data"`
}

// Get segment article record list request params | SegmentArticleRecord列表请求参数
// swagger:model SegmentArticleRecordListReq
type SegmentArticleRecordListReq struct {
	PageInfo
	// SegmentId
	SegmentId *uint64 `json:"segmentId,optional"`
	Type      *int32  `json:"type,optional"`
}

// SegmentArticleRecord information response | SegmentArticleRecord信息返回体
// swagger:model SegmentArticleRecordInfoResp
type SegmentArticleRecordInfoResp struct {
	BaseDataInfo
	// SegmentArticleRecord information | SegmentArticleRecord数据
	Data SegmentArticleRecordInfo `json:"data"`
}

// The response data of reply information | Reply信息
// swagger:model ReplyInfo
type ReplyInfo struct {
	Id *uint64 `json:"id,optional"`
	// 业务ID
	BizId *string `json:"bizId,optional"`
	// 评论目标id
	TargetId *uint64 `json:"targetId,optional"`
	// 评论用户ID
	ReplyUserId *uint64 `json:"replyUserId,optional"`
	// 被回复用户ID
	BeReplyUserId *uint64 `json:"beReplyUserId,optional"`
	// 父评论ID
	ParentId *uint64 `json:"parentId,optional"`
	// 内容
	Content *string `json:"content,optional"`
	// 状态 0:正常 1:删除
	Status *uint32 `json:"status,optional"`
	// 点赞数
	LikeNum *int64 `json:"likeNum,optional"`
	// 创建时间
	CreateTime *int64 `json:"createTime,optional"`
	// 最后修改时间
	UpdateTime *int64 `json:"updateTime,optional"`
}

// The response data of reply list | Reply列表数据
// swagger:model ReplyListResp
type ReplyListResp struct {
	BaseDataInfo
	// Reply list data | Reply列表数据
	Data ReplyListInfo `json:"data"`
}

// Reply list data | Reply列表数据
// swagger:model ReplyListInfo
type ReplyListInfo struct {
	BaseListInfo
	// The API list data | Reply列表数据
	Data []ReplyInfo `json:"data"`
}

// Get reply list request params | Reply列表请求参数
// swagger:model ReplyListReq
type ReplyListReq struct {
	PageInfo
	// BizId
	BizId *string `json:"bizId,optional"`
	// Content
	Content *string `json:"content,optional"`
	// 评论用户ID
	ReplyUserId *uint64 `json:"replyUserId,optional"`
	// 被回复用户ID
	BeReplyUserId *uint64 `json:"beReplyUserId,optional"`
	// 状态 0:正常 1:删除
	Status *uint32 `json:"status,optional"`
}

// Reply information response | Reply信息返回体
// swagger:model ReplyInfoResp
type ReplyInfoResp struct {
	BaseDataInfo
	// Reply information | Reply数据
	Data ReplyInfo `json:"data"`
}

// The response data of app version information | AppVersion信息
// swagger:model AppVersionInfo
type AppVersionInfo struct {
	BaseIDInfo
	// 版本更新内容 支持<br>自动换行
	Describe *string `json:"describe,optional"`
	// 状态
	Status *uint32 `json:"status,optional"`
	// 版本名称 manifest里的版本名称
	EditionName *string `json:"editionName,optional"`
	// 本号 最重要的manifest里的版本号 （检查更新主要以服务器返回的edition_number版本号是否大于当前app的版本号来实现是否更新）
	EditionNumber *int32 `json:"editionNumber,optional"`
	// apk、wgt包下载地址或者应用市场地址  安卓应用市场 market://details?id=xxxx 苹果store itms-apps://itunes.apple.com/cn/app/xxxxxx
	EditionUrl []string `json:"editionUrl,optional"`
	// 是否强制更新 0代表否 1代表是
	EditionForce *int32 `json:"editionForce,optional"`
	// 0是整包升级（apk或者appstore或者安卓应用市场） 1是wgt升级
	PackageType *int32 `json:"packageType,optional"`
	// 是否发行  0否 1是 为了控制上架应用市场审核时不能弹出热更新框
	EditionIssue *int32 `json:"editionIssue,optional"`
	// 是否静默更新 0代表否 1代表是
	EditionSilence *int32 `json:"editionSilence,optional"`
	Platform       *int32 `json:"platform,optional"`
	ChannelId      *int32 `json:"channelId,optional"`
	// uni-app应用标识
	AppId *string `json:"appId,optional"`
}

// The response data of app version list | AppVersion列表数据
// swagger:model AppVersionListResp
type AppVersionListResp struct {
	BaseDataInfo
	// AppVersion list data | AppVersion列表数据
	Data AppVersionListInfo `json:"data"`
}

// AppVersion list data | AppVersion列表数据
// swagger:model AppVersionListInfo
type AppVersionListInfo struct {
	BaseListInfo
	// The API list data | AppVersion列表数据
	Data []AppVersionInfo `json:"data"`
}

// Get app version list request params | AppVersion列表请求参数
// swagger:model AppVersionListReq
type AppVersionListReq struct {
	PageInfo
	// Describe
	Describe *string `json:"describe,optional"`
	// EditionName
	EditionName *string `json:"editionName,optional"`
	// EditionUrl
	EditionUrl *string `json:"editionUrl,optional"`
	// uni-app应用标识
	AppId *uint64 `json:"appId,optional"`
}

// AppVersion information response | AppVersion信息返回体
// swagger:model AppVersionInfoResp
type AppVersionInfoResp struct {
	BaseDataInfo
	// AppVersion information | AppVersion数据
	Data AppVersionInfo `json:"data"`
}

// The response data of approle information | Approle信息
// swagger:model ApproleInfo
type ApproleInfo struct {
	BaseIDInfo
	// Translated Name | 展示名称
	Trans string `json:"trans,optional"`
	// Status | 状态
	// max : 20
	Status *uint32 `json:"status,optional" validate:"omitempty,lt=20"`
	// Name | 角色名称
	// max length : 30
	Name *string `json:"name,optional" validate:"omitempty,max=30"`
	// Role code | 角色码
	// max length : 20
	Code *string `json:"code,optional" validate:"omitempty,max=20"`
	// DefaultRouter | 默认首页
	// max length : 80
	DefaultRouter *string `json:"defaultRouter,optional" validate:"omitempty,max=80"`
	// Remark | 备注
	// max length : 200
	Remark *string `json:"remark,optional" validate:"omitempty,max=200"`
	// Sort | 排序
	// max : 10000
	Sort *uint32 `json:"sort,optional" validate:"omitempty,lt=10000"`
}

// The response data of approle list | Approle信息列表数据
// swagger:model ApproleListResp
type ApproleListResp struct {
	BaseDataInfo
	// Role list data | 角色列表数据
	Data ApproleListInfo `json:"data"`
}

// The approle list data | Approle信息列表数据
// swagger:model ApproleListInfo
type ApproleListInfo struct {
	BaseListInfo
	// The approle list data | Approle信息列表数据
	Data []ApproleInfo `json:"data"`
}

// Get approle list request params | Approle列表请求参数
// swagger:model ApproleListReq
type ApproleListReq struct {
	PageInfo
	// Name | 角色名称
	Name *string `json:"name,optional"`
}

// The approle information response | Approle信息返回体
// swagger:model ApproleInfoResp
type ApproleInfoResp struct {
	BaseDataInfo
	// The approle information | Approle信息数据
	Data ApproleInfo `json:"data"`
}

// The response data of appapi information | Appapi信息
// swagger:model AppapiInfo
type AppapiInfo struct {
	BaseIDInfo
	// Translated Name | 多语言名称
	Trans string `json:"trans,optional"`
	// API path | API路径
	// min length : 1
	// max length : 80
	Path *string `json:"path,optional" validate:"omitempty,min=1,max=80"`
	// API Description | API 描述
	// max length : 100
	Description *string `json:"description,optional" validate:"omitempty,max=100"`
	// API group | API分组
	// min length : 1
	// max length : 80
	Group *string `json:"group,optional" validate:"omitempty,min=1,max=80"`
	// API request method e.g. POST | API请求类型 如POST
	// min length : 3
	// max length : 7
	Method *string `json:"method,optional" validate:"omitempty,uppercase,min=3,max=7"`
	// Whether is required | 是否是必须的 api
	IsRequired *bool `json:"isRequired,optional"`
	// Service name | 服务名称
	ServiceName *string `json:"serviceName,optional"`
}

// The response data of appapi list | Appapi信息列表数据
// swagger:model AppapiListResp
type AppapiListResp struct {
	BaseDataInfo
	// Appapi list data | Appapi信息列表数据
	Data AppapiListInfo `json:"data"`
}

// The appapi list data | Appapi信息列表数据
// swagger:model AppapiListInfo
type AppapiListInfo struct {
	BaseListInfo
	// The appapi list data | Appapi信息列表数据
	Data []AppapiInfo `json:"data"`
}

// Get appapi list request params | Appapi列表请求参数
// swagger:model AppapiListReq
type AppapiListReq struct {
	PageInfo
	// API path | API路径
	// max length : 200
	Path *string `json:"path,optional" validate:"omitempty,max=200"`
	// API Description | API 描述
	// max length : 100
	Description *string `json:"description,optional" validate:"omitempty,max=100"`
	// API group | API分组
	// max length : 80
	Group *string `json:"group,optional" validate:"omitempty,max=80"`
	// API request method e.g. POST | API请求类型 如POST
	// min length : 3
	// max length : 7
	Method *string `json:"method,optional" validate:"omitempty,uppercase,min=3,max=7"`
	// Whether is required | 是否是必须的 api
	IsRequired *bool `json:"isRequired,optional"`
	// Service name | 服务名称
	ServiceName *string `json:"serviceName,optional"`
}

// The appapi information response | Appapi信息返回体
// swagger:model AppapiInfoResp
type AppapiInfoResp struct {
	BaseDataInfo
	// The appapi information | Appapi信息数据
	Data AppapiInfo `json:"data"`
}

// The response data of api authorization | API授权数据
// swagger:model ApiAuthorityInfo
type ApiAuthorityInfo struct {
	// API path | API 路径
	// required : true
	// max length : 80
	Path string `json:"path" validate="required,max=80"`
	// API method | API请求方法
	// required : true
	// min length : 3
	// max length : 4
	Method string `json:"method" validate="required,min=3,max=4"`
}

// Create or update api authorization information request | 创建或更新API授权信息
// swagger:model CreateOrUpdateApiAuthorityReq
type CreateOrUpdateApiAuthorityReq struct {
	// Role ID | 角色ID
	// required : true
	// max : 1000
	RoleId uint64 `json:"roleId" validate:"required,lt=1000"`
	// API authorization list | API授权列表数据
	// Required: true
	Data []ApiAuthorityInfo `json:"data"`
}

// The response data of api authorization list | API授权列表返回数据
// swagger:model ApiAuthorityListResp
type ApiAuthorityListResp struct {
	BaseDataInfo
	// The api authorization list data | API授权列表数据
	Data ApiAuthorityListInfo `json:"data"`
}

// The  data of api authorization list | API授权列表数据
// swagger:model ApiAuthorityListInfo
type ApiAuthorityListInfo struct {
	BaseListInfo
	// The api authorization list data | API授权列表数据
	Data []ApiAuthorityInfo `json:"data"`
}

// Create or update menu authorization information request params | 创建或更新菜单授权信息参数
// swagger:model MenuAuthorityInfoReq
type MenuAuthorityInfoReq struct {
	// role ID | 角色ID
	// required : true
	// max : 1000
	RoleId uint64 `json:"roleId" validate:"required,lt=1000"`
}

// Menu authorization response data | 菜单授权信息数据
// swagger:model MenuAuthorityInfoResp
type MenuAuthorityInfoResp struct {
	BaseDataInfo
	// The menu authorization data | 菜单授权信息数据
	Data MenuAuthorityInfoReq `json:"data"`
}

// The response data of mall order information | MallOrder信息
// swagger:model MallOrderInfo
type MallOrderInfo struct {
	BaseIDInfo
	// 订单主题
	Subject *string `json:"subject,optional"`
	// 订单编号
	OrderNo *string `json:"orderNo,optional"`
	// 用户ID
	UserId *uint64 `json:"userId,optional"`
	// 订单类型:1实物订单,2虚拟订单
	OrderType *int32 `json:"orderType,optional"`
	// 订单状态:1待支付,2待发货,3待收货,4已完成,5已取消,6已退款
	Status *uint32 `json:"status,optional"`
	// 订单总金额(分)
	TotalAmount *int64 `json:"totalAmount,optional"`
	// 实付金额(分)
	PayAmount *int64 `json:"payAmount,optional"`
	// 币种
	Currency *string `json:"currency,optional"`
	// 支付时间
	PayTime *int64 `json:"payTime,optional"`
	// 支付渠道
	PayChannel *int32 `json:"payChannel,optional"`
	// 支付渠道订单号
	PayChannelOrderNo *string `json:"payChannelOrderNo,optional"`
}

// The response data of mall order list | MallOrder信息列表数据
// swagger:model MallOrderListResp
type MallOrderListResp struct {
	BaseDataInfo
	// MallOrder list data | MallOrder信息列表数据
	Data MallOrderListInfo `json:"data"`
}

// The mall order list data | MallOrder信息列表数据
// swagger:model MallOrderListInfo
type MallOrderListInfo struct {
	BaseListInfo
	// The mall order list data | MallOrder信息列表数据
	Data []MallOrderInfo `json:"data"`
}

// Get mall order list request params | MallOrder列表请求参数
// swagger:model MallOrderListReq
type MallOrderListReq struct {
	PageInfo
	// ProductName
	ProductName *string `json:"productName,optional"`
	// SkuName
	SkuName *string `json:"skuName,optional"`
	// QuotaType
	QuotaType *string `json:"quotaType,optional"`
	// OrderNo
	OrderNo *string `json:"orderNo,optional"`
	// Currency
	Currency *string `json:"currency,optional"`
	// Remark
	Remark *string `json:"remark,optional"`
	// OperatorName
	OperatorName *string `json:"operatorName,optional"`
	// Status
	Status *uint32 `json:"status,optional"`
	// TotalAmount
	TotalAmount *int64 `json:"totalAmount,optional"`
	// PayAmount
	PayAmount *int64 `json:"payAmount,optional"`
	// PayTime
	PayTime *int64 `json:"payTime,optional"`
	// UserId
	UserId *uint64 `json:"userId,optional"`
	// OrderType
	OrderType *int32 `json:"orderType,optional"`
	// PayChannel
	PayChannel *int32 `json:"payChannel,optional"`
	// PayChannelOrderNo
	PayChannelOrderNo *string `json:"payChannelOrderNo,optional"`
}

// The mall order information response | MallOrder信息返回体
// swagger:model MallOrderInfoResp
type MallOrderInfoResp struct {
	BaseDataInfo
	// The mall order information | MallOrder信息数据
	Data MallOrderInfo `json:"data"`
}

// The response data of mall order item information | MallOrderItem信息
// swagger:model MallOrderItemInfo
type MallOrderItemInfo struct {
	BaseIDInfo
	// 订单ID
	OrderId *uint64 `json:"orderId,optional"`
	// 商品ID
	ProductId *uint64 `json:"productId,optional"`
	// SKU ID
	SkuId *uint64 `json:"skuId,optional"`
	// 商品名称
	ProductName *string `json:"productName,optional"`
	// SKU名称
	SkuName *string `json:"skuName,optional"`
	// 数量
	Quantity *int64 `json:"quantity,optional"`
	// 单价(分)
	Price *int64 `json:"price,optional"`
	// 总金额(分)
	TotalAmount *int64 `json:"totalAmount,optional"`
	// 配额类型: 1 api_calls-API调用次数, 2 tokens-Token数量
	QuotaType *int32 `json:"quotaType,optional"`
	// 购买的配额数量
	QuotaAmount *int64 `json:"quotaAmount,optional"`
	// 单价(分/次或分/token)
	UnitPrice *int64 `json:"unitPrice,optional"`
	// 订单商品编号
	ItemNo *string `json:"itemNo,optional"`
}

// The response data of mall order item list | MallOrderItem信息列表数据
// swagger:model MallOrderItemListResp
type MallOrderItemListResp struct {
	BaseDataInfo
	// MallOrderItem list data | MallOrderItem信息列表数据
	Data MallOrderItemListInfo `json:"data"`
}

// The mall order item list data | MallOrderItem信息列表数据
// swagger:model MallOrderItemListInfo
type MallOrderItemListInfo struct {
	BaseListInfo
	// The mall order item list data | MallOrderItem信息列表数据
	Data []MallOrderItemInfo `json:"data"`
}

// Get mall order item list request params | MallOrderItem列表请求参数
// swagger:model MallOrderItemListReq
type MallOrderItemListReq struct {
	PageInfo
	// ProductName
	ProductName *string `json:"productName,optional"`
	// SkuName
	SkuName *string `json:"skuName,optional"`
	// OrderId
	OrderId *uint64 `json:"orderId,optional"`
	// ProductId
	ProductId *uint64 `json:"productId,optional"`
	// SkuId
	SkuId *uint64 `json:"skuId,optional"`
	// Quantity
	Quantity *int64 `json:"quantity,optional"`
	// Price
	Price *int64 `json:"price,optional"`
	// TotalAmount
	TotalAmount *int64 `json:"totalAmount,optional"`
	// QuotaType
	QuotaType *int32 `json:"quotaType,optional"`
	// QuotaAmount
	QuotaAmount *int64 `json:"quotaAmount,optional"`
	// UnitPrice
	UnitPrice *int64 `json:"unitPrice,optional"`
	// ItemNo
	ItemNo *string `json:"itemNo,optional"`
}

// The mall order item information response | MallOrderItem信息返回体
// swagger:model MallOrderItemInfoResp
type MallOrderItemInfoResp struct {
	BaseDataInfo
	// The mall order item information | MallOrderItem信息数据
	Data MallOrderItemInfo `json:"data"`
}

// The response data of mall order log information | MallOrderLog信息
// swagger:model MallOrderLogInfo
type MallOrderLogInfo struct {
	BaseIDInfo
	// 订单ID
	OrderId *uint64 `json:"orderId,optional"`
	// 订单编号
	OrderNo *string `json:"orderNo,optional"`
	// 前状态
	FromStatus *int32 `json:"fromStatus,optional"`
	// 后状态
	ToStatus *int32 `json:"toStatus,optional"`
	// 备注
	Remark *string `json:"remark,optional"`
	// 操作人ID
	OperatorId *uint64 `json:"operatorId,optional"`
	// 操作人名称
	OperatorName *string `json:"operatorName,optional"`
}

// The response data of mall order log list | MallOrderLog信息列表数据
// swagger:model MallOrderLogListResp
type MallOrderLogListResp struct {
	BaseDataInfo
	// MallOrderLog list data | MallOrderLog信息列表数据
	Data MallOrderLogListInfo `json:"data"`
}

// The mall order log list data | MallOrderLog信息列表数据
// swagger:model MallOrderLogListInfo
type MallOrderLogListInfo struct {
	BaseListInfo
	// The mall order log list data | MallOrderLog信息列表数据
	Data []MallOrderLogInfo `json:"data"`
}

// Get mall order log list request params | MallOrderLog列表请求参数
// swagger:model MallOrderLogListReq
type MallOrderLogListReq struct {
	PageInfo
	// OrderNo
	OrderNo *string `json:"orderNo,optional"`
	// Remark
	Remark *string `json:"remark,optional"`
	// OperatorName
	OperatorName *string `json:"operatorName,optional"`
	// OrderId
	OrderId *uint64 `json:"orderId,optional"`
	// FromStatus
	FromStatus *int32 `json:"fromStatus,optional"`
	// ToStatus
	ToStatus *int32 `json:"toStatus,optional"`
	// OperatorId
	OperatorId *uint64 `json:"operatorId,optional"`
}

// The mall order log information response | MallOrderLog信息返回体
// swagger:model MallOrderLogInfoResp
type MallOrderLogInfoResp struct {
	BaseDataInfo
	// The mall order log information | MallOrderLog信息数据
	Data MallOrderLogInfo `json:"data"`
}

// The response data of mall product information | MallProduct信息
// swagger:model MallProductInfo
type MallProductInfo struct {
	BaseIDInfo
	ProductCode *string `json:"productCode,optional"`
	// 商品名称
	Name *string `json:"name,optional"`
	// 商品简介
	Brief *string `json:"brief,optional"`
	// 商品详细描述
	Description *string `json:"description,optional"`
	// 商品封面图
	CoverImage *string `json:"coverImage,optional"`
	// 商品图片列表
	Images []*ProductImage `json:"images,optional"`
	// 商品分类ID
	CategoryId *uint64 `json:"categoryId,optional"`
	// 商品分类名称
	CategoryName *string `json:"categoryName,optional"`
	// 商品类型:1实物商品,2虚拟商品
	Type *int32 `json:"type,optional"`
	// 总库存(所有SKU库存之和)
	TotalStock *int64 `json:"totalStock,optional"`
	// 币种
	Currency *string `json:"currency,optional"`
	// 状态:0下架,1上架,2售罄
	Status *uint32 `json:"status,optional"`
	// 规格类型:1单规格 2多规格
	SpecType *int32 `json:"specType,optional"`
	// 供应商ID
	SupplierId *uint64 `json:"supplierId,optional"`
	// 供应商分成比例(%)
	SupplierCommissionRate *int64 `json:"supplierCommissionRate,optional"`
	// 分成规则:0根据供应商表计算,1根据单个商品计算
	CommissionRule *int32 `json:"commissionRule,optional"`
	// 开启分成 0、禁用 1、开启
	CommissionStatus *int32 `json:"commissionStatus,optional"`
	// 应用ID
	ApplicationId *uint64 `json:"applicationId,optional"`
}

// Product images | 商品图片
type ProductImage struct {
	// 图片地址
	Url *string `json:"url,optional"`
	// 图片描述
	Description *string `json:"description,optional"`
}

// The response data of mall product list | MallProduct信息列表数据
// swagger:model MallProductListResp
type MallProductListResp struct {
	BaseDataInfo
	// MallProduct list data | MallProduct信息列表数据
	Data MallProductListInfo `json:"data"`
}

// The mall product list data | MallProduct信息列表数据
// swagger:model MallProductListInfo
type MallProductListInfo struct {
	BaseListInfo
	// The mall product list data | MallProduct信息列表数据
	Data []MallProductInfo `json:"data"`
}

// Get mall product list request params | MallProduct列表请求参数
// swagger:model MallProductListReq
type MallProductListReq struct {
	PageInfo
	// ProductCode
	ProductCode *string `json:"productCode,optional"`
	// Name
	Name *string `json:"name,optional"`
	// Brief
	Brief *string `json:"brief,optional"`
	// Description
	Description *string `json:"description,optional"`
	// CoverImage
	CoverImage *string `json:"coverImage,optional"`
	// Currency
	Currency *string `json:"currency,optional"`
	// QuotaType
	QuotaType *string `json:"quotaType,optional"`
	// Attributes
	Attributes *string `json:"attributes,optional"`
	// SkuCode
	SkuCode *string `json:"skuCode,optional"`
	// CategoryId
	CategoryId *uint64 `json:"categoryId,optional"`
	// Type
	Type *int32 `json:"type,optional"`
	// TotalStock
	TotalStock *int64 `json:"totalStock,optional"`
	// Status
	Status *uint32 `json:"status,optional"`
	// SpecType
	SpecType *int32 `json:"specType,optional"`
}

// The mall product information response | MallProduct信息返回体
// swagger:model MallProductInfoResp
type MallProductInfoResp struct {
	BaseDataInfo
	// The mall product information | MallProduct信息数据
	Data MallProductInfo `json:"data"`
}

// The response data of mall product category information | MallProductCategory信息
// swagger:model MallProductCategoryInfo
type MallProductCategoryInfo struct {
	BaseIDInfo
	// 分类名称
	Name *string `json:"name,optional"`
	// 父分类ID
	ParentId *uint64 `json:"parentId,optional"`
	// 分类层级
	Level *int32 `json:"level,optional"`
	// 排序
	Sort *int32 `json:"sort,optional"`
	// 状态:0禁用,1启用
	Status *uint32 `json:"status,optional"`
	// 子分类
	Children []*MallProductCategoryInfo `json:"children,optional"`
}

// The response data of mall product category list | MallProductCategory信息列表数据
// swagger:model MallProductCategoryListResp
type MallProductCategoryListResp struct {
	BaseDataInfo
	// MallProductCategory list data | MallProductCategory信息列表数据
	Data MallProductCategoryListInfo `json:"data"`
}

// The mall product category list data | MallProductCategory信息列表数据
// swagger:model MallProductCategoryListInfo
type MallProductCategoryListInfo struct {
	BaseListInfo
	// The mall product category list data | MallProductCategory信息列表数据
	Data []MallProductCategoryInfo `json:"data"`
}

// Get mall product category list request params | MallProductCategory列表请求参数
// swagger:model MallProductCategoryListReq
type MallProductCategoryListReq struct {
	PageInfo
	// 分类名称
	Name *string `json:"name,optional"`
	// 父分类ID
	ParentId *uint64 `json:"parentId,optional"`
	// 分类层级
	Level *int32 `json:"level,optional"`
	// 排序
	Sort *int32 `json:"sort,optional"`
	// 状态:0禁用,1启用
	Status *uint32 `json:"status,optional"`
}

// The mall product category information response | MallProductCategory信息返回体
// swagger:model MallProductCategoryInfoResp
type MallProductCategoryInfoResp struct {
	BaseDataInfo
	// The mall product category information | MallProductCategory信息数据
	Data MallProductCategoryInfo `json:"data"`
}

// 获取分类树请求
// swagger:model GetMallProductCategoryTreeReq
type GetMallProductCategoryTreeReq struct {
}

// 获取分类树响应
// swagger:model GetMallProductCategoryTreeResp
type GetMallProductCategoryTreeResp struct {
	Code int32                      `json:"code"`
	Msg  string                     `json:"msg"`
	Data []*MallProductCategoryInfo `json:"data"`
}

// The response data of mall product quota information | MallProductQuota信息
// swagger:model MallProductQuotaInfo
type MallProductQuotaInfo struct {
	BaseIDInfo
	// 商品ID
	ProductId *uint64 `json:"productId,optional"`
	// 配额类型
	QuotaType *int32 `json:"quotaType,optional"`
	// 配额值
	QuotaValue *int64 `json:"quotaValue,optional"`
	// 单位价格(分)
	UnitPrice *int64 `json:"unitPrice,optional"`
	// 最小购买量
	MinPurchase *int64 `json:"minPurchase,optional"`
	// 最大购买量
	MaxPurchase *int64 `json:"maxPurchase,optional"`
}

// The response data of mall product quota list | MallProductQuota信息列表数据
// swagger:model MallProductQuotaListResp
type MallProductQuotaListResp struct {
	BaseDataInfo
	// MallProductQuota list data | MallProductQuota信息列表数据
	Data MallProductQuotaListInfo `json:"data"`
}

// The mall product quota list data | MallProductQuota信息列表数据
// swagger:model MallProductQuotaListInfo
type MallProductQuotaListInfo struct {
	BaseListInfo
	// The mall product quota list data | MallProductQuota信息列表数据
	Data []MallProductQuotaInfo `json:"data"`
}

// Get mall product quota list request params | MallProductQuota列表请求参数
// swagger:model MallProductQuotaListReq
type MallProductQuotaListReq struct {
	PageInfo
	// Page
	Page uint64 `json:"page,"`
	// PageSize
	PageSize uint64 `json:"pageSize,"`
	// ProductId
	ProductId *uint64 `json:"productId,optional"`
	// QuotaType
	QuotaType *int32 `json:"quotaType,optional"`
	// QuotaValue
	QuotaValue *int64 `json:"quotaValue,optional"`
	// UnitPrice
	UnitPrice *int64 `json:"unitPrice,optional"`
	// MinPurchase
	MinPurchase *int64 `json:"minPurchase,optional"`
	// MaxPurchase
	MaxPurchase *int64 `json:"maxPurchase,optional"`
}

// The mall product quota information response | MallProductQuota信息返回体
// swagger:model MallProductQuotaInfoResp
type MallProductQuotaInfoResp struct {
	BaseDataInfo
	// The mall product quota information | MallProductQuota信息数据
	Data MallProductQuotaInfo `json:"data"`
}

// The response data of mall product sku information | MallProductSku信息
// swagger:model MallProductSkuInfo
type MallProductSkuInfo struct {
	BaseIDInfo
	// 商品ID
	ProductId *uint64 `json:"productId,optional"`
	// 规格属性
	Attributes *string `json:"attributes,optional"`
	// 原价(分)
	OriginalPrice *int64 `json:"originalPrice,optional"`
	// 售价(分)
	SellingPrice *int64 `json:"sellingPrice,optional"`
	// 库存
	Stock *int64 `json:"stock,optional"`
	// SKU编码
	SkuCode *string `json:"skuCode,optional"`
	// 状态:0禁用,1启用
	Status *uint32 `json:"status,optional"`
	// 长度(cm)
	Length *int32 `json:"length,optional"`
	// 宽度(cm)
	Width *int32 `json:"width,optional"`
	// 高度(cm)
	Height *int32 `json:"height,optional"`
	// 重量(g)
	Weight *int32 `json:"weight,optional"`
	// 用量
	UsageCount *int64 `json:"usageCount,optional"`
	// 商品类型
	ProductType *int32 `json:"productType,optional"`
	// 名称
	Name *string `json:"name,optional"`
	// 是否特价:0否,1是
	IsOnSpecial *int32 `json:"isOnSpecial,optional"`
	// 特价
	SpecialPrice *int64 `json:"specialPrice,optional"`
	// 特价类型:0无,1新用户特价,2新设备特价,3限时特价
	SpecialSaleType *int32 `json:"specialSaleType,optional"`
	// 限购数量(单个用户/设备)
	SpecialSaleLimit *int64 `json:"specialSaleLimit,optional"`
	// 特价开始时间
	SpecialPriceStartTime *int64 `json:"specialPriceStartTime,optional"`
	// 特价结束时间
	SpecialPriceEndTime *int64 `json:"specialPriceEndTime,optional"`
	// 图片
	Image *string `json:"image,optional"`
	// 特价商品卖完恢复原价:0、不恢复 1、恢复
	SellOutRestoreOrigin *int32 `json:"sellOutRestoreOrigin,optional"`
}

// The response data of mall product sku list | MallProductSku信息列表数据
// swagger:model MallProductSkuListResp
type MallProductSkuListResp struct {
	BaseDataInfo
	// MallProductSku list data | MallProductSku信息列表数据
	Data MallProductSkuListInfo `json:"data"`
}

// The mall product sku list data | MallProductSku信息列表数据
// swagger:model MallProductSkuListInfo
type MallProductSkuListInfo struct {
	BaseListInfo
	// The mall product sku list data | MallProductSku信息列表数据
	Data []MallProductSkuInfo `json:"data"`
}

// Get mall product sku list request params | MallProductSku列表请求参数
// swagger:model MallProductSkuListReq
type MallProductSkuListReq struct {
	PageInfo
	// Attributes
	Attributes *string `json:"attributes,optional"`
	// SkuCode
	SkuCode *string `json:"skuCode,optional"`
	// ProductId
	ProductId *uint64 `json:"productId,optional"`
	// OriginalPrice
	OriginalPrice *int64 `json:"originalPrice,optional"`
	// SellingPrice
	SellingPrice *int64 `json:"sellingPrice,optional"`
	// Stock
	Stock *int64 `json:"stock,optional"`
	// Status
	Status *uint32 `json:"status,optional"`
	// Length
	Length *int32 `json:"length,optional"`
	// Width
	Width *int32 `json:"width,optional"`
	// Height
	Height *int32 `json:"height,optional"`
	// Weight
	Weight *int32 `json:"weight,optional"`
	// Name
	Name *string `json:"name,optional"`
	// 是否特价:0否,1是
	IsOnSpecial *int32 `json:"isOnSpecial,optional"`
	// 特价商品卖完恢复原价:0、不恢复 1、恢复
	SellOutRestoreOrigin *int32 `json:"sellOutRestoreOrigin,optional"`
}

// The mall product sku information response | MallProductSku信息返回体
// swagger:model MallProductSkuInfoResp
type MallProductSkuInfoResp struct {
	BaseDataInfo
	// The mall product sku information | MallProductSku信息数据
	Data MallProductSkuInfo `json:"data"`
}

// The mall product sku batch create request | MallProductSku批量创建请求参数
// swagger:model MallProductSkuBatchCreateReq
type MallProductSkuBatchCreateReq struct {
	// The mall product sku batch create request | MallProductSku批量创建请求参数
	Data []MallProductSkuInfo `json:"data"`
}

// The batch process mall product sku request | 批量处理SKU请求
// swagger:model MallProductSkuBatchProcessReq
type MallProductSkuBatchProcessReq struct {
	// ProductId | 商品ID
	ProductId *uint64 `json:"productId"`
	// Create or update SKU list | 新增或更新的SKU列表
	CreateOrUpdateList []MallProductSkuInfo `json:"createOrUpdateList,optional"`
	// Delete SKU ids | 要删除的SKU ID列表
	DeleteIds []uint64 `json:"deleteIds,optional"`
}

// The response data of mall subscription information | MallSubscription信息
// swagger:model MallSubscriptionInfo
type MallSubscriptionInfo struct {
	BaseIDInfo
	// 用户ID
	UserId *uint64 `json:"userId,optional"`
	// 商品ID
	ProductId *uint64 `json:"productId,optional"`
	// 订单ID
	OrderId *uint64 `json:"orderId,optional"`
	// 开始时间
	StartTime *int64 `json:"startTime,optional"`
	// 结束时间
	EndTime *int64 `json:"endTime,optional"`
	// 状态:1生效中,2已过期,3已取消
	Status *uint32 `json:"status,optional"`
	// 是否自动续费
	AutoRenew *int32 `json:"autoRenew,optional"`
}

// The response data of mall subscription list | MallSubscription信息列表数据
// swagger:model MallSubscriptionListResp
type MallSubscriptionListResp struct {
	BaseDataInfo
	// MallSubscription list data | MallSubscription信息列表数据
	Data MallSubscriptionListInfo `json:"data"`
}

// The mall subscription list data | MallSubscription信息列表数据
// swagger:model MallSubscriptionListInfo
type MallSubscriptionListInfo struct {
	BaseListInfo
	// The mall subscription list data | MallSubscription信息列表数据
	Data []MallSubscriptionInfo `json:"data"`
}

// Get mall subscription list request params | MallSubscription列表请求参数
// swagger:model MallSubscriptionListReq
type MallSubscriptionListReq struct {
	PageInfo
	// UserId
	UserId *uint64 `json:"userId,optional"`
	// ProductId
	ProductId *uint64 `json:"productId,optional"`
	// OrderId
	OrderId *uint64 `json:"orderId,optional"`
	// StartTime
	StartTime *int64 `json:"startTime,optional"`
	// EndTime
	EndTime *int64 `json:"endTime,optional"`
	// Status
	Status *uint32 `json:"status,optional"`
	// AutoRenew
	AutoRenew *int32 `json:"autoRenew,optional"`
}

// The mall subscription information response | MallSubscription信息返回体
// swagger:model MallSubscriptionInfoResp
type MallSubscriptionInfoResp struct {
	BaseDataInfo
	// The mall subscription information | MallSubscription信息数据
	Data MallSubscriptionInfo `json:"data"`
}

// The response data of mall usage record information | MallUsageRecord信息
// swagger:model MallUsageRecordInfo
type MallUsageRecordInfo struct {
	BaseIDInfo
	// 用户ID
	UserId *uint64 `json:"userId,optional"`
	// 商品ID
	ProductId *uint64 `json:"productId,optional"`
	// 订单ID
	OrderId *uint64 `json:"orderId,optional"`
	// 配额类型
	QuotaType *int32 `json:"quotaType,optional"`
	// 已使用数量
	UsedAmount *int64 `json:"usedAmount,optional"`
	// 剩余数量
	RemainingAmount *int64 `json:"remainingAmount,optional"`
	// 请求ID
	RequestId *string `json:"requestId,optional"`
	// 使用详情
	UsageDetail *string `json:"usageDetail,optional"`
}

// The response data of mall usage record list | MallUsageRecord信息列表数据
// swagger:model MallUsageRecordListResp
type MallUsageRecordListResp struct {
	BaseDataInfo
	// MallUsageRecord list data | MallUsageRecord信息列表数据
	Data MallUsageRecordListInfo `json:"data"`
}

// The mall usage record list data | MallUsageRecord信息列表数据
// swagger:model MallUsageRecordListInfo
type MallUsageRecordListInfo struct {
	BaseListInfo
	// The mall usage record list data | MallUsageRecord信息列表数据
	Data []MallUsageRecordInfo `json:"data"`
}

// Get mall usage record list request params | MallUsageRecord列表请求参数
// swagger:model MallUsageRecordListReq
type MallUsageRecordListReq struct {
	PageInfo
	// Page
	Page uint64 `json:"page,"`
	// PageSize
	PageSize uint64 `json:"pageSize,"`
	// UserId
	UserId *uint64 `json:"userId,optional"`
	// ProductId
	ProductId *uint64 `json:"productId,optional"`
	// OrderId
	OrderId *uint64 `json:"orderId,optional"`
	// QuotaType
	QuotaType *int32 `json:"quotaType,optional"`
	// UsedAmount
	UsedAmount *int64 `json:"usedAmount,optional"`
	// RemainingAmount
	RemainingAmount *int64 `json:"remainingAmount,optional"`
	// RequestId
	RequestId *string `json:"requestId,optional"`
	// UsageDetail
	UsageDetail *string `json:"usageDetail,optional"`
}

// The mall usage record information response | MallUsageRecord信息返回体
// swagger:model MallUsageRecordInfoResp
type MallUsageRecordInfoResp struct {
	BaseDataInfo
	// The mall usage record information | MallUsageRecord信息数据
	Data MallUsageRecordInfo `json:"data"`
}

// The response data of mall refund express information | MallRefundExpress信息
// swagger:model MallRefundExpressInfo
type MallRefundExpressInfo struct {
	BaseIDInfo
	// 退款ID
	RefundId *uint64 `json:"refundId,optional"`
	// 退款单号
	RefundNo *string `json:"refundNo,optional"`
	// 快递公司
	ExpressCompany *string `json:"expressCompany,optional"`
	// 快递单号
	ExpressNo *string `json:"expressNo,optional"`
	// 寄件人姓名
	SenderName *string `json:"senderName,optional"`
	// 寄件人电话
	SenderMobile *string `json:"senderMobile,optional"`
	// 寄件地址
	SenderAddress *string `json:"senderAddress,optional"`
}

// The response data of mall refund express list | MallRefundExpress信息列表数据
// swagger:model MallRefundExpressListResp
type MallRefundExpressListResp struct {
	BaseDataInfo
	// MallRefundExpress list data | MallRefundExpress信息列表数据
	Data MallRefundExpressListInfo `json:"data"`
}

// The mall refund express list data | MallRefundExpress信息列表数据
// swagger:model MallRefundExpressListInfo
type MallRefundExpressListInfo struct {
	BaseListInfo
	// The mall refund express list data | MallRefundExpress信息列表数据
	Data []MallRefundExpressInfo `json:"data"`
}

// Get mall refund express list request params | MallRefundExpress列表请求参数
// swagger:model MallRefundExpressListReq
type MallRefundExpressListReq struct {
	PageInfo
	// RefundId
	RefundId *uint64 `json:"refundId,optional"`
	// RefundNo
	RefundNo *string `json:"refundNo,optional"`
	// ExpressCompany
	ExpressCompany *string `json:"expressCompany,optional"`
	// ExpressNo
	ExpressNo *string `json:"expressNo,optional"`
	// SenderName
	SenderName *string `json:"senderName,optional"`
	// SenderMobile
	SenderMobile *string `json:"senderMobile,optional"`
	// SenderAddress
	SenderAddress *string `json:"senderAddress,optional"`
	// CreatedAt
	CreatedAt *int64 `json:"createdAt,optional"`
	// UpdatedAt
	UpdatedAt *int64 `json:"updatedAt,optional"`
}

// The mall refund express information response | MallRefundExpress信息返回体
// swagger:model MallRefundExpressInfoResp
type MallRefundExpressInfoResp struct {
	BaseDataInfo
	// The mall refund express information | MallRefundExpress信息数据
	Data MallRefundExpressInfo `json:"data"`
}

// The response data of mall delivery information | MallDelivery信息
// swagger:model MallDeliveryInfo
type MallDeliveryInfo struct {
	BaseIDInfo
	// 订单ID
	OrderId *uint64 `json:"orderId,optional"`
	// 订单编号
	OrderNo *string `json:"orderNo,optional"`
	// 快递公司
	ExpressCompany *string `json:"expressCompany,optional"`
	// 快递单号
	ExpressNo *string `json:"expressNo,optional"`
	// 收件人姓名
	ReceiverName *string `json:"receiverName,optional"`
	// 收件人电话
	ReceiverMobile *string `json:"receiverMobile,optional"`
	// 省份
	ReceiverProvince *string `json:"receiverProvince,optional"`
	// 城市
	ReceiverCity *string `json:"receiverCity,optional"`
	// 区县
	ReceiverDistrict *string `json:"receiverDistrict,optional"`
	// 详细地址
	ReceiverAddress *string `json:"receiverAddress,optional"`
	// 发货时间
	DeliveryTime *int64 `json:"deliveryTime,optional"`
	// 签收时间
	SignTime *int64 `json:"signTime,optional"`
	// 状态:0待发货,1已发货,2已签收,3已退回
	Status *uint32 `json:"status,optional"`
	// 备注
	Remark *string `json:"remark,optional"`
}

// The response data of mall delivery list | MallDelivery信息列表数据
// swagger:model MallDeliveryListResp
type MallDeliveryListResp struct {
	BaseDataInfo
	// MallDelivery list data | MallDelivery信息列表数据
	Data MallDeliveryListInfo `json:"data"`
}

// The mall delivery list data | MallDelivery信息列表数据
// swagger:model MallDeliveryListInfo
type MallDeliveryListInfo struct {
	BaseListInfo
	// The mall delivery list data | MallDelivery信息列表数据
	Data []MallDeliveryInfo `json:"data"`
}

// Get mall delivery list request params | MallDelivery列表请求参数
// swagger:model MallDeliveryListReq
type MallDeliveryListReq struct {
	PageInfo
	// OrderId
	OrderId *uint64 `json:"orderId,optional"`
	// OrderNo
	OrderNo *string `json:"orderNo,optional"`
	// ExpressCompany
	ExpressCompany *string `json:"expressCompany,optional"`
	// ExpressNo
	ExpressNo *string `json:"expressNo,optional"`
	// ReceiverName
	ReceiverName *string `json:"receiverName,optional"`
	// ReceiverMobile
	ReceiverMobile *string `json:"receiverMobile,optional"`
	// ReceiverProvince
	ReceiverProvince *string `json:"receiverProvince,optional"`
	// ReceiverCity
	ReceiverCity *string `json:"receiverCity,optional"`
	// ReceiverDistrict
	ReceiverDistrict *string `json:"receiverDistrict,optional"`
	// ReceiverAddress
	ReceiverAddress *string `json:"receiverAddress,optional"`
	// DeliveryTime
	DeliveryTime *int64 `json:"deliveryTime,optional"`
	// SignTime
	SignTime *int64 `json:"signTime,optional"`
	// Status
	Status *uint32 `json:"status,optional"`
	// Remark
	Remark *string `json:"remark,optional"`
}

// The mall delivery information response | MallDelivery信息返回体
// swagger:model MallDeliveryInfoResp
type MallDeliveryInfoResp struct {
	BaseDataInfo
	// The mall delivery information | MallDelivery信息数据
	Data MallDeliveryInfo `json:"data"`
}

// The response data of mall payment information | MallPayment信息
// swagger:model MallPaymentInfo
type MallPaymentInfo struct {
	Id *uint64 `json:"id,optional"`
}

// The response data of mall payment list | MallPayment信息列表数据
// swagger:model MallPaymentListResp
type MallPaymentListResp struct {
	BaseDataInfo
	// MallPayment list data | MallPayment信息列表数据
	Data MallPaymentListInfo `json:"data"`
}

// The mall payment list data | MallPayment信息列表数据
// swagger:model MallPaymentListInfo
type MallPaymentListInfo struct {
	BaseListInfo
	// The mall payment list data | MallPayment信息列表数据
	Data []MallPaymentInfo `json:"data"`
}

// Get mall payment list request params | MallPayment列表请求参数
// swagger:model MallPaymentListReq
type MallPaymentListReq struct {
	PageInfo
	// OrderId
	OrderId *uint64 `json:"orderId,optional"`
	// OrderNo
	OrderNo *string `json:"orderNo,optional"`
	// PayChannel
	PayChannel *int32 `json:"payChannel,optional"`
	// PayAmount
	PayAmount *int64 `json:"payAmount,optional"`
	// PayStatus
	PayStatus *int32 `json:"payStatus,optional"`
	// ChannelOrderNo
	ChannelOrderNo *string `json:"channelOrderNo,optional"`
	// ChannelUserId
	ChannelUserId *string `json:"channelUserId,optional"`
	// ChannelExtra
	ChannelExtra *string `json:"channelExtra,optional"`
	// ErrorCode
	ErrorCode *string `json:"errorCode,optional"`
	// ErrorMsg
	ErrorMsg *string `json:"errorMsg,optional"`
	// NotifyTime
	NotifyTime *int64 `json:"notifyTime,optional"`
	// CreatedAt
	CreatedAt *int64 `json:"createdAt,optional"`
	// UpdatedAt
	UpdatedAt *int64 `json:"updatedAt,optional"`
}

// The mall payment information response | MallPayment信息返回体
// swagger:model MallPaymentInfoResp
type MallPaymentInfoResp struct {
	BaseDataInfo
	// The mall payment information | MallPayment信息数据
	Data MallPaymentInfo `json:"data"`
}

// The response data of mall payment log information | MallPaymentLog信息
// swagger:model MallPaymentLogInfo
type MallPaymentLogInfo struct {
	BaseIDInfo
	// 订单ID
	OrderId *uint64 `json:"orderId,optional"`
	// 订单编号
	OrderNo *string `json:"orderNo,optional"`
	// 支付渠道:1-微信支付,2-支付宝,3-余额支付
	PayChannel *int32 `json:"payChannel,optional"`
	// 支付金额(分)
	PayAmount *int64 `json:"payAmount,optional"`
	// 支付状态:0-待支付,1-支付中,2-支付成功,3-支付失败
	PayStatus *int32 `json:"payStatus,optional"`
	// 渠道订单号
	ChannelOrderNo *string `json:"channelOrderNo,optional"`
	// 渠道用户标识
	ChannelUserId *string `json:"channelUserId,optional"`
	// 渠道额外参数JSON
	ChannelExtra *string `json:"channelExtra,optional"`
	// 错误码
	ErrorCode *string `json:"errorCode,optional"`
	// 错误信息
	ErrorMsg *string `json:"errorMsg,optional"`
	// 通知时间
	NotifyTime *int64 `json:"notifyTime,optional"`
}

// The response data of mall payment log list | MallPaymentLog信息列表数据
// swagger:model MallPaymentLogListResp
type MallPaymentLogListResp struct {
	BaseDataInfo
	// MallPaymentLog list data | MallPaymentLog信息列表数据
	Data MallPaymentLogListInfo `json:"data"`
}

// The mall payment log list data | MallPaymentLog信息列表数据
// swagger:model MallPaymentLogListInfo
type MallPaymentLogListInfo struct {
	BaseListInfo
	// The mall payment log list data | MallPaymentLog信息列表数据
	Data []MallPaymentLogInfo `json:"data"`
}

// Get mall payment log list request params | MallPaymentLog列表请求参数
// swagger:model MallPaymentLogListReq
type MallPaymentLogListReq struct {
	PageInfo
	// OrderId
	OrderId *uint64 `json:"orderId,optional"`
	// OrderNo
	OrderNo *string `json:"orderNo,optional"`
	// PayChannel
	PayChannel *int32 `json:"payChannel,optional"`
	// PayAmount
	PayAmount *int64 `json:"payAmount,optional"`
	// PayStatus
	PayStatus *int32 `json:"payStatus,optional"`
	// ChannelOrderNo
	ChannelOrderNo *string `json:"channelOrderNo,optional"`
	// ChannelUserId
	ChannelUserId *string `json:"channelUserId,optional"`
	// ChannelExtra
	ChannelExtra *string `json:"channelExtra,optional"`
	// ErrorCode
	ErrorCode *string `json:"errorCode,optional"`
	// ErrorMsg
	ErrorMsg *string `json:"errorMsg,optional"`
	// NotifyTime
	NotifyTime *int64 `json:"notifyTime,optional"`
	// CreatedAt
	CreatedAt *int64 `json:"createdAt,optional"`
	// UpdatedAt
	UpdatedAt *int64 `json:"updatedAt,optional"`
}

// The mall payment log information response | MallPaymentLog信息返回体
// swagger:model MallPaymentLogInfoResp
type MallPaymentLogInfoResp struct {
	BaseDataInfo
	// The mall payment log information | MallPaymentLog信息数据
	Data MallPaymentLogInfo `json:"data"`
}

// The response data of mall refund information | MallRefund信息
// swagger:model MallRefundInfo
type MallRefundInfo struct {
	BaseIDInfo
	// 退款单号
	RefundNo *string `json:"refundNo,optional"`
	// 订单ID
	OrderId *uint64 `json:"orderId,optional"`
	// 订单编号
	OrderNo *string `json:"orderNo,optional"`
	// 用户ID
	UserId *uint64 `json:"userId,optional"`
	// 退款类型:1仅退款,2退货退款
	RefundType *int32 `json:"refundType,optional"`
	// 退款原因
	RefundReason *string `json:"refundReason,optional"`
	// 退款金额(分)
	RefundAmount *int64 `json:"refundAmount,optional"`
	// 状态:0待审核,1已同意,2已拒绝,3已退款,4已取消
	Status *uint32 `json:"status,optional"`
	// 审核人ID
	AuditUserId *uint64 `json:"auditUserId,optional"`
	// 审核人名称
	AuditUserName *string `json:"auditUserName,optional"`
	// 审核时间
	AuditTime *int64 `json:"auditTime,optional"`
	// 审核备注
	AuditRemark *string `json:"auditRemark,optional"`
	// 退款时间
	RefundTime *int64 `json:"refundTime,optional"`
	// 退款凭证
	Evidence *string `json:"evidence,optional"`
	// 备注
	Remark *string `json:"remark,optional"`
}

// The response data of mall refund list | MallRefund信息列表数据
// swagger:model MallRefundListResp
type MallRefundListResp struct {
	BaseDataInfo
	// MallRefund list data | MallRefund信息列表数据
	Data MallRefundListInfo `json:"data"`
}

// The mall refund list data | MallRefund信息列表数据
// swagger:model MallRefundListInfo
type MallRefundListInfo struct {
	BaseListInfo
	// The mall refund list data | MallRefund信息列表数据
	Data []MallRefundInfo `json:"data"`
}

// Get mall refund list request params | MallRefund列表请求参数
// swagger:model MallRefundListReq
type MallRefundListReq struct {
	PageInfo
	// RefundId
	RefundId *uint64 `json:"refundId,optional"`
	// RefundNo
	RefundNo *string `json:"refundNo,optional"`
	// ExpressCompany
	ExpressCompany *string `json:"expressCompany,optional"`
	// ExpressNo
	ExpressNo *string `json:"expressNo,optional"`
	// SenderName
	SenderName *string `json:"senderName,optional"`
	// SenderMobile
	SenderMobile *string `json:"senderMobile,optional"`
	// SenderAddress
	SenderAddress *string `json:"senderAddress,optional"`
	// CreatedAt
	CreatedAt *int64 `json:"createdAt,optional"`
	// UpdatedAt
	UpdatedAt *int64 `json:"updatedAt,optional"`
	// OrderId
	OrderId *uint64 `json:"orderId,optional"`
	// OrderNo
	OrderNo *string `json:"orderNo,optional"`
	// UserId
	UserId *uint64 `json:"userId,optional"`
	// RefundType
	RefundType *int32 `json:"refundType,optional"`
	// RefundReason
	RefundReason *string `json:"refundReason,optional"`
	// RefundAmount
	RefundAmount *int64 `json:"refundAmount,optional"`
	// Status
	Status *uint32 `json:"status,optional"`
	// AuditUserId
	AuditUserId *uint64 `json:"auditUserId,optional"`
	// AuditUserName
	AuditUserName *string `json:"auditUserName,optional"`
	// AuditTime
	AuditTime *int64 `json:"auditTime,optional"`
	// AuditRemark
	AuditRemark *string `json:"auditRemark,optional"`
	// RefundTime
	RefundTime *int64 `json:"refundTime,optional"`
	// Evidence
	Evidence *string `json:"evidence,optional"`
	// Remark
	Remark *string `json:"remark,optional"`
}

// The mall refund information response | MallRefund信息返回体
// swagger:model MallRefundInfoResp
type MallRefundInfoResp struct {
	BaseDataInfo
	// The mall refund information | MallRefund信息数据
	Data MallRefundInfo `json:"data"`
}

// The response data of mall license information | 许可证信息
// swagger:model MallLicenseInfo
type MallLicenseInfo struct {
	BaseIDInfo
	// 关联订单号
	OrderNo *string `json:"orderNo,optional"`
	// License密钥
	LicenseKey *string `json:"licenseKey,optional"`
	// 用户ID
	UserId *uint64 `json:"userId,optional"`
	// SKU ID
	SkuId *uint64 `json:"skuId,optional"`
	// 激活状态: 0-未激活, 1-已激活
	ActivationStatus *int32 `json:"activationStatus,optional"`
	// 激活时间
	ActivationTime *int64 `json:"activationTime,optional"`
	// 最大设备数
	MaxDevices *int32 `json:"maxDevices,optional"`
	// 关联订单ID
	OrderId *uint64 `json:"orderId,optional"`
	// 产品ID
	ProductId *uint64 `json:"productId,optional"`
	// SKU名称
	SkuName *string `json:"skuName,optional"`
}

// The response data of mall license list | 许可证信息列表数据
// swagger:model MallLicenseListResp
type MallLicenseListResp struct {
	BaseDataInfo
	// MallLicense list data | 许可证信息列表数据
	Data MallLicenseListInfo `json:"data"`
}

// The mall license list data | 许可证信息列表数据
// swagger:model MallLicenseListInfo
type MallLicenseListInfo struct {
	BaseListInfo
	// The mall license list data | 许可证信息列表数据
	Data []MallLicenseInfo `json:"data"`
}

// Get mall license list request params | 许可证列表请求参数
// swagger:model MallLicenseListReq
type MallLicenseListReq struct {
	PageInfo
	// LicenseId
	LicenseId *uint64 `json:"licenseId,optional"`
	// DeviceType
	DeviceType *string `json:"deviceType,optional"`
	// DeviceId
	DeviceId *string `json:"deviceId,optional"`
	// DeviceName
	DeviceName *string `json:"deviceName,optional"`
	// OsType
	OsType *string `json:"osType,optional"`
	// OsVersion
	OsVersion *string `json:"osVersion,optional"`
	// BrowserType
	BrowserType *string `json:"browserType,optional"`
	// BrowserVersion
	BrowserVersion *string `json:"browserVersion,optional"`
	// IpAddress
	IpAddress *string `json:"ipAddress,optional"`
	// LastActiveTime
	LastActiveTime *int64 `json:"lastActiveTime,optional"`
	// Status
	Status *uint32 `json:"status,optional"`
	// OrderNo
	OrderNo *string `json:"orderNo,optional"`
	// LicenseKey
	LicenseKey *string `json:"licenseKey,optional"`
	// UserId
	UserId *uint64 `json:"userId,optional"`
	// SkuId
	SkuId *uint64 `json:"skuId,optional"`
	// ActivationStatus
	ActivationStatus *int32 `json:"activationStatus,optional"`
	// ActivationTime
	ActivationTime *int64 `json:"activationTime,optional"`
	// MaxDevices
	MaxDevices *int32 `json:"maxDevices,optional"`
	// OrderId
	OrderId *uint64 `json:"orderId,optional"`
	// ProductId
	ProductId *uint64 `json:"productId,optional"`
	// CreatedAt
	CreatedAt *int64 `json:"createdAt,optional"`
	// UpdatedAt
	UpdatedAt *int64 `json:"updatedAt,optional"`
	// DeletedAt
	DeletedAt *int64 `json:"deletedAt,optional"`
}

// The mall license information response | 许可证信息返回体
// swagger:model MallLicenseInfoResp
type MallLicenseInfoResp struct {
	BaseDataInfo
	// The mall license information | 许可证信息数据
	Data MallLicenseInfo `json:"data"`
}

// The request data of batch create mall license | 批量创建许可证请求数据
// swagger:model BatchCreateMallLicenseReq
type BatchCreateMallLicenseReq struct {
	// ProductId
	ProductId *uint64 `json:"productId,optional"`
	// SkuId
	SkuId *uint64 `json:"skuId,optional"`
	// Count
	Count *uint64 `json:"count,optional"`
}

// The response data of mall license device information | 许可证设备信息
// swagger:model MallLicenseDeviceInfo
type MallLicenseDeviceInfo struct {
	BaseIDInfo
	// 关联License ID
	LicenseId *uint64 `json:"licenseId,optional"`
	// 设备类型:PC,Mobile,Tablet等
	DeviceType *string `json:"deviceType,optional"`
	// 设备唯一标识
	DeviceId *string `json:"deviceId,optional"`
	// 设备名称
	DeviceName *string `json:"deviceName,optional"`
	// 操作系统类型
	OsType *string `json:"osType,optional"`
	// 操作系统版本
	OsVersion *string `json:"osVersion,optional"`
	// 浏览器类型
	BrowserType *string `json:"browserType,optional"`
	// 浏览器版本
	BrowserVersion *string `json:"browserVersion,optional"`
	// IP地址
	IpAddress *string `json:"ipAddress,optional"`
	// 最后活跃时间
	LastActiveTime *int64 `json:"lastActiveTime,optional"`
	// 状态:0禁用,1启用
	Status *uint32 `json:"status,optional"`
	// 过期时间
	ExpirationTime *int64 `json:"expirationTime,optional"`
	// 关联产品ID
	ProductId *uint64 `json:"productId,optional"`
	// 关联产品SKU ID
	ProductSkuId *uint64 `json:"productSkuId,optional"`
	// 关联应用ID
	ApplicationId *uint64 `json:"applicationId,optional"`
	// 初始持续天数
	InitialDurationDays *int64 `json:"initialDurationDays,optional"`
	// 增加的持续天数
	AddedDurationDays *int64 `json:"addedDurationDays,optional"`
	// 总持续天数
	TotalDurationDays *int64 `json:"totalDurationDays,optional"`
	// 首次激活时间
	FirstActivationTime *int64 `json:"firstActivationTime,optional"`
	// 过期日期
	ExpirationDate *int64 `json:"expirationDate,optional"`
	// 绑定状态
	BindStatus *int32 `json:"bindStatus,optional"`
	// 设备码
	MachineCode *string `json:"machineCode,optional"`
}

// The response data of mall license device list | 许可证设备信息列表数据
// swagger:model MallLicenseDeviceListResp
type MallLicenseDeviceListResp struct {
	BaseDataInfo
	// MallLicenseDevice list data | 许可证设备信息列表数据
	Data MallLicenseDeviceListInfo `json:"data"`
}

// The mall license device list data | 许可证设备信息列表数据
// swagger:model MallLicenseDeviceListInfo
type MallLicenseDeviceListInfo struct {
	BaseListInfo
	// The mall license device list data | 许可证设备信息列表数据
	Data []MallLicenseDeviceInfo `json:"data"`
}

// Get mall license device list request params | 许可证设备列表请求参数
// swagger:model MallLicenseDeviceListReq
type MallLicenseDeviceListReq struct {
	PageInfo
	// LicenseId
	LicenseId *uint64 `json:"licenseId,optional"`
	// MachineCode
	MachineCode *string `json:"machineCode,optional"`
	// DeviceType
	DeviceType *string `json:"deviceType,optional"`
	// DeviceId
	DeviceId *string `json:"deviceId,optional"`
	// DeviceName
	DeviceName *string `json:"deviceName,optional"`
	// OsType
	OsType *string `json:"osType,optional"`
	// OsVersion
	OsVersion *string `json:"osVersion,optional"`
	// BrowserType
	BrowserType *string `json:"browserType,optional"`
	// BrowserVersion
	BrowserVersion *string `json:"browserVersion,optional"`
	// IpAddress
	IpAddress *string `json:"ipAddress,optional"`
	// LastActiveTime
	LastActiveTime *int64 `json:"lastActiveTime,optional"`
	// Status
	Status *uint32 `json:"status,optional"`
	// CreatedAt
	CreatedAt *int64 `json:"createdAt,optional"`
	// UpdatedAt
	UpdatedAt *int64 `json:"updatedAt,optional"`
	// ProductId
	ProductId *uint64 `json:"productId,optional"`
	// ProductSkuId
	ProductSkuId *uint64 `json:"productSkuId,optional"`
}

// The mall license device information response | 许可证设备信息返回体
// swagger:model MallLicenseDeviceInfoResp
type MallLicenseDeviceInfoResp struct {
	BaseDataInfo
	// The mall license device information | 许可证设备信息数据
	Data MallLicenseDeviceInfo `json:"data"`
}

// Unbind mall license device request params | 解绑许可证设备请求参数
// swagger:model UnbindMallLicenseDeviceReq
type UnbindMallLicenseDeviceReq struct {
	// DeviceId
	DeviceId *uint64 `json:"deviceId,optional"`
	// NotifyType
	NotifyType *string `json:"notifyType,optional" required:"typeof=1 2"`
	// Receiver
	Receiver *string `json:"receiver,optional"`
}

// The response data of mall supplier information | 供应商信息
// swagger:model MallSupplierInfo
type MallSupplierInfo struct {
	BaseIDInfo
	// 供应商名称
	Name *string `json:"name,optional"`
	// 供应商编码
	Code *string `json:"code,optional"`
	// 联系人姓名
	ContactName *string `json:"contactName,optional"`
	// 联系人电话
	ContactPhone *string `json:"contactPhone,optional"`
	// 联系人邮箱
	ContactEmail *string `json:"contactEmail,optional"`
	// 地址
	Address *string `json:"address,optional"`
	// 分成比例(%)
	CommissionRate *int64 `json:"commissionRate,optional"`
	// 状态:0禁用,1启用
	Status *uint32 `json:"status,optional"`
	// 关联用户ID
	UserId *string `json:"userId,optional"`
	// 备注
	Remark *string `json:"remark,optional"`
}

// The response data of mall supplier list | 供应商信息列表数据
// swagger:model MallSupplierListResp
type MallSupplierListResp struct {
	BaseDataInfo
	// MallSupplier list data | 供应商信息列表数据
	Data MallSupplierListInfo `json:"data"`
}

// The mall supplier list data | 供应商信息列表数据
// swagger:model MallSupplierListInfo
type MallSupplierListInfo struct {
	BaseListInfo
	// The mall supplier list data | 供应商信息列表数据
	Data []MallSupplierInfo `json:"data"`
}

// Get mall supplier list request params | 供应商列表请求参数
// swagger:model MallSupplierListReq
type MallSupplierListReq struct {
	PageInfo
	// UserId
	UserId *string `json:"userId,optional"`
	// SupplierId
	SupplierId *uint64 `json:"supplierId,optional"`
	// OrderId
	OrderId *uint64 `json:"orderId,optional"`
	// OrderNo
	OrderNo *string `json:"orderNo,optional"`
	// ProductId
	ProductId *uint64 `json:"productId,optional"`
	// ProductName
	ProductName *string `json:"productName,optional"`
	// OrderAmount
	OrderAmount *int64 `json:"orderAmount,optional"`
	// CommissionRate
	CommissionRate *int64 `json:"commissionRate,optional"`
	// CommissionAmount
	CommissionAmount *int64 `json:"commissionAmount,optional"`
	// Status
	Status *uint32 `json:"status,optional"`
	// SettleTime
	SettleTime *int64 `json:"settleTime,optional"`
	// Remark
	Remark *string `json:"remark,optional"`
	// Name
	Name *string `json:"name,optional"`
	// Code
	Code *string `json:"code,optional"`
	// ContactName
	ContactName *string `json:"contactName,optional"`
	// ContactPhone
	ContactPhone *string `json:"contactPhone,optional"`
	// ContactEmail
	ContactEmail *string `json:"contactEmail,optional"`
	// Address
	Address *string `json:"address,optional"`
}

// The mall supplier information response | 供应商信息返回体
// swagger:model MallSupplierInfoResp
type MallSupplierInfoResp struct {
	BaseDataInfo
	// The mall supplier information | 供应商信息数据
	Data MallSupplierInfo `json:"data"`
}

// The response data of mall supplier commission information | 供应商分成信息
// swagger:model MallSupplierCommissionInfo
type MallSupplierCommissionInfo struct {
	BaseIDInfo
	// 供应商ID
	SupplierId *uint64 `json:"supplierId,optional"`
	// 订单ID
	OrderId *uint64 `json:"orderId,optional"`
	// 订单编号
	OrderNo *string `json:"orderNo,optional"`
	// 商品ID
	ProductId *uint64 `json:"productId,optional"`
	// 商品名称
	ProductName *string `json:"productName,optional"`
	// 订单金额(分)
	OrderAmount *int64 `json:"orderAmount,optional"`
	// 分成比例(%)
	CommissionRate *int64 `json:"commissionRate,optional"`
	// 分成金额(分)
	CommissionAmount *int64 `json:"commissionAmount,optional"`
	// 状态:0待结算,1已结算,2已取消
	Status *uint32 `json:"status,optional"`
	// 结算时间
	SettleTime *int64 `json:"settleTime,optional"`
	// 备注
	Remark *string `json:"remark,optional"`
}

// The response data of mall supplier commission list | 供应商分成信息列表数据
// swagger:model MallSupplierCommissionListResp
type MallSupplierCommissionListResp struct {
	BaseDataInfo
	// MallSupplierCommission list data | 供应商分成信息列表数据
	Data MallSupplierCommissionListInfo `json:"data"`
}

// The mall supplier commission list data | 供应商分成信息列表数据
// swagger:model MallSupplierCommissionListInfo
type MallSupplierCommissionListInfo struct {
	BaseListInfo
	// The mall supplier commission list data | 供应商分成信息列表数据
	Data []MallSupplierCommissionInfo `json:"data"`
}

// Get mall supplier commission list request params | 供应商分成列表请求参数
// swagger:model MallSupplierCommissionListReq
type MallSupplierCommissionListReq struct {
	PageInfo
	// SupplierId
	SupplierId *uint64 `json:"supplierId,optional"`
	// OrderId
	OrderId *uint64 `json:"orderId,optional"`
	// OrderNo
	OrderNo *string `json:"orderNo,optional"`
	// ProductId
	ProductId *uint64 `json:"productId,optional"`
	// ProductName
	ProductName *string `json:"productName,optional"`
	// OrderAmount
	OrderAmount *int64 `json:"orderAmount,optional"`
	// CommissionRate
	CommissionRate *int64 `json:"commissionRate,optional"`
	// CommissionAmount
	CommissionAmount *int64 `json:"commissionAmount,optional"`
	// Status
	Status *uint32 `json:"status,optional"`
	// SettleTime
	SettleTime *int64 `json:"settleTime,optional"`
	// Remark
	Remark *string `json:"remark,optional"`
}

// The mall supplier commission information response | 供应商分成信息返回体
// swagger:model MallSupplierCommissionInfoResp
type MallSupplierCommissionInfoResp struct {
	BaseDataInfo
	// The mall supplier commission information | 供应商分成信息数据
	Data MallSupplierCommissionInfo `json:"data"`
}

// The response data of mall application information | MallApplication信息
// swagger:model MallApplicationInfo
type MallApplicationInfo struct {
	BaseIDInfo
	// 应用编码
	AppCode *string `json:"appCode,optional"`
	// 应用名称
	AppName *string `json:"appName,optional"`
	// 应用版本
	AppVersionName *string `json:"appVersionName,optional"`
	// 内部版本号
	AppVersionCode *int64 `json:"appVersionCode,optional"`
	// 应用类型:1桌面应用,2移动应用,3网页应用,4服务端应用
	AppType *int32 `json:"appType,optional"`
	// 应用描述
	AppDescription *string `json:"appDescription,optional"`
	// 应用图标URL
	IconUrl *string `json:"iconUrl,optional"`
	// 状态:0禁用,1启用
	Status *uint32 `json:"status,optional"`
}

// The response data of mall application list | MallApplication信息列表数据
// swagger:model MallApplicationListResp
type MallApplicationListResp struct {
	BaseDataInfo
	// MallApplication list data | MallApplication信息列表数据
	Data MallApplicationListInfo `json:"data"`
}

// The mall application list data | MallApplication信息列表数据
// swagger:model MallApplicationListInfo
type MallApplicationListInfo struct {
	BaseListInfo
	// The mall application list data | MallApplication信息列表数据
	Data []MallApplicationInfo `json:"data"`
}

// Get mall application list request params | MallApplication列表请求参数
// swagger:model MallApplicationListReq
type MallApplicationListReq struct {
	PageInfo
	// AppCode
	AppCode *string `json:"appCode,optional"`
	// AppName
	AppName *string `json:"appName,optional"`
	// AppVersionName
	AppVersionName *string `json:"appVersionName,optional"`
	// AppVersionCode
	AppVersionCode *int64 `json:"appVersionCode,optional"`
	// AppType
	AppType *int32 `json:"appType,optional"`
	// AppDescription
	AppDescription *string `json:"appDescription,optional"`
	// IconUrl
	IconUrl *string `json:"iconUrl,optional"`
	// Status
	Status *uint32 `json:"status,optional"`
}

// The mall application information response | MallApplication信息返回体
// swagger:model MallApplicationInfoResp
type MallApplicationInfoResp struct {
	BaseDataInfo
	// The mall application information | MallApplication信息数据
	Data MallApplicationInfo `json:"data"`
}

// The response data of bolo lexicon information | BoloLexicon信息
// swagger:model BoloLexiconInfo
type BoloLexiconInfo struct {
	BaseIDInfo
	// 词汇内容
	Word *string `json:"word,optional"`
	// 词汇类型
	WordType *string `json:"wordType,optional"`
	// 拼音标注
	Pinyin *string `json:"pinyin,optional"`
	// 词汇释义
	Definition *string `json:"definition,optional"`
	// 状态 1=启用 0=禁用
	Status *uint32 `json:"status,optional"`
	// 创建人ID
	CreatorId *uint64 `json:"creatorId,optional"`
	// 权重（用于排序）
	Weight *int32 `json:"weight,optional"`
	// 标签（逗号分隔）
	Tags *string `json:"tags,optional"`
	// 示例用法
	Example *string `json:"example,optional"`
}

// The response data of bolo lexicon list | BoloLexicon信息列表数据
// swagger:model BoloLexiconListResp
type BoloLexiconListResp struct {
	BaseDataInfo
	// BoloLexicon list data | BoloLexicon信息列表数据
	Data BoloLexiconListInfo `json:"data"`
}

// The bolo lexicon list data | BoloLexicon信息列表数据
// swagger:model BoloLexiconListInfo
type BoloLexiconListInfo struct {
	BaseListInfo
	// The bolo lexicon list data | BoloLexicon信息列表数据
	Data []BoloLexiconInfo `json:"data"`
}

// Get bolo lexicon list request params | BoloLexicon列表请求参数
// swagger:model BoloLexiconListReq
type BoloLexiconListReq struct {
	PageInfo
	// CreatedAt
	CreatedAt *int64 `json:"createdAt,optional"`
	// UpdatedAt
	UpdatedAt *int64 `json:"updatedAt,optional"`
	// DeletedAt
	DeletedAt *int64 `json:"deletedAt,optional"`
	// Word
	Word *string `json:"word,optional"`
	// WordType
	WordType *string `json:"wordType,optional"`
	// Pinyin
	Pinyin *string `json:"pinyin,optional"`
	// Definition
	Definition *string `json:"definition,optional"`
	// Status
	Status *uint32 `json:"status,optional"`
	// CreatorId
	CreatorId *uint64 `json:"creatorId,optional"`
	// Weight
	Weight *int32 `json:"weight,optional"`
	// Tags
	Tags *string `json:"tags,optional"`
	// Example
	Example *string `json:"example,optional"`
}

// The bolo lexicon information response | BoloLexicon信息返回体
// swagger:model BoloLexiconInfoResp
type BoloLexiconInfoResp struct {
	BaseDataInfo
	// The bolo lexicon information | BoloLexicon信息数据
	Data BoloLexiconInfo `json:"data"`
}

// swagger:model BoloLexiconImportReq
type BoloLexiconImportReq struct {
	Name *string `json:"name,optional"`
	// 处理重复方式：skip-跳过，overwrite-覆盖
	HandleDuplicate *string `json:"handleDuplicate,options=skip|overwrite,default=skip"`
}

// 通用统计查询请求
// swagger:model UniversalStatsReq
type UniversalStatsReq struct {
	AppId          uint64                 `json:"appId"`                   // 应用ID
	StartDate      int64                  `json:"startDate"`               // 开始日期（时间戳）
	EndDate        int64                  `json:"endDate"`                 // 结束日期（时间戳）
	MetricType     string                 `json:"metricType"`              // 指标类型：banner_clicks, device_stats, active_users, feature_usage, purchase_funnel, version_stats, payment_sku
	Dimensions     []string               `json:"dimensions,optional"`     // 查询维度：device_model, device_brand, os_version, region, user_type等
	Filters        map[string]interface{} `json:"filters,optional"`        // 过滤条件：{"bannerId": "banner_001", "featureName": "login"}
	GroupBy        []string               `json:"groupBy,optional"`        // 分组字段：["device_model", "date"]
	Interval       *string                `json:"interval,optional"`       // 时间间隔：hour, day, week, month
	Limit          *int32                 `json:"limit,optional"`          // 结果数量限制
	OrderBy        *string                `json:"orderBy,optional"`        // 排序字段
	OrderDirection *string                `json:"orderDirection,optional"` // 排序方向：asc, desc
}

// 通用统计数据项
type UniversalStatsItem struct {
	Timestamp  int64                  `json:"timestamp,optional"`  // 时间戳（用于时间序列数据）
	Date       *string                `json:"date,optional"`       // 日期字符串
	Dimensions map[string]interface{} `json:"dimensions,optional"` // 维度数据：{"device_model": "iPhone 14", "region": "北京"}
	Metrics    map[string]interface{} `json:"metrics"`             // 指标数据：{"clicks": 1234, "impressions": 5678, "click_rate": 21.7}
	Metadata   map[string]interface{} `json:"metadata,optional"`   // 元数据：{"banner_name": "首页广告位"}
}

// 通用统计响应
// swagger:model UniversalStatsResp
type UniversalStatsResp struct {
	BaseDataInfo
	Data     []UniversalStatsItem   `json:"data"`              // 统计数据
	Summary  map[string]interface{} `json:"summary,optional"`  // 汇总信息：{"total_clicks": 12345, "avg_click_rate": 15.6}
	Metadata map[string]interface{} `json:"metadata,optional"` // 响应元数据：{"metric_type": "banner_clicks", "time_range": "7d"}
}

// 仪表盘数据请求
// swagger:model AnalyticsDashboardReq
type AnalyticsDashboardReq struct {
	AppId     uint64 `json:"appId"`
	StartDate int64  `json:"startDate"`
	EndDate   int64  `json:"endDate"`
}

// 仪表盘指标数据
type DashboardMetric struct {
	MetricName string  `json:"metricName"`
	Value      float64 `json:"value"`
	Change     float64 `json:"change"` // 相比上一周期的变化百分比
}

// 热门事件数据
type TopEvent struct {
	EventName string `json:"eventName"`
	Count     int64  `json:"count"`
}

// 设备分布数据
type DeviceDistribution struct {
	DeviceType string  `json:"deviceType"`
	Count      int64   `json:"count"`
	Percentage float64 `json:"percentage"`
}

// 趋势数据
type TrendData struct {
	Time     int64   `json:"time"`
	Value    float64 `json:"value"`
	Category *string `json:"category,optional"`
}

// 对比数据
type ComparisonData struct {
	Name          string  `json:"name"`
	Current       float64 `json:"current"`
	Previous      float64 `json:"previous"`
	Change        float64 `json:"change"`
	ChangePercent float64 `json:"changePercent"`
}

// 地理分布数据
type GeographicData struct {
	Region      string    `json:"region"`
	Country     string    `json:"country"`
	Users       int64     `json:"users"`
	Sessions    int64     `json:"sessions"`
	Coordinates []float64 `json:"coordinates"`
}

// 用户行为数据
type UserBehaviorData struct {
	Hour        int32 `json:"hour"`
	ActiveUsers int64 `json:"activeUsers"`
	NewUsers    int64 `json:"newUsers"`
	Sessions    int64 `json:"sessions"`
}

// 仪表盘数据
type DashboardData struct {
	ActiveUsers         DashboardMetric      `json:"activeUsers"`
	NewUsers            DashboardMetric      `json:"newUsers"`
	EventCount          DashboardMetric      `json:"eventCount"`
	AvgSessionTime      DashboardMetric      `json:"avgSessionTime"`
	TopEvents           []TopEvent           `json:"topEvents"`
	DeviceDistribution  []DeviceDistribution `json:"deviceDistribution"`
	UserTrends          []TrendData          `json:"userTrends"`
	EventTrends         []TrendData          `json:"eventTrends"`
	ComparisonData      []ComparisonData     `json:"comparisonData"`
	GeographicData      []GeographicData     `json:"geographicData"`
	UserBehaviorHeatmap [][]float64          `json:"userBehaviorHeatmap"`
	RealtimeData        []TrendData          `json:"realtimeData"`
}

// 仪表盘数据响应
// swagger:model AnalyticsDashboardResp
type AnalyticsDashboardResp struct {
	BaseDataInfo
	Data DashboardData `json:"data"`
}

// 漏斗信息
// swagger:model FunnelInfo
type FunnelInfo struct {
	// ID
	Id *uint64 `json:"id,optional"`
	// 创建时间 | Create date
	CreatedAt *int64 `json:"createdAt,optional"`
	// 更新时间 | Update date
	UpdatedAt *int64 `json:"updatedAt,optional"`
	// 应用ID | Application ID
	AppId *uint64 `json:"appId,optional" validate:"omitempty,number"`
	// 漏斗名称 | Funnel name
	// max length : 100
	FunnelName *string `json:"funnelName,optional" validate:"omitempty,max=100"`
	// 漏斗描述 | Funnel description
	// max length : 500
	Description *string `json:"description,optional" validate:"omitempty,max=500"`
	// 漏斗类型：1-转化漏斗,2-留存漏斗,3-行为漏斗 | Funnel type
	// min length : 1
	// max length : 3
	FunnelType *int32 `json:"funnelType,optional" validate:"omitempty,min=1,max=3"`
	// 时间窗口(小时) | Time window in hours
	// min length : 1
	// max length : 168
	TimeWindow *int32 `json:"timeWindow,optional" validate:"omitempty,min=1,max=168"`
	// 状态：0-禁用,1-启用 | Status
	Status *uint32 `json:"status,optional" validate:"omitempty,oneof=0 1"`
	// 创建者ID | Creator ID
	CreatorId *uint64 `json:"creatorId,optional"`
	// 步骤 | Steps
	Steps []FunnelStep `json:"steps,optional"`
}

// 漏斗步骤
type FunnelStep struct {
	Id       *uint64 `json:"id,optional"`
	FunnelId *uint64 `json:"funnelId,optional"`
	// required : true
	StepName string `json:"stepName" validate:"required"`
	// required : true
	StepOrder       int32             `json:"stepOrder" validate:"required"`
	StepDescription *string           `json:"stepDescription,optional"`
	IsRequired      *bool             `json:"isRequired,optional"`
	Conditions      []FunnelCondition `json:"conditions,optional"`
}

// 漏斗条件
type FunnelCondition struct {
	Id            *uint64 `json:"id,optional"`
	StepId        *uint64 `json:"stepId,optional"`
	ConditionType *string `json:"conditionType,optional"`
	EventName     *string `json:"eventName,optional"`
	PropertyKey   *string `json:"propertyKey,optional"`
	Operator      *string `json:"operator,optional"`
	PropertyValue *string `json:"propertyValue,optional"`
}

// 漏斗步骤信息
// swagger:model FunnelStepInfo
type FunnelStepInfo struct {
	// ID
	Id *uint64 `json:"id,optional"`
	// 创建时间 | Create date
	CreatedAt *int64 `json:"createdAt,optional"`
	// 更新时间 | Update date
	UpdatedAt *int64 `json:"updatedAt,optional"`
	// 漏斗ID | Funnel ID
	FunnelId *uint64 `json:"funnelId,optional"`
	// 步骤顺序 | Step order
	// min length : 1
	StepOrder *int32 `json:"stepOrder,optional" validate:"omitempty,min=1"`
	// 步骤名称 | Step name
	// max length : 100
	StepName *string `json:"stepName,optional" validate:"omitempty,max=100"`
	// 步骤描述 | Step description
	// max length : 500
	StepDescription *string `json:"stepDescription,optional" validate:"omitempty,max=500"`
	// 是否必需步骤 | Is required step
	IsRequired *bool `json:"isRequired,optional"`
}

// 漏斗条件信息
// swagger:model FunnelConditionInfo
type FunnelConditionInfo struct {
	// ID
	Id *uint64 `json:"id,optional"`
	// 创建时间 | Create date
	CreatedAt *int64 `json:"createdAt,optional"`
	// 更新时间 | Update date
	UpdatedAt *int64 `json:"updatedAt,optional"`
	// 步骤ID | Step ID
	StepId *uint64 `json:"stepId,optional"`
	// 条件类型 | Condition type
	ConditionType *string `json:"conditionType,optional"`
	// 条件键 | Condition key
	ConditionKey *string `json:"conditionKey,optional"`
	// 条件操作符 | Condition operator
	ConditionOperator *string `json:"conditionOperator,optional"`
	// 条件值 | Condition value
	ConditionValue *string `json:"conditionValue,optional"`
}

// 漏斗列表请求
// swagger:model FunnelListReq
type FunnelListReq struct {
	PageInfo
	// 应用ID | Application ID
	AppId *uint64 `json:"appId,optional"`
	// 漏斗名称 | Funnel name
	FunnelName *string `json:"funnelName,optional"`
	// 漏斗描述 | Funnel description
	Description *string `json:"description,optional"`
	// 漏斗类型 | Funnel type
	FunnelType *int32 `json:"funnelType,optional"`
	// 时间窗口 | Time window
	TimeWindow *int32 `json:"timeWindow,optional"`
	// 状态 | Status
	Status *uint32 `json:"status,optional"`
	// 创建者ID | Creator ID
	CreatorId *uint64 `json:"creatorId,optional"`
	// 创建时间 | Created at
	CreatedAt *int64 `json:"createdAt,optional"`
	// 更新时间 | Updated at
	UpdatedAt *int64 `json:"updatedAt,optional"`
}

// 漏斗列表数据
// swagger:model FunnelListInfo
type FunnelListInfo struct {
	BaseListInfo
	// 漏斗列表数据 | Funnel list data
	Data []FunnelInfo `json:"data"`
}

// 漏斗列表响应
// swagger:model FunnelListResp
type FunnelListResp struct {
	BaseDataInfo
	// 漏斗列表数据 | Funnel list data
	Data FunnelListInfo `json:"data"`
}

// 漏斗信息响应
// swagger:model FunnelInfoResp
type FunnelInfoResp struct {
	BaseDataInfo
	// 漏斗信息 | Funnel data
	Data FunnelInfo `json:"data"`
}

// 漏斗步骤列表请求
// swagger:model FunnelStepListReq
type FunnelStepListReq struct {
	PageInfo
	// 漏斗ID | Funnel ID
	FunnelId *uint64 `json:"funnelId,optional"`
	// 步骤顺序 | Step order
	StepOrder *int32 `json:"stepOrder,optional"`
	// 步骤名称 | Step name
	StepName *string `json:"stepName,optional"`
	// 步骤描述 | Step description
	StepDescription *string `json:"stepDescription,optional"`
	// 是否必需 | Is required
	IsRequired *bool `json:"isRequired,optional"`
	// 创建时间 | Created at
	CreatedAt *int64 `json:"createdAt,optional"`
	// 更新时间 | Updated at
	UpdatedAt *int64 `json:"updatedAt,optional"`
}

// 漏斗步骤列表数据
// swagger:model FunnelStepListInfo
type FunnelStepListInfo struct {
	BaseListInfo
	// 漏斗步骤列表数据 | Funnel step list data
	Data []FunnelStepInfo `json:"data"`
}

// 漏斗步骤列表响应
// swagger:model FunnelStepListResp
type FunnelStepListResp struct {
	BaseDataInfo
	// 漏斗步骤列表数据 | Funnel step list data
	Data FunnelStepListInfo `json:"data"`
}

// 漏斗步骤信息响应
// swagger:model FunnelStepInfoResp
type FunnelStepInfoResp struct {
	BaseDataInfo
	// 漏斗步骤信息 | Funnel step data
	Data FunnelStepInfo `json:"data"`
}

// 漏斗条件列表请求
// swagger:model FunnelConditionListReq
type FunnelConditionListReq struct {
	PageInfo
	// 步骤ID | Step ID
	StepId *uint64 `json:"stepId,optional"`
	// 条件类型 | Condition type
	ConditionType *string `json:"conditionType,optional"`
	// 条件键 | Condition key
	ConditionKey *string `json:"conditionKey,optional"`
	// 条件操作符 | Condition operator
	ConditionOperator *string `json:"conditionOperator,optional"`
	// 条件值 | Condition value
	ConditionValue *string `json:"conditionValue,optional"`
	// 创建时间 | Created at
	CreatedAt *int64 `json:"createdAt,optional"`
	// 更新时间 | Updated at
	UpdatedAt *int64 `json:"updatedAt,optional"`
}

// 漏斗条件列表数据
// swagger:model FunnelConditionListInfo
type FunnelConditionListInfo struct {
	BaseListInfo
	// 漏斗条件列表数据 | Funnel condition list data
	Data []FunnelConditionInfo `json:"data"`
}

// 漏斗条件列表响应
// swagger:model FunnelConditionListResp
type FunnelConditionListResp struct {
	BaseDataInfo
	// 漏斗条件列表数据 | Funnel condition list data
	Data FunnelConditionListInfo `json:"data"`
}

// 漏斗条件信息响应
// swagger:model FunnelConditionInfoResp
type FunnelConditionInfoResp struct {
	BaseDataInfo
	// 漏斗条件信息 | Funnel condition data
	Data FunnelConditionInfo `json:"data"`
}

// 运行漏斗分析请求
// swagger:model RunFunnelAnalysisReq
type RunFunnelAnalysisReq struct {
	// 漏斗ID | Funnel ID
	// required : true
	FunnelId uint64 `json:"funnelId" validate:"required,number"`
	// 开始时间 | Start time
	StartTime *int64 `json:"startTime,optional"`
	// 结束时间 | End time
	EndTime *int64 `json:"endTime,optional"`
	// 分析参数 | Analysis parameters
	Parameters map[string]interface{} `json:"parameters,optional"`
}

// 漏斗分析结果
type FunnelAnalysisResult struct {
	// 漏斗ID | Funnel ID
	FunnelId uint64 `json:"funnelId"`
	// 漏斗名称 | Funnel name
	FunnelName string `json:"funnelName"`
	// 分析时间范围 | Analysis time range
	TimeRange map[string]interface{} `json:"timeRange"`
	// 总体转化率 | Overall conversion rate
	OverallConversionRate float64 `json:"overallConversionRate"`
	// 步骤转化数据 | Step conversion data
	StepConversions []map[string]interface{} `json:"stepConversions"`
	// 用户路径数据 | User path data
	UserPaths []map[string]interface{} `json:"userPaths,optional"`
	// 趋势数据 | Trend data
	TrendData []map[string]interface{} `json:"trendData,optional"`
	// 统计摘要 | Statistics summary
	Summary map[string]interface{} `json:"summary"`
}

// 漏斗分析响应
// swagger:model FunnelAnalysisResp
type FunnelAnalysisResp struct {
	BaseDataInfo
	// 漏斗分析结果 | Funnel analysis result
	Data FunnelAnalysisResult `json:"data"`
}

// 路径分析请求
// swagger:model PathAnalysisReq
type PathAnalysisReq struct {
	AppId        uint64  `json:"appId"`                 // 应用ID
	StartDate    int64   `json:"startDate"`             // 开始日期（时间戳）
	EndDate      int64   `json:"endDate"`               // 结束日期（时间戳）
	StartEvent   *string `json:"startEvent,optional"`   // 起点事件
	EndEvent     *string `json:"endEvent,optional"`     // 终点事件
	MaxDepth     *int32  `json:"maxDepth,optional"`     // 最大路径深度
	MinUserCount *int32  `json:"minUserCount,optional"` // 最小用户数量
	PathFilters  *string `json:"pathFilters,optional"`  // JSON格式的路径过滤条件
}

// 位置信息
type Position struct {
	X float64 `json:"x"` // X坐标
	Y float64 `json:"y"` // Y坐标
}

// 路径节点
type PathNode struct {
	EventName   string    `json:"eventName"`         // 事件名称
	UserCount   int64     `json:"userCount"`         // 用户数量
	Percentage  float64   `json:"percentage"`        // 百分比
	AvgTime     float64   `json:"avgTime"`           // 平均停留时间（秒）
	DropOffRate float64   `json:"dropOffRate"`       // 流失率
	Position    *Position `json:"position,optional"` // 位置信息
}

// 路径连接
type PathLink struct {
	Source            string  `json:"source"`            // 源事件名称
	Target            string  `json:"target"`            // 目标事件名称
	UserCount         int64   `json:"userCount"`         // 用户数量
	Percentage        float64 `json:"percentage"`        // 百分比
	AvgTransitionTime float64 `json:"avgTransitionTime"` // 平均转换时间（秒）
	ConversionRate    float64 `json:"conversionRate"`    // 转化率
}

// 用户路径
type UserPath struct {
	PathId         string   `json:"pathId"`         // 路径ID
	Events         []string `json:"events"`         // 事件序列
	UserCount      int64    `json:"userCount"`      // 用户数量
	Percentage     float64  `json:"percentage"`     // 百分比
	AvgDuration    float64  `json:"avgDuration"`    // 平均路径完成时间（秒）
	ConversionRate float64  `json:"conversionRate"` // 转化率
}

// 路径分析结果
type PathAnalysisResult struct {
	TotalUsers     int64      `json:"totalUsers"`     // 总用户数
	TotalPaths     int64      `json:"totalPaths"`     // 总路径数
	AvgPathLength  float64    `json:"avgPathLength"`  // 平均路径长度
	CompletionRate float64    `json:"completionRate"` // 完成率
	Nodes          []PathNode `json:"nodes"`          // 节点数据
	Links          []PathLink `json:"links"`          // 连接数据
	TopPaths       []UserPath `json:"topPaths"`       // 热门路径
	AnalysisTime   int64      `json:"analysisTime"`   // 分析时间
}

// 路径分析响应
// swagger:model PathAnalysisResp
type PathAnalysisResp struct {
	BaseDataInfo
	Data PathAnalysisResult `json:"data"`
}

// 路径统计数据
type PathStatsData struct {
	Date           string  `json:"date"`           // 日期
	TotalUsers     int64   `json:"totalUsers"`     // 总用户数
	UniquePaths    int64   `json:"uniquePaths"`    // 唯一路径数
	AvgPathLength  float64 `json:"avgPathLength"`  // 平均路径长度
	CompletionRate float64 `json:"completionRate"` // 完成率
}

// 事件转换数据
type EventConversionData struct {
	FromEvent      string  `json:"fromEvent"`      // 源事件
	ToEvent        string  `json:"toEvent"`        // 目标事件
	UserCount      int64   `json:"userCount"`      // 用户数量
	ConversionRate float64 `json:"conversionRate"` // 转化率
	AvgTime        float64 `json:"avgTime"`        // 平均转换时间
}

// 路径热力图数据
type PathHeatmapData struct {
	EventName string `json:"eventName"` // 事件名称
	Hour      int32  `json:"hour"`      // 小时
	Day       int32  `json:"day"`       // 星期几
	UserCount int64  `json:"userCount"` // 用户数量
}

// 用户趋势响应
// swagger:model UserTrendsResp
type UserTrendsResp struct {
	BaseDataInfo
	Data []TrendData `json:"data"`
}

// 事件趋势响应
// swagger:model EventTrendsResp
type EventTrendsResp struct {
	BaseDataInfo
	Data []TrendData `json:"data"`
}

// 地理分布数据响应
// swagger:model GeographicDataResp
type GeographicDataResp struct {
	BaseDataInfo
	Data []GeographicData `json:"data"`
}

// 用户行为热力图响应
// swagger:model UserBehaviorHeatmapResp
type UserBehaviorHeatmapResp struct {
	BaseDataInfo
	Data [][]float64 `json:"data"`
}

// 实时数据响应
// swagger:model RealtimeDataResp
type RealtimeDataResp struct {
	BaseDataInfo
	Data []TrendData `json:"data"`
}

// 导出数据响应
// swagger:model ExportDataResp
type ExportDataResp struct {
	BaseDataInfo
	Data string `json:"data"`
}

// 路径统计响应
// swagger:model PathStatsResp
type PathStatsResp struct {
	BaseDataInfo
	Data []PathStatsData `json:"data"`
}

// 事件转换响应
// swagger:model EventConversionsResp
type EventConversionsResp struct {
	BaseDataInfo
	Data []EventConversionData `json:"data"`
}

// 路径热力图响应
// swagger:model PathHeatmapResp
type PathHeatmapResp struct {
	BaseDataInfo
	Data []PathHeatmapData `json:"data"`
}

// The response data of analytics application information | AnalyticsApplication信息
// swagger:model AnalyticsApplicationInfo
type AnalyticsApplicationInfo struct {
	BaseIDInfo
	// 应用统计Key（对外使用）
	AppKey *string `json:"appKey,optional"`
	// 应用名称
	AppName *string `json:"appName,optional"`
	// 应用描述
	AppDescription *string `json:"appDescription,optional"`
	// 应用类型：1-桌面应用,2-移动应用,3-网页应用,4-服务端应用
	AppType *int32 `json:"appType,optional"`
	// 应用域名
	Domain *string `json:"domain,optional"`
	// 平台信息
	Platform *string `json:"platform,optional"`
	// 状态：0-禁用,1-启用
	Status *uint32 `json:"status,optional"`
	// 采样率(0.0001-1.0000)
	SamplingRate *float64 `json:"samplingRate,optional"`
	// 数据保留天数
	DataRetentionDays *int32 `json:"dataRetentionDays,optional"`
	// 时区
	Timezone *string `json:"timezone,optional"`
	// 创建人ID
	CreatedBy *uint64 `json:"createdBy,optional"`
	// 应用密钥（API私钥）
	AppSecret *string `json:"appSecret,optional"`
}

// The response data of analytics application list | AnalyticsApplication信息列表数据
// swagger:model AnalyticsApplicationListResp
type AnalyticsApplicationListResp struct {
	BaseDataInfo
	// AnalyticsApplication list data | AnalyticsApplication信息列表数据
	Data AnalyticsApplicationListInfo `json:"data"`
}

// The analytics application list data | AnalyticsApplication信息列表数据
// swagger:model AnalyticsApplicationListInfo
type AnalyticsApplicationListInfo struct {
	BaseListInfo
	// The analytics application list data | AnalyticsApplication信息列表数据
	Data []AnalyticsApplicationInfo `json:"data"`
}

// Get analytics application list request params | AnalyticsApplication列表请求参数
// swagger:model AnalyticsApplicationListReq
type AnalyticsApplicationListReq struct {
	PageInfo
	// AppKey
	AppKey *string `json:"appKey,optional"`
	// AppName
	AppName *string `json:"appName,optional"`
	// AppDescription
	AppDescription *string `json:"appDescription,optional"`
	// AppType
	AppType *int32 `json:"appType,optional"`
	// Domain
	Domain *string `json:"domain,optional"`
	// Platform
	Platform *string `json:"platform,optional"`
	// Status
	Status *uint32 `json:"status,optional"`
	// SamplingRate
	SamplingRate *float64 `json:"samplingRate,optional"`
	// DataRetentionDays
	DataRetentionDays *int32 `json:"dataRetentionDays,optional"`
	// Timezone
	Timezone *string `json:"timezone,optional"`
	// CreatedBy
	CreatedBy *uint64 `json:"createdBy,optional"`
}

// The analytics application information response | AnalyticsApplication信息返回体
// swagger:model AnalyticsApplicationInfoResp
type AnalyticsApplicationInfoResp struct {
	BaseDataInfo
	// The analytics application information | AnalyticsApplication信息数据
	Data AnalyticsApplicationInfo `json:"data"`
}

// 生成API密钥请求
// swagger:model GenerateApiSecretReq
type GenerateApiSecretReq struct {
	// 应用ID
	Id uint64 `json:"id"`
}

// 重置API密钥请求
// swagger:model ResetApiSecretReq
type ResetApiSecretReq struct {
	// 应用ID
	Id uint64 `json:"id"`
}

// The response data of analytics cohort information | AnalyticsCohort信息
// swagger:model AnalyticsCohortInfo
type AnalyticsCohortInfo struct {
	BaseIDInfo
	// 应用ID
	AppId *uint64 `json:"appId,optional"`
	// 群组名称
	CohortName *string `json:"cohortName,optional"`
	// 群组描述
	Description *string `json:"description,optional"`
	// 群组类型: 1-行为群组, 2-属性群组, 3-自定义群组
	CohortType *int32 `json:"cohortType,optional"`
	// 群组定义规则 (JSON格式)
	Definition *string `json:"definition,optional"`
	// 时间范围配置
	TimeRange *string `json:"timeRange,optional"`
	// 状态: 0-禁用, 1-启用
	Status *uint32 `json:"status,optional"`
	// 创建者ID
	CreatorId *uint64 `json:"creatorId,optional"`
	// 是否公开: 0-私有, 1-公开
	IsPublic *int32 `json:"isPublic,optional"`
	// 用户数量
	UserCount *uint64 `json:"userCount,optional"`
	// 最后计算时间戳(毫秒)
	LastCalculatedTime *int64 `json:"lastCalculatedTime,optional"`
}

// The response data of analytics cohort list | AnalyticsCohort信息列表数据
// swagger:model AnalyticsCohortListResp
type AnalyticsCohortListResp struct {
	BaseDataInfo
	// AnalyticsCohort list data | AnalyticsCohort信息列表数据
	Data AnalyticsCohortListInfo `json:"data"`
}

// The analytics cohort list data | AnalyticsCohort信息列表数据
// swagger:model AnalyticsCohortListInfo
type AnalyticsCohortListInfo struct {
	BaseListInfo
	// The analytics cohort list data | AnalyticsCohort信息列表数据
	Data []AnalyticsCohortInfo `json:"data"`
}

// Get analytics cohort list request params | AnalyticsCohort列表请求参数
// swagger:model AnalyticsCohortListReq
type AnalyticsCohortListReq struct {
	PageInfo
	// CreatedAt
	CreatedAt *int64 `json:"createdAt,optional"`
	// UpdatedAt
	UpdatedAt *int64 `json:"updatedAt,optional"`
	// DeletedAt
	DeletedAt *int64 `json:"deletedAt,optional"`
	// AppId
	AppId *uint64 `json:"appId,optional"`
	// CohortName
	CohortName *string `json:"cohortName,optional"`
	// Description
	Description *string `json:"description,optional"`
	// CohortType
	CohortType *int32 `json:"cohortType,optional"`
	// Definition
	Definition *string `json:"definition,optional"`
	// TimeRange
	TimeRange *string `json:"timeRange,optional"`
	// Status
	Status *uint32 `json:"status,optional"`
	// CreatorId
	CreatorId *uint64 `json:"creatorId,optional"`
	// IsPublic
	IsPublic *int32 `json:"isPublic,optional"`
	// UserCount
	UserCount *uint64 `json:"userCount,optional"`
	// LastCalculatedTime
	LastCalculatedTime *int64 `json:"lastCalculatedTime,optional"`
}

// The analytics cohort information response | AnalyticsCohort信息返回体
// swagger:model AnalyticsCohortInfoResp
type AnalyticsCohortInfoResp struct {
	BaseDataInfo
	// The analytics cohort information | AnalyticsCohort信息数据
	Data AnalyticsCohortInfo `json:"data"`
}

// The response data of analytics daily summary information | AnalyticsDailySummary信息
// swagger:model AnalyticsDailySummaryInfo
type AnalyticsDailySummaryInfo struct {
	BaseIDInfo
	// 应用ID
	AppId *uint64 `json:"appId,optional"`
	// 统计日期
	Date *int64 `json:"date,optional"`
	// 指标类型: event, user, device, funnel, cohort
	MetricType *string `json:"metricType,optional"`
	// 指标名称
	MetricName *string `json:"metricName,optional"`
	// 维度: platform, country, version等
	Dimension *string `json:"dimension,optional"`
	// 维度值
	DimensionValue *string `json:"dimensionValue,optional"`
	// 总数
	TotalCount *uint64 `json:"totalCount,optional"`
	// 去重数
	UniqueCount *uint64 `json:"uniqueCount,optional"`
	// 求和值
	SumValue *float64 `json:"sumValue,optional"`
	// 平均值
	AvgValue *float64 `json:"avgValue,optional"`
	// 最小值
	MinValue *float64 `json:"minValue,optional"`
	// 最大值
	MaxValue *float64 `json:"maxValue,optional"`
	// 中位数
	Percentile_50 *float64 `json:"percentile50,optional"`
	// 90分位数
	Percentile_90 *float64 `json:"percentile90,optional"`
	// 95分位数
	Percentile_95 *float64 `json:"percentile95,optional"`
	// 99分位数
	Percentile_99 *float64 `json:"percentile99,optional"`
	// 扩展统计数据
	ExtraData *string `json:"extraData,optional"`
}

// The response data of analytics daily summary list | AnalyticsDailySummary信息列表数据
// swagger:model AnalyticsDailySummaryListResp
type AnalyticsDailySummaryListResp struct {
	BaseDataInfo
	// AnalyticsDailySummary list data | AnalyticsDailySummary信息列表数据
	Data AnalyticsDailySummaryListInfo `json:"data"`
}

// The analytics daily summary list data | AnalyticsDailySummary信息列表数据
// swagger:model AnalyticsDailySummaryListInfo
type AnalyticsDailySummaryListInfo struct {
	BaseListInfo
	// The analytics daily summary list data | AnalyticsDailySummary信息列表数据
	Data []AnalyticsDailySummaryInfo `json:"data"`
}

// Get analytics daily summary list request params | AnalyticsDailySummary列表请求参数
// swagger:model AnalyticsDailySummaryListReq
type AnalyticsDailySummaryListReq struct {
	PageInfo
	// CreatedAt
	CreatedAt *int64 `json:"createdAt,optional"`
	// UpdatedAt
	UpdatedAt *int64 `json:"updatedAt,optional"`
	// DeletedAt
	DeletedAt *int64 `json:"deletedAt,optional"`
	// AppId
	AppId *uint64 `json:"appId,optional"`
	// Date
	Date *int64 `json:"date,optional"`
	// MetricType
	MetricType *string `json:"metricType,optional"`
	// MetricName
	MetricName *string `json:"metricName,optional"`
	// Dimension
	Dimension *string `json:"dimension,optional"`
	// DimensionValue
	DimensionValue *string `json:"dimensionValue,optional"`
	// TotalCount
	TotalCount *uint64 `json:"totalCount,optional"`
	// UniqueCount
	UniqueCount *uint64 `json:"uniqueCount,optional"`
	// SumValue
	SumValue *float64 `json:"sumValue,optional"`
	// AvgValue
	AvgValue *float64 `json:"avgValue,optional"`
	// MinValue
	MinValue *float64 `json:"minValue,optional"`
	// MaxValue
	MaxValue *float64 `json:"maxValue,optional"`
	// Percentile_50
	Percentile_50 *float64 `json:"percentile50,optional"`
	// Percentile_90
	Percentile_90 *float64 `json:"percentile90,optional"`
	// Percentile_95
	Percentile_95 *float64 `json:"percentile95,optional"`
	// Percentile_99
	Percentile_99 *float64 `json:"percentile99,optional"`
	// ExtraData
	ExtraData *string `json:"extraData,optional"`
}

// The analytics daily summary information response | AnalyticsDailySummary信息返回体
// swagger:model AnalyticsDailySummaryInfoResp
type AnalyticsDailySummaryInfoResp struct {
	BaseDataInfo
	// The analytics daily summary information | AnalyticsDailySummary信息数据
	Data AnalyticsDailySummaryInfo `json:"data"`
}

// The response data of analytics device information | AnalyticsDevice信息
// swagger:model AnalyticsDeviceInfo
type AnalyticsDeviceInfo struct {
	BaseIDInfo
	// 应用ID
	AppId *uint64 `json:"appId,optional"`
	// 设备ID
	DeviceId *string `json:"deviceId,optional"`
	// 设备类型: mobile, tablet, desktop, tv
	DeviceType *string `json:"deviceType,optional"`
	// 平台: web, android, ios, desktop
	Platform *string `json:"platform,optional"`
	// 操作系统名称
	OsName *string `json:"osName,optional"`
	// 操作系统版本
	OsVersion *string `json:"osVersion,optional"`
	// 浏览器名称
	BrowserName *string `json:"browserName,optional"`
	// 浏览器版本
	BrowserVersion *string `json:"browserVersion,optional"`
	// 设备型号
	DeviceModel *string `json:"deviceModel,optional"`
	// 设备品牌
	DeviceBrand *string `json:"deviceBrand,optional"`
	// 屏幕宽度
	ScreenWidth *uint32 `json:"screenWidth,optional"`
	// 屏幕高度
	ScreenHeight *uint32 `json:"screenHeight,optional"`
	// 屏幕密度
	ScreenDensity *float64 `json:"screenDensity,optional"`
	// 网络类型: wifi, 4g, 5g, ethernet
	NetworkType *string `json:"networkType,optional"`
	// 运营商
	Carrier *string `json:"carrier,optional"`
	// 国家
	Country *string `json:"country,optional"`
	// 省份
	Province *string `json:"province,optional"`
	// 城市
	City *string `json:"city,optional"`
	// 应用版本
	AppVersion *string `json:"appVersion,optional"`
	// SDK版本
	SdkVersion *string `json:"sdkVersion,optional"`
	// 自定义设备属性
	Properties *string `json:"properties,optional"`
	// 首次出现时间戳(毫秒)
	FirstSeenTime *int64 `json:"firstSeenTime,optional"`
	// 最后出现时间戳(毫秒)
	LastSeenTime *int64 `json:"lastSeenTime,optional"`
	// 总会话数
	TotalSessions *uint32 `json:"totalSessions,optional"`
	// 总事件数
	TotalEvents *uint32 `json:"totalEvents,optional"`
}

// The response data of analytics device list | AnalyticsDevice信息列表数据
// swagger:model AnalyticsDeviceListResp
type AnalyticsDeviceListResp struct {
	BaseDataInfo
	// AnalyticsDevice list data | AnalyticsDevice信息列表数据
	Data AnalyticsDeviceListInfo `json:"data"`
}

// The analytics device list data | AnalyticsDevice信息列表数据
// swagger:model AnalyticsDeviceListInfo
type AnalyticsDeviceListInfo struct {
	BaseListInfo
	// The analytics device list data | AnalyticsDevice信息列表数据
	Data []AnalyticsDeviceInfo `json:"data"`
}

// Get analytics device list request params | AnalyticsDevice列表请求参数
// swagger:model AnalyticsDeviceListReq
type AnalyticsDeviceListReq struct {
	PageInfo
	// CreatedAt
	CreatedAt *int64 `json:"createdAt,optional"`
	// UpdatedAt
	UpdatedAt *int64 `json:"updatedAt,optional"`
	// DeletedAt
	DeletedAt *int64 `json:"deletedAt,optional"`
	// AppId
	AppId *uint64 `json:"appId,optional"`
	// DeviceId
	DeviceId *string `json:"deviceId,optional"`
	// DeviceType
	DeviceType *string `json:"deviceType,optional"`
	// Platform
	Platform *string `json:"platform,optional"`
	// OsName
	OsName *string `json:"osName,optional"`
	// OsVersion
	OsVersion *string `json:"osVersion,optional"`
	// BrowserName
	BrowserName *string `json:"browserName,optional"`
	// BrowserVersion
	BrowserVersion *string `json:"browserVersion,optional"`
	// DeviceModel
	DeviceModel *string `json:"deviceModel,optional"`
	// DeviceBrand
	DeviceBrand *string `json:"deviceBrand,optional"`
	// ScreenWidth
	ScreenWidth *uint32 `json:"screenWidth,optional"`
	// ScreenHeight
	ScreenHeight *uint32 `json:"screenHeight,optional"`
	// ScreenDensity
	ScreenDensity *float64 `json:"screenDensity,optional"`
	// NetworkType
	NetworkType *string `json:"networkType,optional"`
	// Carrier
	Carrier *string `json:"carrier,optional"`
	// Country
	Country *string `json:"country,optional"`
}

// The analytics device information response | AnalyticsDevice信息返回体
// swagger:model AnalyticsDeviceInfoResp
type AnalyticsDeviceInfoResp struct {
	BaseDataInfo
	// The analytics device information | AnalyticsDevice信息数据
	Data AnalyticsDeviceInfo `json:"data"`
}

// The response data of analytics event information | AnalyticsEvent信息
// swagger:model AnalyticsEventInfo
type AnalyticsEventInfo struct {
	BaseIDInfo
	// 应用ID
	AppId *uint64 `json:"appId,optional"`
	// 用户ID (可选)
	UserId *uint64 `json:"userId,optional"`
	// 设备ID
	DeviceId *string `json:"deviceId,optional"`
	// 事件名称
	EventName *string `json:"eventName,optional"`
	// 事件类型: user_action, business_event, system_event, marketing_event
	EventType *string `json:"eventType,optional"`
	// 事件属性(JSON格式)
	EventProperties *string `json:"eventProperties,optional"`
	// 用户属性(JSON格式)
	UserProperties *string `json:"userProperties,optional"`
	// 事件发生时间戳(毫秒)
	Timestamp *int64 `json:"timestamp,optional"`
	// IP地址
	IpAddress *string `json:"ipAddress,optional"`
	// 用户代理字符串
	UserAgent *string `json:"userAgent,optional"`
	// 平台: web, android, ios, desktop
	Platform *string `json:"platform,optional"`
	// 应用版本
	AppVersion *string `json:"appVersion,optional"`
	// 操作系统版本
	OsVersion *string `json:"osVersion,optional"`
	// 国家
	Country *string `json:"country,optional"`
	// 省份
	Province *string `json:"province,optional"`
	// 城市
	City *string `json:"city,optional"`
	// 纬度
	Latitude *float64 `json:"latitude,optional"`
	// 经度
	Longitude *float64 `json:"longitude,optional"`
	// 页面URL
	PageUrl *string `json:"pageUrl,optional"`
	// 页面标题
	PageTitle *string `json:"pageTitle,optional"`
	// 引荐页面
	Referrer *string `json:"referrer,optional"`
	// UTM来源
	UtmSource *string `json:"utmSource,optional"`
	// UTM媒介
	UtmMedium *string `json:"utmMedium,optional"`
	// UTM活动
	UtmCampaign *string `json:"utmCampaign,optional"`
	// UTM内容
	UtmContent *string `json:"utmContent,optional"`
	// UTM词条
	UtmTerm *string `json:"utmTerm,optional"`
	// 持续时长(毫秒) - 前端计算传入
	Duration *int64 `json:"duration,optional"`
	// 会话持续时长(毫秒) - 前端计算传入
	SessionDuration *int64 `json:"sessionDuration,optional"`
	// 页面停留时长(毫秒) - 前端计算传入
	PageViewDuration *int64 `json:"pageViewDuration,optional"`
}

// The response data of analytics event list | AnalyticsEvent信息列表数据
// swagger:model AnalyticsEventListResp
type AnalyticsEventListResp struct {
	BaseDataInfo
	// AnalyticsEvent list data | AnalyticsEvent信息列表数据
	Data AnalyticsEventListInfo `json:"data"`
}

// The analytics event list data | AnalyticsEvent信息列表数据
// swagger:model AnalyticsEventListInfo
type AnalyticsEventListInfo struct {
	BaseListInfo
	// The analytics event list data | AnalyticsEvent信息列表数据
	Data []AnalyticsEventInfo `json:"data"`
}

// Get analytics event list request params | AnalyticsEvent列表请求参数
// swagger:model AnalyticsEventListReq
type AnalyticsEventListReq struct {
	PageInfo
	// CreatedAt
	CreatedAt *int64 `json:"createdAt,optional"`
	// UpdatedAt
	UpdatedAt *int64 `json:"updatedAt,optional"`
	// DeletedAt
	DeletedAt *int64 `json:"deletedAt,optional"`
	// AppId
	AppId *uint64 `json:"appId,optional"`
	// UserId
	UserId *uint64 `json:"userId,optional"`
	// DeviceId
	DeviceId *string `json:"deviceId,optional"`
	// EventName
	EventName *string `json:"eventName,optional"`
	// EventType
	EventType *string `json:"eventType,optional"`
	// EventProperties
	EventProperties *string `json:"eventProperties,optional"`
	// UserProperties
	UserProperties *string `json:"userProperties,optional"`
	// Timestamp
	Timestamp *int64 `json:"timestamp,optional"`
	// IpAddress
	IpAddress *string `json:"ipAddress,optional"`
	// UserAgent
	UserAgent *string `json:"userAgent,optional"`
	// Platform
	Platform *string `json:"platform,optional"`
	// AppVersion
	AppVersion *string `json:"appVersion,optional"`
	// OsVersion
	OsVersion *string `json:"osVersion,optional"`
	// Country
	Country *string `json:"country,optional"`
	// Province
	Province *string `json:"province,optional"`
	// City
	City *string `json:"city,optional"`
}

// The analytics event information response | AnalyticsEvent信息返回体
// swagger:model AnalyticsEventInfoResp
type AnalyticsEventInfoResp struct {
	BaseDataInfo
	// The analytics event information | AnalyticsEvent信息数据
	Data AnalyticsEventInfo `json:"data"`
}

// The response data of analytics event definition information | AnalyticsEventDefinition信息
// swagger:model AnalyticsEventDefinitionInfo
type AnalyticsEventDefinitionInfo struct {
	BaseIDInfo
	// 应用ID
	AppId *uint64 `json:"appId,optional"`
	// 事件名称
	EventName *string `json:"eventName,optional"`
	// 事件类型
	EventType *string `json:"eventType,optional"`
	// 显示名称
	DisplayName *string `json:"displayName,optional"`
	// 事件描述
	Description *string `json:"description,optional"`
	// 状态: 0-禁用, 1-启用
	Status *uint32 `json:"status,optional"`
	// 是否自定义事件: 0-系统预定义, 1-用户自定义
	IsCustom *int32 `json:"isCustom,optional"`
	// 属性结构定义
	PropertiesSchema *string `json:"propertiesSchema,optional"`
	// 采样率: 0.0001-1.0000
	SamplingRate *float64 `json:"samplingRate,optional"`
	// 总触发次数
	TotalCount *uint64 `json:"totalCount,optional"`
	// 最后触发时间戳(毫秒)
	LastTriggeredTime *int64 `json:"lastTriggeredTime,optional"`
}

// The response data of analytics event definition list | AnalyticsEventDefinition信息列表数据
// swagger:model AnalyticsEventDefinitionListResp
type AnalyticsEventDefinitionListResp struct {
	BaseDataInfo
	// AnalyticsEventDefinition list data | AnalyticsEventDefinition信息列表数据
	Data AnalyticsEventDefinitionListInfo `json:"data"`
}

// The analytics event definition list data | AnalyticsEventDefinition信息列表数据
// swagger:model AnalyticsEventDefinitionListInfo
type AnalyticsEventDefinitionListInfo struct {
	BaseListInfo
	// The analytics event definition list data | AnalyticsEventDefinition信息列表数据
	Data []AnalyticsEventDefinitionInfo `json:"data"`
}

// Get analytics event definition list request params | AnalyticsEventDefinition列表请求参数
// swagger:model AnalyticsEventDefinitionListReq
type AnalyticsEventDefinitionListReq struct {
	PageInfo
	// CreatedAt
	CreatedAt *int64 `json:"createdAt,optional"`
	// UpdatedAt
	UpdatedAt *int64 `json:"updatedAt,optional"`
	// DeletedAt
	DeletedAt *int64 `json:"deletedAt,optional"`
	// AppId
	AppId *uint64 `json:"appId,optional"`
	// EventName
	EventName *string `json:"eventName,optional"`
	// EventType
	EventType *string `json:"eventType,optional"`
	// DisplayName
	DisplayName *string `json:"displayName,optional"`
	// Description
	Description *string `json:"description,optional"`
	// Status
	Status *uint32 `json:"status,optional"`
	// IsCustom
	IsCustom *int32 `json:"isCustom,optional"`
	// PropertiesSchema
	PropertiesSchema *string `json:"propertiesSchema,optional"`
	// SamplingRate
	SamplingRate *float64 `json:"samplingRate,optional"`
	// TotalCount
	TotalCount *uint64 `json:"totalCount,optional"`
	// LastTriggeredTime
	LastTriggeredTime *int64 `json:"lastTriggeredTime,optional"`
}

// The analytics event definition information response | AnalyticsEventDefinition信息返回体
// swagger:model AnalyticsEventDefinitionInfoResp
type AnalyticsEventDefinitionInfoResp struct {
	BaseDataInfo
	// The analytics event definition information | AnalyticsEventDefinition信息数据
	Data AnalyticsEventDefinitionInfo `json:"data"`
}

// The response data of analytics real time metric information | AnalyticsRealTimeMetric信息
// swagger:model AnalyticsRealTimeMetricInfo
type AnalyticsRealTimeMetricInfo struct {
	BaseIDInfo
	// 应用ID
	AppId *uint64 `json:"appId,optional"`
	// 指标类型: active_users, event_count, session_count等
	MetricType *string `json:"metricType,optional"`
	// 指标名称
	MetricName *string `json:"metricName,optional"`
	// 时间窗口(秒): 60, 300, 900, 1800, 3600
	TimeWindow *uint32 `json:"timeWindow,optional"`
	// 窗口开始时间戳(毫秒)
	WindowStartTime *int64 `json:"windowStartTime,optional"`
	// 窗口结束时间戳(毫秒)
	WindowEndTime *int64 `json:"windowEndTime,optional"`
	// 维度: platform, country, version等
	Dimension *string `json:"dimension,optional"`
	// 维度值
	DimensionValue *string `json:"dimensionValue,optional"`
	// 指标值
	MetricValue *float64 `json:"metricValue,optional"`
	// 计数
	Count *uint64 `json:"count,optional"`
	// 求和值
	SumValue *float64 `json:"sumValue,optional"`
	// 平均值
	AvgValue *float64 `json:"avgValue,optional"`
	// 最小值
	MinValue *float64 `json:"minValue,optional"`
	// 最大值
	MaxValue *float64 `json:"maxValue,optional"`
	// 扩展指标数据
	ExtraData *string `json:"extraData,optional"`
}

// The response data of analytics real time metric list | AnalyticsRealTimeMetric信息列表数据
// swagger:model AnalyticsRealTimeMetricListResp
type AnalyticsRealTimeMetricListResp struct {
	BaseDataInfo
	// AnalyticsRealTimeMetric list data | AnalyticsRealTimeMetric信息列表数据
	Data AnalyticsRealTimeMetricListInfo `json:"data"`
}

// The analytics real time metric list data | AnalyticsRealTimeMetric信息列表数据
// swagger:model AnalyticsRealTimeMetricListInfo
type AnalyticsRealTimeMetricListInfo struct {
	BaseListInfo
	// The analytics real time metric list data | AnalyticsRealTimeMetric信息列表数据
	Data []AnalyticsRealTimeMetricInfo `json:"data"`
}

// Get analytics real time metric list request params | AnalyticsRealTimeMetric列表请求参数
// swagger:model AnalyticsRealTimeMetricListReq
type AnalyticsRealTimeMetricListReq struct {
	PageInfo
	// CreatedAt
	CreatedAt *int64 `json:"createdAt,optional"`
	// UpdatedAt
	UpdatedAt *int64 `json:"updatedAt,optional"`
	// DeletedAt
	DeletedAt *int64 `json:"deletedAt,optional"`
	// AppId
	AppId *uint64 `json:"appId,optional"`
	// MetricType
	MetricType *string `json:"metricType,optional"`
	// MetricName
	MetricName *string `json:"metricName,optional"`
	// TimeWindow
	TimeWindow *uint32 `json:"timeWindow,optional"`
	// WindowStartTime
	WindowStartTime *int64 `json:"windowStartTime,optional"`
	// WindowEndTime
	WindowEndTime *int64 `json:"windowEndTime,optional"`
	// Dimension
	Dimension *string `json:"dimension,optional"`
	// DimensionValue
	DimensionValue *string `json:"dimensionValue,optional"`
	// MetricValue
	MetricValue *float64 `json:"metricValue,optional"`
	// Count
	Count *uint64 `json:"count,optional"`
	// SumValue
	SumValue *float64 `json:"sumValue,optional"`
	// AvgValue
	AvgValue *float64 `json:"avgValue,optional"`
	// MinValue
	MinValue *float64 `json:"minValue,optional"`
	// MaxValue
	MaxValue *float64 `json:"maxValue,optional"`
	// ExtraData
	ExtraData *string `json:"extraData,optional"`
}

// The analytics real time metric information response | AnalyticsRealTimeMetric信息返回体
// swagger:model AnalyticsRealTimeMetricInfoResp
type AnalyticsRealTimeMetricInfoResp struct {
	BaseDataInfo
	// The analytics real time metric information | AnalyticsRealTimeMetric信息数据
	Data AnalyticsRealTimeMetricInfo `json:"data"`
}

// The response data of analytics user information | AnalyticsUser信息
// swagger:model AnalyticsUserInfo
type AnalyticsUserInfo struct {
	BaseIDInfo
	// 应用ID
	AppId *uint64 `json:"appId,optional"`
	// 用户ID
	UserId *uint64 `json:"userId,optional"`
	// 用户名
	Username *string `json:"username,optional"`
	// 邮箱
	Email *string `json:"email,optional"`
	// 手机号
	Phone *string `json:"phone,optional"`
	// 头像URL
	Avatar *string `json:"avatar,optional"`
	// 性别: 0-未知, 1-男, 2-女
	Gender *int32 `json:"gender,optional"`
	// 生日
	Birthday *int64 `json:"birthday,optional"`
	// 年龄
	Age *int32 `json:"age,optional"`
	// 国家
	Country *string `json:"country,optional"`
	// 省份
	Province *string `json:"province,optional"`
	// 城市
	City *string `json:"city,optional"`
	// 用户标签
	Tags *string `json:"tags,optional"`
	// 自定义用户属性
	Properties *string `json:"properties,optional"`
	// 首次访问时间戳(毫秒)
	FirstVisitTime *int64 `json:"firstVisitTime,optional"`
	// 最后访问时间戳(毫秒)
	LastVisitTime *int64 `json:"lastVisitTime,optional"`
	// 总会话数
	TotalSessions *uint32 `json:"totalSessions,optional"`
	// 总事件数
	TotalEvents *uint32 `json:"totalEvents,optional"`
	// 总使用时长(毫秒)
	TotalDuration *int64 `json:"totalDuration,optional"`
}

// The response data of analytics user list | AnalyticsUser信息列表数据
// swagger:model AnalyticsUserListResp
type AnalyticsUserListResp struct {
	BaseDataInfo
	// AnalyticsUser list data | AnalyticsUser信息列表数据
	Data AnalyticsUserListInfo `json:"data"`
}

// The analytics user list data | AnalyticsUser信息列表数据
// swagger:model AnalyticsUserListInfo
type AnalyticsUserListInfo struct {
	BaseListInfo
	// The analytics user list data | AnalyticsUser信息列表数据
	Data []AnalyticsUserInfo `json:"data"`
}

// Get analytics user list request params | AnalyticsUser列表请求参数
// swagger:model AnalyticsUserListReq
type AnalyticsUserListReq struct {
	PageInfo
	// CreatedAt
	CreatedAt *int64 `json:"createdAt,optional"`
	// UpdatedAt
	UpdatedAt *int64 `json:"updatedAt,optional"`
	// DeletedAt
	DeletedAt *int64 `json:"deletedAt,optional"`
	// AppId
	AppId *uint64 `json:"appId,optional"`
	// UserId
	UserId *uint64 `json:"userId,optional"`
	// Username
	Username *string `json:"username,optional"`
	// Email
	Email *string `json:"email,optional"`
	// Phone
	Phone *string `json:"phone,optional"`
	// Avatar
	Avatar *string `json:"avatar,optional"`
	// Gender
	Gender *int32 `json:"gender,optional"`
	// Birthday
	Birthday *int64 `json:"birthday,optional"`
	// Age
	Age *int32 `json:"age,optional"`
	// Country
	Country *string `json:"country,optional"`
	// Province
	Province *string `json:"province,optional"`
	// City
	City *string `json:"city,optional"`
	// Tags
	Tags *string `json:"tags,optional"`
	// Properties
	Properties *string `json:"properties,optional"`
	// FirstVisitTime
	FirstVisitTime *int64 `json:"firstVisitTime,optional"`
	// LastVisitTime
	LastVisitTime *int64 `json:"lastVisitTime,optional"`
}

// The analytics user information response | AnalyticsUser信息返回体
// swagger:model AnalyticsUserInfoResp
type AnalyticsUserInfoResp struct {
	BaseDataInfo
	// The analytics user information | AnalyticsUser信息数据
	Data AnalyticsUserInfo `json:"data"`
}

// The response data of rental device information | RentalDevice信息
// swagger:model RentalDeviceInfo
type RentalDeviceInfo struct {
	BaseIDInfo
	// 设备序列号SN
	SerialNumber *string `json:"serialNumber,optional"`
	// 设备描述
	Description *string `json:"description,optional"`
	// 设备状态:0禁用,1启用
	DeviceStatus *uint32 `json:"deviceStatus,optional"`
	// 额外信息JSON
	ExtraInfo *string `json:"extraInfo,optional"`
}

// The response data of rental device list | RentalDevice信息列表数据
// swagger:model RentalDeviceListResp
type RentalDeviceListResp struct {
	BaseDataInfo
	// RentalDevice list data | RentalDevice信息列表数据
	Data RentalDeviceListInfo `json:"data"`
}

// The rental device list data | RentalDevice信息列表数据
// swagger:model RentalDeviceListInfo
type RentalDeviceListInfo struct {
	BaseListInfo
	// The rental device list data | RentalDevice信息列表数据
	Data []RentalDeviceInfo `json:"data"`
}

// Get rental device list request params | RentalDevice列表请求参数
// swagger:model RentalDeviceListReq
type RentalDeviceListReq struct {
	PageInfo
	// CreatedAt
	CreatedAt *int64 `json:"createdAt,optional"`
	// UpdatedAt
	UpdatedAt *int64 `json:"updatedAt,optional"`
	// DeletedAt
	DeletedAt *int64 `json:"deletedAt,optional"`
	// SerialNumber
	SerialNumber *string `json:"serialNumber,optional"`
	// Description
	Description *string `json:"description,optional"`
	// DeviceStatus
	DeviceStatus *uint32 `json:"deviceStatus,optional"`
	// ExtraInfo
	ExtraInfo *string `json:"extraInfo,optional"`
}

// The rental device information response | RentalDevice信息返回体
// swagger:model RentalDeviceInfoResp
type RentalDeviceInfoResp struct {
	BaseDataInfo
	// The rental device information | RentalDevice信息数据
	Data RentalDeviceInfo `json:"data"`
}

// The response data of rental device message information | RentalDeviceMessage信息
// swagger:model RentalDeviceMessageInfo
type RentalDeviceMessageInfo struct {
	BaseIDInfo
	// 设备序列号SN
	SerialNumber *string `json:"serialNumber,optional"`
	// 消息类型:1普通消息,2上锁指令,3解锁指令,4租赁提醒,5归还提醒
	MessageType *int32 `json:"messageType,optional"`
	// 消息标题
	MessageTitle *string `json:"messageTitle,optional"`
	// 消息内容
	MessageContent *string `json:"messageContent,optional"`
	// 状态:0待发送,1已发送,2已读,3已过期
	Status *uint32 `json:"status,optional"`
	// 过期时间(48小时后)
	ExpireTime *int64 `json:"expireTime,optional"`
	// 发送时间
	SendTime *int64 `json:"sendTime,optional"`
	// 阅读时间
	ReadTime *int64 `json:"readTime,optional"`
	// 发送者ID
	SenderId *string `json:"senderId,optional"`
	// 额外数据JSON
	ExtraData *string `json:"extraData,optional"`
}

// The response data of rental device message list | RentalDeviceMessage信息列表数据
// swagger:model RentalDeviceMessageListResp
type RentalDeviceMessageListResp struct {
	BaseDataInfo
	// RentalDeviceMessage list data | RentalDeviceMessage信息列表数据
	Data RentalDeviceMessageListInfo `json:"data"`
}

// The rental device message list data | RentalDeviceMessage信息列表数据
// swagger:model RentalDeviceMessageListInfo
type RentalDeviceMessageListInfo struct {
	BaseListInfo
	// The rental device message list data | RentalDeviceMessage信息列表数据
	Data []RentalDeviceMessageInfo `json:"data"`
}

// Get rental device message list request params | RentalDeviceMessage列表请求参数
// swagger:model RentalDeviceMessageListReq
type RentalDeviceMessageListReq struct {
	PageInfo
	// CreatedAt
	CreatedAt *int64 `json:"createdAt,optional"`
	// UpdatedAt
	UpdatedAt *int64 `json:"updatedAt,optional"`
	// DeletedAt
	DeletedAt *int64 `json:"deletedAt,optional"`
	// SerialNumber
	SerialNumber *string `json:"serialNumber,optional"`
	// MessageType
	MessageType *int32 `json:"messageType,optional"`
	// MessageTitle
	MessageTitle *string `json:"messageTitle,optional"`
	// MessageContent
	MessageContent *string `json:"messageContent,optional"`
	// Status
	Status *uint32 `json:"status,optional"`
	// ExpireTime
	ExpireTime *int64 `json:"expireTime,optional"`
	// SendTime
	SendTime *int64 `json:"sendTime,optional"`
	// ReadTime
	ReadTime *int64 `json:"readTime,optional"`
	// SenderId
	SenderId *string `json:"senderId,optional"`
	// ExtraData
	ExtraData *string `json:"extraData,optional"`
}

// The rental device message information response | RentalDeviceMessage信息返回体
// swagger:model RentalDeviceMessageInfoResp
type RentalDeviceMessageInfoResp struct {
	BaseDataInfo
	// The rental device message information | RentalDeviceMessage信息数据
	Data RentalDeviceMessageInfo `json:"data"`
}
