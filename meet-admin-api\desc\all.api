import "base.api"
import "./health/health.api"
import "./syncService/sync.api"
import "./meetgrouprpc/group_follow.api"
import "./meetgrouprpc/group_member.api"
import "./meetgrouprpc/group.api"
import "./meettagrpc/tag.api"
import "./meettagrpc/tag_record.api"
import "./meetsaleservicerpc/product.api"
import "./meetsaleservicerpc/mainten_info.api"
import "./meetsaleservicerpc/mainten_order.api"
import "./meetgeorpc/province.api"
import "./meetgeorpc/city.api"
import "./meetgeorpc/area.api"
import "./meetgeorpc/street.api"
import "./meetusercenterrpc/user.api"
import "./meetadrpc/banner.api"
import "./meetarticlerpc/article.api"
import "./meetarticlerpc/segment.api"
import "./meetusercenterrpc/user_addres.api"
import "./meetarticlerpc/segment_article_record.api"
import "./meetreplyrpc/reply.api"
import "./meetappversionrpc/app_version.api"
import "./meetusercenterrpc/approle.api"
import "./meetusercenterrpc/appapi.api"
import "./meetusercenterrpc/authority.api"
import "./meetsaleservicerpc/mall_order.api"
import "./meetsaleservicerpc/mall_order_item.api"
import "./meetsaleservicerpc/mall_order_log.api"
import "./meetsaleservicerpc/mall_product.api"
import "./meetsaleservicerpc/mall_product_category.api"
import "./meetsaleservicerpc/mall_product_quota.api"
import "./meetsaleservicerpc/mall_product_sku.api"
import "./meetsaleservicerpc/mall_subscription.api"
import "./meetsaleservicerpc/mall_usage_record.api"
import "./meetsaleservicerpc/mall_refund_express.api"
import "./meetsaleservicerpc/mall_delivery.api"
import "./meetsaleservicerpc/mall_payment.api"
import "./meetsaleservicerpc/mall_payment_log.api"
import "./meetsaleservicerpc/mall_refund.api"
import "./meetsaleservicerpc/mall_license.api"
import "./meetsaleservicerpc/mall_license_device.api"
import "./meetsaleservicerpc/mall_supplier.api"
import "./meetsaleservicerpc/mall_supplier_commission.api"
import "./meetsaleservicerpc/mall_application.api"
import "./meetgeorpc/bolo_lexicon.api"
import "./analytics/analytics.api"
import "./meetanalyticsrpc/analytics_application.api"
import "./meetanalyticsrpc/analytics_cohort.api"
import "./meetanalyticsrpc/analytics_daily_summary.api"
import "./meetanalyticsrpc/analytics_device.api"
import "./meetanalyticsrpc/analytics_event.api"
import "./meetanalyticsrpc/analytics_event_definition.api"
import "./meetanalyticsrpc/analytics_real_time_metric.api"
import "./meetanalyticsrpc/analytics_user.api"
import "./meetsaleservicerpc/rental_device.api"
import "./meetsaleservicerpc/rental_device_message.api"