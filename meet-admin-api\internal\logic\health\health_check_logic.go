package health

import (
	"context"
	"time"

	"seevision.cn/server/meet-admin-api/internal/svc"
	"seevision.cn/server/meet-admin-api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type HealthCheckLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewHealthCheckLogic(ctx context.Context, svcCtx *svc.ServiceContext) *HealthCheckLogic {
	return &HealthCheckLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx}
}

func (l *HealthCheckLogic) HealthCheck() (resp *types.HealthCheckResp, err error) {
	return &types.HealthCheckResp{
		Status:    "ok",
		Timestamp: time.Now().Unix(),
		Service:   "meet-admin-api",
		Version:   "v1.0.0",
		Checks: map[string]string{
			"database": "ok",
			"redis":    "ok",
		},
	}, nil
}
