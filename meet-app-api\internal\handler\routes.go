// Code generated by goctl. DO NOT EDIT.
package handler

import (
	"net/http"

	admin "seevision.cn/server/meet-app-api/internal/handler/admin"
	anonymousmallorder "seevision.cn/server/meet-app-api/internal/handler/anonymousmallorder"
	appversion "seevision.cn/server/meet-app-api/internal/handler/appversion"
	area "seevision.cn/server/meet-app-api/internal/handler/area"
	article "seevision.cn/server/meet-app-api/internal/handler/article"
	banner "seevision.cn/server/meet-app-api/internal/handler/banner"
	bololexicon "seevision.cn/server/meet-app-api/internal/handler/bololexicon"
	city "seevision.cn/server/meet-app-api/internal/handler/city"
	group "seevision.cn/server/meet-app-api/internal/handler/group"
	groupfollow "seevision.cn/server/meet-app-api/internal/handler/groupfollow"
	groupmember "seevision.cn/server/meet-app-api/internal/handler/groupmember"
	health "seevision.cn/server/meet-app-api/internal/handler/health"
	mainteninfo "seevision.cn/server/meet-app-api/internal/handler/mainteninfo"
	malllicense "seevision.cn/server/meet-app-api/internal/handler/malllicense"
	mallorder "seevision.cn/server/meet-app-api/internal/handler/mallorder"
	mallorderitem "seevision.cn/server/meet-app-api/internal/handler/mallorderitem"
	mallproduct "seevision.cn/server/meet-app-api/internal/handler/mallproduct"
	mallproductsku "seevision.cn/server/meet-app-api/internal/handler/mallproductsku"
	payment "seevision.cn/server/meet-app-api/internal/handler/payment"
	product "seevision.cn/server/meet-app-api/internal/handler/product"
	province "seevision.cn/server/meet-app-api/internal/handler/province"
	publicapi "seevision.cn/server/meet-app-api/internal/handler/publicapi"
	rentaldevice "seevision.cn/server/meet-app-api/internal/handler/rentaldevice"
	segment "seevision.cn/server/meet-app-api/internal/handler/segment"
	segmentarticlerecord "seevision.cn/server/meet-app-api/internal/handler/segmentarticlerecord"
	street "seevision.cn/server/meet-app-api/internal/handler/street"
	tag "seevision.cn/server/meet-app-api/internal/handler/tag"
	tagfollow "seevision.cn/server/meet-app-api/internal/handler/tagfollow"
	user "seevision.cn/server/meet-app-api/internal/handler/user"
	useraddres "seevision.cn/server/meet-app-api/internal/handler/useraddres"
	useragreement "seevision.cn/server/meet-app-api/internal/handler/useragreement"
	"seevision.cn/server/meet-app-api/internal/svc"

	"github.com/zeromicro/go-zero/rest"
)

func RegisterHandlers(server *rest.Server, serverCtx *svc.ServiceContext) {
	server.AddRoutes(
		[]rest.Route{},
		rest.WithPrefix("/app/v1"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodGet,
				Path:    "/health",
				Handler: health.HealthCheckHandler(serverCtx),
			},
		},
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodGet,
				Path:    "/configuration",
				Handler: publicapi.GetPublicSystemConfigurationHandler(serverCtx),
			},
		},
		rest.WithPrefix("/app/v1"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/verification",
				Handler: user.VerificationHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/geetestVerify",
				Handler: user.GeetestVerifyHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/user/register",
				Handler: user.RegisterHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/user/login",
				Handler: user.LoginHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/user/verifylogin",
				Handler: user.VerifyLoginHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/user/verifyloginByAuthType",
				Handler: user.VerifyLoginByAuthTypeHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/user/wxMiniAuth",
				Handler: user.WxMiniAuthHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/user/registerByAuthId",
				Handler: user.RegisterByAuthIdHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/user/bindWxByMobile",
				Handler: user.BindWxByMobileHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/user/wxAuth",
				Handler: user.WxAuthHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/user/getcode",
				Handler: user.GetSendSmsCodeHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/user/session/getTicket",
				Handler: user.GetUserMobileSsoTicketHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/user/session/getMobileSSOUserLoginInfo",
				Handler: user.GetUserMobileSsoUserLoginInfoHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/user/email/code",
				Handler: user.SendEmailCodeHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/user/email/code/verify",
				Handler: user.VerifiedEmailCodeHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/user/im/getUserSig",
				Handler: user.GetUserSigHandler(serverCtx),
			},
		},
		rest.WithPrefix("/v1"),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.CustomAuthority},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/user/getUserInfoByUserId",
					Handler: user.GetUserInfoByUserIdHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/user/getUserInfoByMobile",
					Handler: user.GetUserInfoByMobileHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/v1"),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Authority},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/user/detail",
					Handler: user.DetailHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/user/getUserInfoByName",
					Handler: user.GetUserInfoByNameHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/user/follow",
					Handler: user.FollowHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/user/unfollow",
					Handler: user.UnFollowHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/user/followlists",
					Handler: user.FollowListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/user/fanslists",
					Handler: user.FansListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/user/thumbup",
					Handler: user.ThumbupHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/user/isthumbup",
					Handler: user.IsThumbupHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/user/setPassword",
					Handler: user.SetPasswordHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/user/updatePassword",
					Handler: user.UpdatePasswordHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/user/setNickname",
					Handler: user.SetNicknameHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/user/setAvatar",
					Handler: user.SetAvatarHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/user/setBackgroundImage",
					Handler: user.SetBackgroundImageHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/user/setUserInfo",
					Handler: user.SetUserInfoHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/user/setUserSex",
					Handler: user.SetUserSexHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/user/setUserHomePage",
					Handler: user.SetUserHomePageHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/user/setUserDescription",
					Handler: user.SetUserDescriptionHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/user/msessage/read",
					Handler: user.ReadMessageHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/user/message/lists",
					Handler: user.GetMessageListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/user/product/add",
					Handler: user.AddUserProductHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/user/product/lists",
					Handler: user.GetUserProductListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/user/session/create",
					Handler: user.CreateUserMobileSsoLoginSessionHandler(serverCtx),
				},
				{
					Method:  http.MethodDelete,
					Path:    "/user/session/delete",
					Handler: user.DeleteUserMobileSsoLoginSessionHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/user/perm",
					Handler: user.GetUserPermCodeHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/user/email/set",
					Handler: user.SetUserEmailHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/user/realname",
					Handler: user.RealNameAuthenticationHandler(serverCtx),
				},
			}...,
		),
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
		rest.WithPrefix("/v1"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/article/content/image/auditedCallback",
				Handler: article.ImageAuditedCallbackHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/article/content/video/auditedCallback",
				Handler: article.VideoAuditedCallbackHandler(serverCtx),
			},
		},
		rest.WithPrefix("/v1"),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.CustomAuthority},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/article/detail",
					Handler: article.ArticleDetailHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/article/reply/lists",
					Handler: article.ReplyListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/article/lists",
					Handler: article.ArticleListsHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/article/listsByTagId",
					Handler: article.ArticleListsByTagIdHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/article/listsByGroupId",
					Handler: article.ArticleListsByGroupIdHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/article/userlists",
					Handler: article.UserArticleListsHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/v1"),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Authority},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/article/upload/cos/token",
					Handler: article.GetTecentCloudUploadTokenHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/article/upload/cos/policy",
					Handler: article.GetTecentCloudCosPostPolicyHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/article/upload/cover",
					Handler: article.UploadCoverHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/article/publish",
					Handler: article.PublishHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/article/draft/lists",
					Handler: article.ArticleDraftListsHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/article/draft/save",
					Handler: article.ArticleSaveDraftHandler(serverCtx),
				},
				{
					Method:  http.MethodDelete,
					Path:    "/article/delete",
					Handler: article.ArticleDeleteHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/article/reply",
					Handler: article.ReplyHandler(serverCtx),
				},
				{
					Method:  http.MethodDelete,
					Path:    "/article/reply/delete",
					Handler: article.DeleteReplyHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/article/reply/userlists",
					Handler: article.UserReplyListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/article/like/lists",
					Handler: article.ArticleLikeListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/article/view/add",
					Handler: article.AddArticleViewRecordHandler(serverCtx),
				},
			}...,
		),
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
		rest.WithPrefix("/v1"),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Authority},
			[]rest.Route{
				{
					Method:  http.MethodDelete,
					Path:    "/article/admin/delete",
					Handler: admin.AdminDeleteArticleHandler(serverCtx),
				},
				{
					Method:  http.MethodDelete,
					Path:    "/article/reply/admin/delete",
					Handler: admin.AdminDeleteReplyHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/user/admin/ban",
					Handler: admin.AdminBanUserHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/user/admin/unban",
					Handler: admin.AdminUnbanUserHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/user/admin/mute",
					Handler: admin.AdminMuteUserHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/user/admin/unmute",
					Handler: admin.AdminUnmuteUserHandler(serverCtx),
				},
			}...,
		),
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
		rest.WithPrefix("/v1"),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Authority, serverCtx.CustomAuthority},
			[]rest.Route{}...,
		),
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
		rest.WithPrefix("/app/v1"),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.CustomAuthority},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/group/list",
					Handler: group.GetGroupListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/group",
					Handler: group.GetGroupByIdHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/group/userFollowList",
					Handler: group.GetUserFollowListHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/app/v1"),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Authority, serverCtx.CustomAuthority},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/tag/create",
					Handler: tag.CreateTagHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/tag/list",
					Handler: tag.GetTagListHandler(serverCtx),
				},
			}...,
		),
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
		rest.WithPrefix("/app/v1"),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Authority, serverCtx.CustomAuthority},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/tag",
					Handler: tag.GetTagByIdHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/app/v1"),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Authority, serverCtx.CustomAuthority},
			[]rest.Route{}...,
		),
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
		rest.WithPrefix("/app/v1"),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.CustomAuthority},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/groupMember/list",
					Handler: groupmember.GetGroupMemberListHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/app/v1"),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Authority, serverCtx.CustomAuthority},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/groupFollow/create",
					Handler: groupfollow.CreateGroupFollowHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/groupFollow/update",
					Handler: groupfollow.UpdateGroupFollowHandler(serverCtx),
				},
			}...,
		),
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
		rest.WithPrefix("/app/v1"),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.CustomAuthority},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/banner/list",
					Handler: banner.GetBannerListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/banner",
					Handler: banner.GetBannerByIdHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/app/v1"),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Authority, serverCtx.CustomAuthority},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/city/list",
					Handler: city.GetCityListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/city",
					Handler: city.GetCityByIdHandler(serverCtx),
				},
			}...,
		),
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
		rest.WithPrefix("/app/v1"),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Authority, serverCtx.CustomAuthority},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/street/list",
					Handler: street.GetStreetListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/street",
					Handler: street.GetStreetByIdHandler(serverCtx),
				},
			}...,
		),
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
		rest.WithPrefix("/app/v1"),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Authority, serverCtx.CustomAuthority},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/area/list",
					Handler: area.GetAreaListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/area",
					Handler: area.GetAreaByIdHandler(serverCtx),
				},
			}...,
		),
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
		rest.WithPrefix("/app/v1"),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Authority, serverCtx.CustomAuthority},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/province/list",
					Handler: province.GetProvinceListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/province",
					Handler: province.GetProvinceByIdHandler(serverCtx),
				},
			}...,
		),
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
		rest.WithPrefix("/app/v1"),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Authority, serverCtx.CustomAuthority},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/maintenInfo/create",
					Handler: mainteninfo.CreateMaintenInfoHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/maintenInfo/list",
					Handler: mainteninfo.GetMaintenInfoListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/maintenInfo",
					Handler: mainteninfo.GetMaintenInfoByIdHandler(serverCtx),
				},
			}...,
		),
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
		rest.WithPrefix("/app/v1"),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Authority, serverCtx.CustomAuthority},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/segment/list",
					Handler: segment.GetSegmentListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/segment",
					Handler: segment.GetSegmentByIdHandler(serverCtx),
				},
			}...,
		),
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
		rest.WithPrefix("/app/v1"),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Authority, serverCtx.CustomAuthority},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/article/list",
					Handler: article.GetArticleListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/article",
					Handler: article.GetArticleByIdHandler(serverCtx),
				},
			}...,
		),
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
		rest.WithPrefix("/app/v1"),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Authority, serverCtx.CustomAuthority},
			[]rest.Route{}...,
		),
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Authority, serverCtx.CustomAuthority},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/segmentArticleRecord/list",
					Handler: segmentarticlerecord.GetSegmentArticleRecordListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/segmentArticleRecord",
					Handler: segmentarticlerecord.GetSegmentArticleRecordByIdHandler(serverCtx),
				},
			}...,
		),
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
		rest.WithPrefix("/app/v1"),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Authority, serverCtx.CustomAuthority},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/userAddres/create",
					Handler: useraddres.CreateUserAddresHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/userAddres/update",
					Handler: useraddres.UpdateUserAddresHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/userAddres/delete",
					Handler: useraddres.DeleteUserAddresHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/userAddres/list",
					Handler: useraddres.GetUserAddresListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/userAddres",
					Handler: useraddres.GetUserAddresByIdHandler(serverCtx),
				},
			}...,
		),
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
		rest.WithPrefix("/app/v1"),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Authority, serverCtx.CustomAuthority},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/product/update",
					Handler: product.UpdateProductHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/product/list",
					Handler: product.GetProductListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/product",
					Handler: product.GetProductByIdHandler(serverCtx),
				},
			}...,
		),
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
		rest.WithPrefix("/app/v1"),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.CustomAuthority},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/appVersion/compareVersion",
					Handler: appversion.CompareVersionHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/app/v1"),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Authority, serverCtx.CustomAuthority},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/tagFollow/create",
					Handler: tagfollow.CreateTagFollowHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/tagFollow/update",
					Handler: tagfollow.UpdateTagFollowHandler(serverCtx),
				},
			}...,
		),
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
		rest.WithPrefix("/app/v1"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/mallProduct/list",
				Handler: mallproduct.GetMallProductListHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/mallProduct",
				Handler: mallproduct.GetMallProductByIdHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/mallProduct/getByAppPackageName",
				Handler: mallproduct.GetMallProductByAppPackageNameHandler(serverCtx),
			},
		},
		rest.WithPrefix("/app/v1"),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Authority, serverCtx.CustomAuthority},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/mallOrder/create",
					Handler: mallorder.CreateMallOrderHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/mallOrder/list",
					Handler: mallorder.GetMallOrderListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/mallOrder",
					Handler: mallorder.GetMallOrderByIdHandler(serverCtx),
				},
			}...,
		),
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
		rest.WithPrefix("/app/v1"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/payment/submit",
				Handler: payment.SubmitPayOrderHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/mallOrder/payment/status",
				Handler: payment.QueryPaymentStatusHandler(serverCtx),
			},
		},
		rest.WithPrefix("/app/v1"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/mallOrder/anonymous/create",
				Handler: anonymousmallorder.CreateAnonymousMallOrderHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/mallOrder/anonymous/list",
				Handler: anonymousmallorder.GetAnonymousMallOrderListHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/mallOrder/anonymous",
				Handler: anonymousmallorder.GetAnonymousMallOrderByIdHandler(serverCtx),
			},
		},
		rest.WithPrefix("/app/v1"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/payment/notify/:channelCode",
				Handler: payment.PaymentNotifyHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/refund/notify/:channelCode",
				Handler: payment.RefundNotifyHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/payment/mock/notify/:channelCode",
				Handler: payment.MockPaymentNotifyHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/refund/mock/notify/:channelCode",
				Handler: payment.MockRefundNotifyHandler(serverCtx),
			},
			{
				Method:  http.MethodGet,
				Path:    "/payment/success/alipay",
				Handler: payment.AliPaySuccessHandler(serverCtx),
			},
		},
		rest.WithPrefix("/app/v1"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/mallProductSku/list",
				Handler: mallproductsku.GetMallProductSkuListHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/mallProductSku",
				Handler: mallproductsku.GetMallProductSkuByIdHandler(serverCtx),
			},
		},
		rest.WithPrefix("/app/v1"),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Authority, serverCtx.CustomAuthority},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/mall_order_item/list",
					Handler: mallorderitem.GetMallOrderItemListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/mall_order_item",
					Handler: mallorderitem.GetMallOrderItemByIdHandler(serverCtx),
				},
			}...,
		),
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
		rest.WithPrefix("/app/v1"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/license/query",
				Handler: malllicense.QueryLicenseHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/license/activate",
				Handler: malllicense.ActivateLicenseHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/license/heartbeat",
				Handler: malllicense.HeartbeatHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/license/getMachineCode",
				Handler: malllicense.GetMachineCodeHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/license/bindDevice",
				Handler: malllicense.BindDeviceHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/license/unbindDevice",
				Handler: malllicense.UnbindDeviceHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/license/getServerTime",
				Handler: malllicense.GetServerTimeHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/license/getServerStatus",
				Handler: malllicense.GetServerStatusHandler(serverCtx),
			},
		},
		rest.WithPrefix("/app/v1"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/bolo_lexicon/list",
				Handler: bololexicon.GetBoloLexiconListHandler(serverCtx),
			},
		},
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/userAgreement",
				Handler: useragreement.GetUserAgreementByIdHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/userAgreement/getUserAgreementByKey",
				Handler: useragreement.GetUserAgreementByKeyHandler(serverCtx),
			},
		},
		rest.WithPrefix("/app/v1"),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Authority},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/rental_device/create",
					Handler: rentaldevice.CreateRentalDeviceHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/rental_device/update",
					Handler: rentaldevice.UpdateRentalDeviceHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/rental_device/delete",
					Handler: rentaldevice.DeleteRentalDeviceHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/rental_device/list",
					Handler: rentaldevice.GetRentalDeviceListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/rental_device",
					Handler: rentaldevice.GetRentalDeviceByIdHandler(serverCtx),
				},
			}...,
		),
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
	)
}
