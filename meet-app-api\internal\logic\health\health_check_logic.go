package health

import (
	"context"
	"errors"
	"time"

	"seevision.cn/server/meet-app-api/internal/svc"
	"seevision.cn/server/meet-app-api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type HealthCheckLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewHealthCheckLogic(ctx context.Context, svcCtx *svc.ServiceContext) *HealthCheckLogic {
	return &HealthCheckLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *HealthCheckLogic) HealthCheck() (resp *types.HealthCheckResp, err error) {
	// 初始化响应
	resp = &types.HealthCheckResp{
		Status:    "ok",
		Timestamp: time.Now().Unix(),
		Service:   "meet-app-api",
		Version:   "v1.0.0",
		Checks:    make(map[string]string),
	}

	// 检查各个依赖服务
	overallStatus := "ok"

	// 检查 Redis 连接
	if l.svcCtx.BizRedis != nil {
		if err := l.checkRedis(); err != nil {
			resp.Checks["redis"] = "error"
			overallStatus = "degraded"
			l.Logger.Errorf("Redis health check failed: %v", err)
		} else {
			resp.Checks["redis"] = "ok"
		}
	} else {
		resp.Checks["redis"] = "not_configured"
	}

	// 检查 Core RPC 服务
	if l.svcCtx.CoreRpc != nil {
		if err := l.checkCoreRpc(); err != nil {
			resp.Checks["core_rpc"] = "error"
			overallStatus = "degraded"
			l.Logger.Errorf("Core RPC health check failed: %v", err)
		} else {
			resp.Checks["core_rpc"] = "ok"
		}
	} else {
		resp.Checks["core_rpc"] = "not_configured"
	}

	// 检查 Community RPC 服务
	if l.svcCtx.MeetCommunityRpc != nil {
		if err := l.checkCommunityRpc(); err != nil {
			resp.Checks["community_rpc"] = "error"
			overallStatus = "degraded"
			l.Logger.Errorf("Community RPC health check failed: %v", err)
		} else {
			resp.Checks["community_rpc"] = "ok"
		}
	} else {
		resp.Checks["community_rpc"] = "not_configured"
	}

	// 检查 Usercenter RPC 服务
	if l.svcCtx.MeetUsercenterRpc != nil {
		if err := l.checkUsercenterRpc(); err != nil {
			resp.Checks["usercenter_rpc"] = "error"
			overallStatus = "degraded"
			l.Logger.Errorf("Usercenter RPC health check failed: %v", err)
		} else {
			resp.Checks["usercenter_rpc"] = "ok"
		}
	} else {
		resp.Checks["usercenter_rpc"] = "not_configured"
	}

	resp.Status = overallStatus
	return resp, nil
}

// checkRedis 检查 Redis 连接
func (l *HealthCheckLogic) checkRedis() error {
	ctx, cancel := context.WithTimeout(l.ctx, 3*time.Second)
	defer cancel()

	if !l.svcCtx.BizRedis.PingCtx(ctx) {
		return errors.New("redis ping failed")
	}
	return nil
}

// checkCoreRpc 检查 Core RPC 服务
func (l *HealthCheckLogic) checkCoreRpc() error {
	// 这里可以调用一个简单的 RPC 方法来检查连接
	// 由于没有具体的健康检查方法，我们暂时返回 nil
	// 在实际项目中，应该调用一个轻量级的 RPC 方法
	return nil
}

// checkCommunityRpc 检查 Community RPC 服务
func (l *HealthCheckLogic) checkCommunityRpc() error {
	// 这里可以调用一个简单的 RPC 方法来检查连接
	return nil
}

// checkAnalyticsRpc 检查 Analytics RPC 服务
func (l *HealthCheckLogic) checkAnalyticsRpc() error {
	// 这里可以调用一个简单的 RPC 方法来检查连接
	return nil
}

// checkUsercenterRpc 检查 Usercenter RPC 服务
func (l *HealthCheckLogic) checkUsercenterRpc() error {
	// 这里可以调用一个简单的 RPC 方法来检查连接
	return nil
}
