// Code generated by goctl. DO NOT EDIT.
package handler

import (
	"net/http"

	article "github.com/suyuan32/simple-cms/internal/handler/article"
	articlecomment "github.com/suyuan32/simple-cms/internal/handler/articlecomment"
	base "github.com/suyuan32/simple-cms/internal/handler/base"
	category "github.com/suyuan32/simple-cms/internal/handler/category"
	health "github.com/suyuan32/simple-cms/internal/handler/health"
	history "github.com/suyuan32/simple-cms/internal/handler/history"
	publicapi "github.com/suyuan32/simple-cms/internal/handler/publicapi"
	tag "github.com/suyuan32/simple-cms/internal/handler/tag"
	"github.com/suyuan32/simple-cms/internal/svc"

	"github.com/zeromicro/go-zero/rest"
)

func RegisterHandlers(server *rest.Server, serverCtx *svc.ServiceContext) {
	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodGet,
				Path:    "/init/database",
				Handler: base.InitDatabaseHandler(serverCtx),
			},
		},
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodGet,
				Path:    "/health",
				Handler: health.HealthCheckHandler(serverCtx),
			},
		},
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Authority},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/article/create",
					Handler: article.CreateArticleHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/article/update",
					Handler: article.UpdateArticleHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/article/delete",
					Handler: article.DeleteArticleHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/article/list",
					Handler: article.GetArticleListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/article",
					Handler: article.GetArticleByIdHandler(serverCtx),
				},
			}...,
		),
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
		rest.WithPrefix("/websitecms"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodGet,
				Path:    "/article/:id",
				Handler: publicapi.GetPublicArticleByIdHandler(serverCtx),
			},
			{
				Method:  http.MethodGet,
				Path:    "/article/list",
				Handler: publicapi.GetPublicArticleListHandler(serverCtx),
			},
		},
		rest.WithPrefix("/websitecms"),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Authority},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/article_category/create",
					Handler: category.CreateCategoryHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/article_category/update",
					Handler: category.UpdateCategoryHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/article_category/delete",
					Handler: category.DeleteCategoryHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/article_category/list",
					Handler: category.GetCategoryListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/article_category",
					Handler: category.GetCategoryByIdHandler(serverCtx),
				},
			}...,
		),
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
		rest.WithPrefix("/websitecms"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodGet,
				Path:    "/article_category/list",
				Handler: publicapi.GetPublicCategoryListHandler(serverCtx),
			},
			{
				Method:  http.MethodGet,
				Path:    "/article_category/:id",
				Handler: publicapi.GetPublicCategoryByIdHandler(serverCtx),
			},
		},
		rest.WithPrefix("/websitecms"),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Authority},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/article_tag/create",
					Handler: tag.CreateTagHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/article_tag/update",
					Handler: tag.UpdateTagHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/article_tag/delete",
					Handler: tag.DeleteTagHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/article_tag/list",
					Handler: tag.GetTagListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/article_tag",
					Handler: tag.GetTagByIdHandler(serverCtx),
				},
			}...,
		),
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
		rest.WithPrefix("/websitecms"),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Authority},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/article_comment/create",
					Handler: articlecomment.CreateArticleCommentHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/article_comment/update",
					Handler: articlecomment.UpdateArticleCommentHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/article_comment/delete",
					Handler: articlecomment.DeleteArticleCommentHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/article_comment/list",
					Handler: articlecomment.GetArticleCommentListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/article_comment",
					Handler: articlecomment.GetArticleCommentByIdHandler(serverCtx),
				},
			}...,
		),
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
		rest.WithPrefix("/websitecms"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/article_comment/add",
				Handler: articlecomment.AddArticleCommentHandler(serverCtx),
			},
		},
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
		rest.WithPrefix("/websitecms"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/article_comment/article",
				Handler: publicapi.GetCommentByArticleIdHandler(serverCtx),
			},
		},
		rest.WithPrefix("/websitecms"),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Authority},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/history/create",
					Handler: history.CreateHistoryHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/history/update",
					Handler: history.UpdateHistoryHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/history/delete",
					Handler: history.DeleteHistoryHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/history/list",
					Handler: history.GetHistoryListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/history",
					Handler: history.GetHistoryByIdHandler(serverCtx),
				},
			}...,
		),
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
		rest.WithPrefix("/websitecms"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodGet,
				Path:    "/history/list",
				Handler: publicapi.GetPublicHistoryListHandler(serverCtx),
			},
		},
		rest.WithPrefix("/websitecms"),
	)
}
