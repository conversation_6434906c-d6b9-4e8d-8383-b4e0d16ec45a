{"consumes": ["application/json"], "produces": ["application/json"], "schemes": ["http", "https"], "swagger": "2.0", "info": {"description": "Description: meet_admin_api service", "title": "meet_admin_api", "version": "0.0.1"}, "host": "localhost:40001", "basePath": "/", "paths": {"/analytics/dashboard": {"post": {"description": "Dashboard | 仪表盘", "tags": ["analytics"], "summary": "Dashboard | 仪表盘", "operationId": "GetAnalyticsDashboard", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/AnalyticsDashboardReq"}}], "responses": {"200": {"description": "AnalyticsDashboardResp", "schema": {"$ref": "#/definitions/AnalyticsDashboardResp"}}}}}, "/analytics/dashboard/eventTrends": {"post": {"description": "Event Trends | 事件趋势数据", "tags": ["analytics"], "summary": "Event Trends | 事件趋势数据", "operationId": "GetEventTrends", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/AnalyticsDashboardReq"}}], "responses": {"200": {"description": "EventTrendsResp", "schema": {"$ref": "#/definitions/EventTrendsResp"}}}}}, "/analytics/dashboard/export": {"post": {"description": "Export Dashboard Data | 导出仪表盘数据", "tags": ["analytics"], "summary": "Export Dashboard Data | 导出仪表盘数据", "operationId": "ExportDashboardData", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/AnalyticsDashboardReq"}}], "responses": {"200": {"description": "ExportDataResp", "schema": {"$ref": "#/definitions/ExportDataResp"}}}}}, "/analytics/dashboard/geographic": {"post": {"description": "Geographic Data | 地理分布数据", "tags": ["analytics"], "summary": "Geographic Data | 地理分布数据", "operationId": "GetGeographicData", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/AnalyticsDashboardReq"}}], "responses": {"200": {"description": "GeographicDataResp", "schema": {"$ref": "#/definitions/GeographicDataResp"}}}}}, "/analytics/dashboard/realtime": {"post": {"description": "Realtime Data | 实时数据", "tags": ["analytics"], "summary": "Realtime Data | 实时数据", "operationId": "GetRealtimeData", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/AnalyticsDashboardReq"}}], "responses": {"200": {"description": "RealtimeDataResp", "schema": {"$ref": "#/definitions/RealtimeDataResp"}}}}}, "/analytics/dashboard/userBehaviorHeatmap": {"post": {"description": "User Behavior Heatmap | 用户行为热力图数据", "tags": ["analytics"], "summary": "User Behavior Heatmap | 用户行为热力图数据", "operationId": "GetUserBehaviorHeatmap", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/AnalyticsDashboardReq"}}], "responses": {"200": {"description": "UserBehaviorHeatmapResp", "schema": {"$ref": "#/definitions/UserBehaviorHeatmapResp"}}}}}, "/analytics/dashboard/userTrends": {"post": {"description": "User Trends | 用户趋势数据", "tags": ["analytics"], "summary": "User Trends | 用户趋势数据", "operationId": "GetUserTrends", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/AnalyticsDashboardReq"}}], "responses": {"200": {"description": "UserTrendsResp", "schema": {"$ref": "#/definitions/UserTrendsResp"}}}}}, "/analytics/funnel": {"post": {"description": "Get Funnel By Id | 获取漏斗详情", "tags": ["funnel"], "summary": "Get Funnel By Id | 获取漏斗详情", "operationId": "GetFunnelById", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDReq"}}], "responses": {"200": {"description": "FunnelInfoResp", "schema": {"$ref": "#/definitions/FunnelInfoResp"}}}}}, "/analytics/funnel/analysis/run": {"post": {"description": "Run Funnel Analysis | 运行漏斗分析", "tags": ["funnel"], "summary": "Run Funnel Analysis | 运行漏斗分析", "operationId": "RunFunnelAnalysis", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/RunFunnelAnalysisReq"}}], "responses": {"200": {"description": "FunnelAnalysisResp", "schema": {"$ref": "#/definitions/FunnelAnalysisResp"}}}}}, "/analytics/funnel/condition": {"post": {"description": "Get Funnel Condition By Id | 获取漏斗条件详情", "tags": ["funnel"], "summary": "Get Funnel Condition By Id | 获取漏斗条件详情", "operationId": "GetFunnelConditionById", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDReq"}}], "responses": {"200": {"description": "FunnelConditionInfoResp", "schema": {"$ref": "#/definitions/FunnelConditionInfoResp"}}}}}, "/analytics/funnel/condition/create": {"post": {"description": "Create Funnel Condition | 创建漏斗条件", "tags": ["funnel"], "summary": "Create Funnel Condition | 创建漏斗条件", "operationId": "CreateFunnelCondition", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/FunnelConditionInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/analytics/funnel/condition/delete": {"post": {"description": "Delete Funnel Condition | 删除漏斗条件", "tags": ["funnel"], "summary": "Delete Funnel Condition | 删除漏斗条件", "operationId": "DeleteFunnelCondition", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDsReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/analytics/funnel/condition/list": {"post": {"description": "Get Funnel Conditions | 获取漏斗条件列表", "tags": ["funnel"], "summary": "Get Funnel Conditions | 获取漏斗条件列表", "operationId": "GetFunnelConditionList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/FunnelConditionListReq"}}], "responses": {"200": {"description": "FunnelConditionListResp", "schema": {"$ref": "#/definitions/FunnelConditionListResp"}}}}}, "/analytics/funnel/condition/update": {"post": {"description": "Update Funnel Condition | 更新漏斗条件", "tags": ["funnel"], "summary": "Update Funnel Condition | 更新漏斗条件", "operationId": "UpdateFunnelCondition", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/FunnelConditionInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/analytics/funnel/create": {"post": {"description": "Create Funnel | 创建漏斗", "tags": ["funnel"], "summary": "Create Funnel | 创建漏斗", "operationId": "CreateFunnel", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/FunnelInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/analytics/funnel/delete": {"post": {"description": "Delete Funnel | 删除漏斗", "tags": ["funnel"], "summary": "Delete Funnel | 删除漏斗", "operationId": "DeleteFunnel", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDsReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/analytics/funnel/list": {"post": {"description": "Get Funnel List | 获取漏斗列表", "tags": ["funnel"], "summary": "Get Funnel List | 获取漏斗列表", "operationId": "GetFunnelList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/FunnelListReq"}}], "responses": {"200": {"description": "FunnelListResp", "schema": {"$ref": "#/definitions/FunnelListResp"}}}}}, "/analytics/funnel/step": {"post": {"description": "Get Funnel Step By Id | 获取漏斗步骤详情", "tags": ["funnel"], "summary": "Get Funnel Step By Id | 获取漏斗步骤详情", "operationId": "GetFunnelStepById", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDReq"}}], "responses": {"200": {"description": "FunnelStepInfoResp", "schema": {"$ref": "#/definitions/FunnelStepInfoResp"}}}}}, "/analytics/funnel/step/create": {"post": {"description": "Create Funnel Step | 创建漏斗步骤", "tags": ["funnel"], "summary": "Create Funnel Step | 创建漏斗步骤", "operationId": "CreateFunnelStep", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/FunnelStepInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/analytics/funnel/step/delete": {"post": {"description": "Delete Funnel Step | 删除漏斗步骤", "tags": ["funnel"], "summary": "Delete Funnel Step | 删除漏斗步骤", "operationId": "DeleteFunnelStep", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDsReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/analytics/funnel/step/list": {"post": {"description": "Get Funnel Steps | 获取漏斗步骤列表", "tags": ["funnel"], "summary": "Get Funnel Steps | 获取漏斗步骤列表", "operationId": "GetFunnelStepList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/FunnelStepListReq"}}], "responses": {"200": {"description": "FunnelStepListResp", "schema": {"$ref": "#/definitions/FunnelStepListResp"}}}}}, "/analytics/funnel/step/update": {"post": {"description": "Update Funnel Step | 更新漏斗步骤", "tags": ["funnel"], "summary": "Update Funnel Step | 更新漏斗步骤", "operationId": "UpdateFunnelStep", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/FunnelStepInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/analytics/funnel/update": {"post": {"description": "Update Funnel | 更新漏斗", "tags": ["funnel"], "summary": "Update Funnel | 更新漏斗", "operationId": "UpdateFunnel", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/FunnelInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/analytics/pathAnalysis": {"post": {"description": "Path Analysis | 路径分析", "tags": ["analytics"], "summary": "Path Analysis | 路径分析", "operationId": "GetPathAnalysis", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/PathAnalysisReq"}}], "responses": {"200": {"description": "PathAnalysisResp", "schema": {"$ref": "#/definitions/PathAnalysisResp"}}}}}, "/analytics/pathAnalysis/conversions": {"post": {"description": "Event Conversions | 事件转换数据", "tags": ["analytics"], "summary": "Event Conversions | 事件转换数据", "operationId": "GetEventConversions", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/PathAnalysisReq"}}], "responses": {"200": {"description": "EventConversionsResp", "schema": {"$ref": "#/definitions/EventConversionsResp"}}}}}, "/analytics/pathAnalysis/export": {"post": {"description": "Export Path Analysis | 导出路径分析数据", "tags": ["analytics"], "summary": "Export Path Analysis | 导出路径分析数据", "operationId": "ExportPathAnalysis", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/PathAnalysisReq"}}], "responses": {"200": {"description": "ExportDataResp", "schema": {"$ref": "#/definitions/ExportDataResp"}}}}}, "/analytics/pathAnalysis/heatmap": {"post": {"description": "Path Heatmap | 路径热力图数据", "tags": ["analytics"], "summary": "Path Heatmap | 路径热力图数据", "operationId": "GetPathHeatmap", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/PathAnalysisReq"}}], "responses": {"200": {"description": "PathHeatmapResp", "schema": {"$ref": "#/definitions/PathHeatmapResp"}}}}}, "/analytics/pathAnalysis/stats": {"post": {"description": "Path Stats | 路径统计趋势数据", "tags": ["analytics"], "summary": "Path Stats | 路径统计趋势数据", "operationId": "GetPathStats", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/PathAnalysisReq"}}], "responses": {"200": {"description": "PathStatsResp", "schema": {"$ref": "#/definitions/PathStatsResp"}}}}}, "/analytics/universalStats": {"post": {"description": "Universal Stats | 通用统计分析", "tags": ["analytics"], "summary": "Universal Stats | 通用统计分析", "operationId": "GetUniversalStats", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/UniversalStatsReq"}}], "responses": {"200": {"description": "UniversalStatsResp", "schema": {"$ref": "#/definitions/UniversalStatsResp"}}}}}, "/analytics_application": {"post": {"description": "Get analytics application by ID | 通过ID获取统计应用", "tags": ["analyticsapplication"], "summary": "Get analytics application by ID | 通过ID获取统计应用", "operationId": "GetAnalyticsApplicationById", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDReq"}}], "responses": {"200": {"description": "AnalyticsApplicationInfoResp", "schema": {"$ref": "#/definitions/AnalyticsApplicationInfoResp"}}}}}, "/analytics_application/create": {"post": {"description": "Create analytics application information | 创建统计应用", "tags": ["analyticsapplication"], "summary": "Create analytics application information | 创建统计应用", "operationId": "CreateAnalyticsApplication", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/AnalyticsApplicationInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/analytics_application/delete": {"post": {"description": "Delete analytics application information | 删除统计应用", "tags": ["analyticsapplication"], "summary": "Delete analytics application information | 删除统计应用", "operationId": "DeleteAnalyticsApplication", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDsReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/analytics_application/generate_api_secret": {"post": {"description": "Generate API secret | 生成API密钥", "tags": ["analyticsapplication"], "summary": "Generate API secret | 生成API密钥", "operationId": "GenerateApiSecret", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/GenerateApiSecretReq"}}], "responses": {"200": {"description": "AnalyticsApplicationInfoResp", "schema": {"$ref": "#/definitions/AnalyticsApplicationInfoResp"}}}}}, "/analytics_application/list": {"post": {"description": "Get analytics application list | 获取统计应用列表", "tags": ["analyticsapplication"], "summary": "Get analytics application list | 获取统计应用列表", "operationId": "GetAnalyticsApplicationList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/AnalyticsApplicationListReq"}}], "responses": {"200": {"description": "AnalyticsApplicationListResp", "schema": {"$ref": "#/definitions/AnalyticsApplicationListResp"}}}}}, "/analytics_application/reset_api_secret": {"post": {"description": "Reset API secret | 重置API密钥", "tags": ["analyticsapplication"], "summary": "Reset API secret | 重置API密钥", "operationId": "ResetApiSecret", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/ResetApiSecretReq"}}], "responses": {"200": {"description": "AnalyticsApplicationInfoResp", "schema": {"$ref": "#/definitions/AnalyticsApplicationInfoResp"}}}}}, "/analytics_application/update": {"post": {"description": "Update analytics application information | 更新统计应用", "tags": ["analyticsapplication"], "summary": "Update analytics application information | 更新统计应用", "operationId": "UpdateAnalyticsApplication", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/AnalyticsApplicationInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/analytics_cohort": {"post": {"description": "Get analytics cohort by ID | 通过ID获取AnalyticsCohort信息", "tags": ["analyticscohort"], "summary": "Get analytics cohort by ID | 通过ID获取AnalyticsCohort信息", "operationId": "GetAnalyticsCohortById", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDReq"}}], "responses": {"200": {"description": "AnalyticsCohortInfoResp", "schema": {"$ref": "#/definitions/AnalyticsCohortInfoResp"}}}}}, "/analytics_cohort/create": {"post": {"description": "Create analytics cohort information | 创建AnalyticsCohort信息", "tags": ["analyticscohort"], "summary": "Create analytics cohort information | 创建AnalyticsCohort信息", "operationId": "CreateAnalyticsCohort", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/AnalyticsCohortInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/analytics_cohort/delete": {"post": {"description": "Delete analytics cohort information | 删除AnalyticsCohort信息", "tags": ["analyticscohort"], "summary": "Delete analytics cohort information | 删除AnalyticsCohort信息", "operationId": "DeleteAnalyticsCohort", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDsReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/analytics_cohort/list": {"post": {"description": "Get analytics cohort list | 获取AnalyticsCohort信息列表", "tags": ["analyticscohort"], "summary": "Get analytics cohort list | 获取AnalyticsCohort信息列表", "operationId": "GetAnalyticsCohortList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/AnalyticsCohortListReq"}}], "responses": {"200": {"description": "AnalyticsCohortListResp", "schema": {"$ref": "#/definitions/AnalyticsCohortListResp"}}}}}, "/analytics_cohort/update": {"post": {"description": "Update analytics cohort information | 更新AnalyticsCohort信息", "tags": ["analyticscohort"], "summary": "Update analytics cohort information | 更新AnalyticsCohort信息", "operationId": "UpdateAnalyticsCohort", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/AnalyticsCohortInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/analytics_daily_summary": {"post": {"description": "Get analytics daily summary by ID | 通过ID获取AnalyticsDailySummary信息", "tags": ["analyticsdailysummary"], "summary": "Get analytics daily summary by ID | 通过ID获取AnalyticsDailySummary信息", "operationId": "GetAnalyticsDailySummaryById", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDReq"}}], "responses": {"200": {"description": "AnalyticsDailySummaryInfoResp", "schema": {"$ref": "#/definitions/AnalyticsDailySummaryInfoResp"}}}}}, "/analytics_daily_summary/create": {"post": {"description": "Create analytics daily summary information | 创建AnalyticsDailySummary信息", "tags": ["analyticsdailysummary"], "summary": "Create analytics daily summary information | 创建AnalyticsDailySummary信息", "operationId": "CreateAnalyticsDailySummary", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/AnalyticsDailySummaryInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/analytics_daily_summary/delete": {"post": {"description": "Delete analytics daily summary information | 删除AnalyticsDailySummary信息", "tags": ["analyticsdailysummary"], "summary": "Delete analytics daily summary information | 删除AnalyticsDailySummary信息", "operationId": "DeleteAnalyticsDailySummary", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDsReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/analytics_daily_summary/list": {"post": {"description": "Get analytics daily summary list | 获取AnalyticsDailySummary信息列表", "tags": ["analyticsdailysummary"], "summary": "Get analytics daily summary list | 获取AnalyticsDailySummary信息列表", "operationId": "GetAnalyticsDailySummaryList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/AnalyticsDailySummaryListReq"}}], "responses": {"200": {"description": "AnalyticsDailySummaryListResp", "schema": {"$ref": "#/definitions/AnalyticsDailySummaryListResp"}}}}}, "/analytics_daily_summary/update": {"post": {"description": "Update analytics daily summary information | 更新AnalyticsDailySummary信息", "tags": ["analyticsdailysummary"], "summary": "Update analytics daily summary information | 更新AnalyticsDailySummary信息", "operationId": "UpdateAnalyticsDailySummary", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/AnalyticsDailySummaryInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/analytics_device": {"post": {"description": "Get analytics device by ID | 通过ID获取AnalyticsDevice信息", "tags": ["analyticsdevice"], "summary": "Get analytics device by ID | 通过ID获取AnalyticsDevice信息", "operationId": "GetAnalyticsDeviceById", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDReq"}}], "responses": {"200": {"description": "AnalyticsDeviceInfoResp", "schema": {"$ref": "#/definitions/AnalyticsDeviceInfoResp"}}}}}, "/analytics_device/create": {"post": {"description": "Create analytics device information | 创建AnalyticsDevice信息", "tags": ["analyticsdevice"], "summary": "Create analytics device information | 创建AnalyticsDevice信息", "operationId": "CreateAnalyticsDevice", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/AnalyticsDeviceInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/analytics_device/delete": {"post": {"description": "Delete analytics device information | 删除AnalyticsDevice信息", "tags": ["analyticsdevice"], "summary": "Delete analytics device information | 删除AnalyticsDevice信息", "operationId": "DeleteAnalyticsDevice", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDsReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/analytics_device/list": {"post": {"description": "Get analytics device list | 获取AnalyticsDevice信息列表", "tags": ["analyticsdevice"], "summary": "Get analytics device list | 获取AnalyticsDevice信息列表", "operationId": "GetAnalyticsDeviceList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/AnalyticsDeviceListReq"}}], "responses": {"200": {"description": "AnalyticsDeviceListResp", "schema": {"$ref": "#/definitions/AnalyticsDeviceListResp"}}}}}, "/analytics_device/update": {"post": {"description": "Update analytics device information | 更新AnalyticsDevice信息", "tags": ["analyticsdevice"], "summary": "Update analytics device information | 更新AnalyticsDevice信息", "operationId": "UpdateAnalyticsDevice", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/AnalyticsDeviceInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/analytics_event": {"post": {"description": "Get analytics event by ID | 通过ID获取AnalyticsEvent信息", "tags": ["analyticsevent"], "summary": "Get analytics event by ID | 通过ID获取AnalyticsEvent信息", "operationId": "GetAnalyticsEventById", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDReq"}}], "responses": {"200": {"description": "AnalyticsEventInfoResp", "schema": {"$ref": "#/definitions/AnalyticsEventInfoResp"}}}}}, "/analytics_event/create": {"post": {"description": "Create analytics event information | 创建AnalyticsEvent信息", "tags": ["analyticsevent"], "summary": "Create analytics event information | 创建AnalyticsEvent信息", "operationId": "CreateAnalyticsEvent", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/AnalyticsEventInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/analytics_event/delete": {"post": {"description": "Delete analytics event information | 删除AnalyticsEvent信息", "tags": ["analyticsevent"], "summary": "Delete analytics event information | 删除AnalyticsEvent信息", "operationId": "DeleteAnalyticsEvent", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDsReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/analytics_event/list": {"post": {"description": "Get analytics event list | 获取AnalyticsEvent信息列表", "tags": ["analyticsevent"], "summary": "Get analytics event list | 获取AnalyticsEvent信息列表", "operationId": "GetAnalyticsEventList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/AnalyticsEventListReq"}}], "responses": {"200": {"description": "AnalyticsEventListResp", "schema": {"$ref": "#/definitions/AnalyticsEventListResp"}}}}}, "/analytics_event/update": {"post": {"description": "Update analytics event information | 更新AnalyticsEvent信息", "tags": ["analyticsevent"], "summary": "Update analytics event information | 更新AnalyticsEvent信息", "operationId": "UpdateAnalyticsEvent", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/AnalyticsEventInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/analytics_event_definition": {"post": {"description": "Get analytics event definition by ID | 通过ID获取AnalyticsEventDefinition信息", "tags": ["analyticseventdefinition"], "summary": "Get analytics event definition by ID | 通过ID获取AnalyticsEventDefinition信息", "operationId": "GetAnalyticsEventDefinitionById", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDReq"}}], "responses": {"200": {"description": "AnalyticsEventDefinitionInfoResp", "schema": {"$ref": "#/definitions/AnalyticsEventDefinitionInfoResp"}}}}}, "/analytics_event_definition/create": {"post": {"description": "Create analytics event definition information | 创建AnalyticsEventDefinition信息", "tags": ["analyticseventdefinition"], "summary": "Create analytics event definition information | 创建AnalyticsEventDefinition信息", "operationId": "CreateAnalyticsEventDefinition", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/AnalyticsEventDefinitionInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/analytics_event_definition/delete": {"post": {"description": "Delete analytics event definition information | 删除AnalyticsEventDefinition信息", "tags": ["analyticseventdefinition"], "summary": "Delete analytics event definition information | 删除AnalyticsEventDefinition信息", "operationId": "DeleteAnalyticsEventDefinition", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDsReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/analytics_event_definition/list": {"post": {"description": "Get analytics event definition list | 获取AnalyticsEventDefinition信息列表", "tags": ["analyticseventdefinition"], "summary": "Get analytics event definition list | 获取AnalyticsEventDefinition信息列表", "operationId": "GetAnalyticsEventDefinitionList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/AnalyticsEventDefinitionListReq"}}], "responses": {"200": {"description": "AnalyticsEventDefinitionListResp", "schema": {"$ref": "#/definitions/AnalyticsEventDefinitionListResp"}}}}}, "/analytics_event_definition/update": {"post": {"description": "Update analytics event definition information | 更新AnalyticsEventDefinition信息", "tags": ["analyticseventdefinition"], "summary": "Update analytics event definition information | 更新AnalyticsEventDefinition信息", "operationId": "UpdateAnalyticsEventDefinition", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/AnalyticsEventDefinitionInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/analytics_real_time_metric": {"post": {"description": "Get analytics real time metric by ID | 通过ID获取AnalyticsRealTimeMetric信息", "tags": ["analyticsrealtimemetric"], "summary": "Get analytics real time metric by ID | 通过ID获取AnalyticsRealTimeMetric信息", "operationId": "GetAnalyticsRealTimeMetricById", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDReq"}}], "responses": {"200": {"description": "AnalyticsRealTimeMetricInfoResp", "schema": {"$ref": "#/definitions/AnalyticsRealTimeMetricInfoResp"}}}}}, "/analytics_real_time_metric/create": {"post": {"description": "Create analytics real time metric information | 创建AnalyticsRealTimeMetric信息", "tags": ["analyticsrealtimemetric"], "summary": "Create analytics real time metric information | 创建AnalyticsRealTimeMetric信息", "operationId": "CreateAnalyticsRealTimeMetric", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/AnalyticsRealTimeMetricInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/analytics_real_time_metric/delete": {"post": {"description": "Delete analytics real time metric information | 删除AnalyticsRealTimeMetric信息", "tags": ["analyticsrealtimemetric"], "summary": "Delete analytics real time metric information | 删除AnalyticsRealTimeMetric信息", "operationId": "DeleteAnalyticsRealTimeMetric", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDsReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/analytics_real_time_metric/list": {"post": {"description": "Get analytics real time metric list | 获取AnalyticsRealTimeMetric信息列表", "tags": ["analyticsrealtimemetric"], "summary": "Get analytics real time metric list | 获取AnalyticsRealTimeMetric信息列表", "operationId": "GetAnalyticsRealTimeMetricList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/AnalyticsRealTimeMetricListReq"}}], "responses": {"200": {"description": "AnalyticsRealTimeMetricListResp", "schema": {"$ref": "#/definitions/AnalyticsRealTimeMetricListResp"}}}}}, "/analytics_real_time_metric/update": {"post": {"description": "Update analytics real time metric information | 更新AnalyticsRealTimeMetric信息", "tags": ["analyticsrealtimemetric"], "summary": "Update analytics real time metric information | 更新AnalyticsRealTimeMetric信息", "operationId": "UpdateAnalyticsRealTimeMetric", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/AnalyticsRealTimeMetricInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/analytics_user": {"post": {"description": "Get analytics user by ID | 通过ID获取AnalyticsUser信息", "tags": ["analyticsuser"], "summary": "Get analytics user by ID | 通过ID获取AnalyticsUser信息", "operationId": "GetAnalyticsUserById", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDReq"}}], "responses": {"200": {"description": "AnalyticsUserInfoResp", "schema": {"$ref": "#/definitions/AnalyticsUserInfoResp"}}}}}, "/analytics_user/create": {"post": {"description": "Create analytics user information | 创建AnalyticsUser信息", "tags": ["analyticsuser"], "summary": "Create analytics user information | 创建AnalyticsUser信息", "operationId": "CreateAnalyticsUser", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/AnalyticsUserInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/analytics_user/delete": {"post": {"description": "Delete analytics user information | 删除AnalyticsUser信息", "tags": ["analyticsuser"], "summary": "Delete analytics user information | 删除AnalyticsUser信息", "operationId": "DeleteAnalyticsUser", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDsReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/analytics_user/list": {"post": {"description": "Get analytics user list | 获取AnalyticsUser信息列表", "tags": ["analyticsuser"], "summary": "Get analytics user list | 获取AnalyticsUser信息列表", "operationId": "GetAnalyticsUserList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/AnalyticsUserListReq"}}], "responses": {"200": {"description": "AnalyticsUserListResp", "schema": {"$ref": "#/definitions/AnalyticsUserListResp"}}}}}, "/analytics_user/update": {"post": {"description": "Update analytics user information | 更新AnalyticsUser信息", "tags": ["analyticsuser"], "summary": "Update analytics user information | 更新AnalyticsUser信息", "operationId": "UpdateAnalyticsUser", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/AnalyticsUserInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/app_version": {"post": {"description": "Get app version by ID | 通过ID获取AppVersion", "tags": ["appversion"], "summary": "Get app version by ID | 通过ID获取AppVersion", "operationId": "GetAppVersionById", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDReq"}}], "responses": {"200": {"description": "AppVersionInfoResp", "schema": {"$ref": "#/definitions/AppVersionInfoResp"}}}}}, "/app_version/create": {"post": {"description": "Create app version information | 创建AppVersion", "tags": ["appversion"], "summary": "Create app version information | 创建AppVersion", "operationId": "CreateAppVersion", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/AppVersionInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/app_version/delete": {"post": {"description": "Delete app version information | 删除AppVersion信息", "tags": ["appversion"], "summary": "Delete app version information | 删除AppVersion信息", "operationId": "DeleteAppVersion", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDsReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/app_version/list": {"post": {"description": "Get app version list | 获取AppVersion列表", "tags": ["appversion"], "summary": "Get app version list | 获取AppVersion列表", "operationId": "GetAppVersionList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/AppVersionListReq"}}], "responses": {"200": {"description": "AppVersionListResp", "schema": {"$ref": "#/definitions/AppVersionListResp"}}}}}, "/app_version/update": {"post": {"description": "Update app version information | 更新AppVersion", "tags": ["appversion"], "summary": "Update app version information | 更新AppVersion", "operationId": "UpdateAppVersion", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/AppVersionInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/appapi": {"post": {"description": "Get appapi by ID | 通过ID获取Appapi信息", "tags": ["appapi"], "summary": "Get appapi by ID | 通过ID获取Appapi信息", "operationId": "GetAppapiById", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDReq"}}], "responses": {"200": {"description": "AppapiInfoResp", "schema": {"$ref": "#/definitions/AppapiInfoResp"}}}}}, "/appapi/create": {"post": {"description": "Create appapi information | 创建Appapi信息", "tags": ["appapi"], "summary": "Create appapi information | 创建Appapi信息", "operationId": "CreateAppapi", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/AppapiInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/appapi/delete": {"post": {"description": "Delete appapi information | 删除Appapi信息", "tags": ["appapi"], "summary": "Delete appapi information | 删除Appapi信息", "operationId": "DeleteAppapi", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDsReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/appapi/list": {"post": {"description": "Get appapi list | 获取Appapi信息列表", "tags": ["appapi"], "summary": "Get appapi list | 获取Appapi信息列表", "operationId": "GetAppapiList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/AppapiListReq"}}], "responses": {"200": {"description": "AppapiListResp", "schema": {"$ref": "#/definitions/AppapiListResp"}}}}}, "/appapi/update": {"post": {"description": "Update appapi information | 更新Appapi信息", "tags": ["appapi"], "summary": "Update appapi information | 更新Appapi信息", "operationId": "UpdateAppapi", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/AppapiInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/approle": {"post": {"description": "Get approle by ID | 通过ID获取Approle信息", "tags": ["approle"], "summary": "Get approle by ID | 通过ID获取Approle信息", "operationId": "GetApproleById", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDReq"}}], "responses": {"200": {"description": "ApproleInfoResp", "schema": {"$ref": "#/definitions/ApproleInfoResp"}}}}}, "/approle/create": {"post": {"description": "Create approle information | 创建Approle信息", "tags": ["approle"], "summary": "Create approle information | 创建Approle信息", "operationId": "CreateApprole", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/ApproleInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/approle/delete": {"post": {"description": "Delete approle information | 删除Approle信息", "tags": ["approle"], "summary": "Delete approle information | 删除Approle信息", "operationId": "DeleteApprole", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDsReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/approle/list": {"post": {"description": "Get approle list | 获取Approle信息列表", "tags": ["approle"], "summary": "Get approle list | 获取Approle信息列表", "operationId": "GetApproleList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/ApproleListReq"}}], "responses": {"200": {"description": "ApproleListResp", "schema": {"$ref": "#/definitions/ApproleListResp"}}}}}, "/approle/update": {"post": {"description": "Update approle information | 更新Approle信息", "tags": ["approle"], "summary": "Update approle information | 更新Approle信息", "operationId": "UpdateApprole", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/ApproleInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/area": {"post": {"description": "Get area by ID | 通过ID获取Area", "tags": ["area"], "summary": "Get area by ID | 通过ID获取Area", "operationId": "GetAreaById", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDReq"}}], "responses": {"200": {"description": "AreaInfoResp", "schema": {"$ref": "#/definitions/AreaInfoResp"}}}}}, "/area/create": {"post": {"description": "Create area information | 创建Area", "tags": ["area"], "summary": "Create area information | 创建Area", "operationId": "CreateArea", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/AreaInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/area/delete": {"post": {"description": "Delete area information | 删除Area信息", "tags": ["area"], "summary": "Delete area information | 删除Area信息", "operationId": "DeleteArea", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDsReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/area/list": {"post": {"description": "Get area list | 获取Area列表", "tags": ["area"], "summary": "Get area list | 获取Area列表", "operationId": "GetAreaList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/AreaListReq"}}], "responses": {"200": {"description": "AreaListResp", "schema": {"$ref": "#/definitions/AreaListResp"}}}}}, "/area/update": {"post": {"description": "Update area information | 更新Area", "tags": ["area"], "summary": "Update area information | 更新Area", "operationId": "UpdateArea", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/AreaInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/article": {"post": {"description": "Get article by ID | 通过ID获取Article", "tags": ["article"], "summary": "Get article by ID | 通过ID获取Article", "operationId": "GetArticleById", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDReq"}}], "responses": {"200": {"description": "ArticleInfoResp", "schema": {"$ref": "#/definitions/ArticleInfoResp"}}}}}, "/article/audit": {"post": {"description": "审核文章", "tags": ["article"], "summary": "审核文章", "operationId": "AuditArticle", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/article/cancelTop": {"post": {"description": "取消置顶文章", "tags": ["article"], "summary": "取消置顶文章", "operationId": "CancelTopArticle", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/article/create": {"post": {"description": "Create article information | 创建Article", "tags": ["article"], "summary": "Create article information | 创建Article", "operationId": "CreateArticle", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/ArticleInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/article/delete": {"post": {"description": "Delete article information | 删除Article信息", "tags": ["article"], "summary": "Delete article information | 删除Article信息", "operationId": "DeleteArticle", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDsReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/article/getArticleListWithoutDraft": {"post": {"description": "获取Article列表（排除草稿）", "tags": ["article"], "summary": "获取Article列表（排除草稿）", "operationId": "GetArticleListWithoutDraft", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/ArticleListReq"}}], "responses": {"200": {"description": "ArticleListResp", "schema": {"$ref": "#/definitions/ArticleListResp"}}}}}, "/article/list": {"post": {"description": "Get article list | 获取Article列表", "tags": ["article"], "summary": "Get article list | 获取Article列表", "operationId": "GetArticleList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/ArticleListReq"}}], "responses": {"200": {"description": "ArticleListResp", "schema": {"$ref": "#/definitions/ArticleListResp"}}}}}, "/article/top": {"post": {"description": "置顶文章", "tags": ["article"], "summary": "置顶文章", "operationId": "TopArticle", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/article/update": {"post": {"description": "Update article information | 更新Article", "tags": ["article"], "summary": "Update article information | 更新Article", "operationId": "UpdateArticle", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/ArticleInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/authority/api/create_or_update": {"post": {"description": "Create or update API authorization information | 创建或更新API权限", "tags": ["authority"], "summary": "Create or update API authorization information | 创建或更新API权限", "operationId": "CreateOrUpdateApiAuthority", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/CreateOrUpdateApiAuthorityReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/authority/api/role": {"post": {"description": "Get role's API authorization list | 获取角色api权限列表", "tags": ["authority"], "summary": "Get role's API authorization list | 获取角色api权限列表", "operationId": "GetApiAuthority", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDReq"}}], "responses": {"200": {"description": "ApiAuthorityListResp", "schema": {"$ref": "#/definitions/ApiAuthorityListResp"}}}}}, "/banner": {"post": {"description": "Get banner by ID | 通过ID获取Banner", "tags": ["banner"], "summary": "Get banner by ID | 通过ID获取Banner", "operationId": "GetBannerById", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDReq"}}], "responses": {"200": {"description": "BannerInfoResp", "schema": {"$ref": "#/definitions/BannerInfoResp"}}}}}, "/banner/create": {"post": {"description": "Create banner information | 创建Banner", "tags": ["banner"], "summary": "Create banner information | 创建Banner", "operationId": "CreateBanner", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/BannerInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/banner/delete": {"post": {"description": "Delete banner information | 删除Banner信息", "tags": ["banner"], "summary": "Delete banner information | 删除Banner信息", "operationId": "DeleteBanner", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDsReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/banner/list": {"post": {"description": "Get banner list | 获取Banner列表", "tags": ["banner"], "summary": "Get banner list | 获取Banner列表", "operationId": "GetBannerList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/BannerListReq"}}], "responses": {"200": {"description": "BannerListResp", "schema": {"$ref": "#/definitions/BannerListResp"}}}}}, "/banner/update": {"post": {"description": "Update banner information | 更新Banner", "tags": ["banner"], "summary": "Update banner information | 更新Banner", "operationId": "UpdateBanner", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/BannerInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/bolo_lexicon": {"post": {"description": "Get bolo lexicon by ID | 通过ID获取BoloLexicon信息", "tags": ["bololexicon"], "summary": "Get bolo lexicon by ID | 通过ID获取BoloLexicon信息", "operationId": "GetBoloLexiconById", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDReq"}}], "responses": {"200": {"description": "BoloLexiconInfoResp", "schema": {"$ref": "#/definitions/BoloLexiconInfoResp"}}}}}, "/bolo_lexicon/create": {"post": {"description": "Create bolo lexicon information | 创建BoloLexicon信息", "tags": ["bololexicon"], "summary": "Create bolo lexicon information | 创建BoloLexicon信息", "operationId": "CreateBoloLexicon", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/BoloLexiconInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/bolo_lexicon/delete": {"post": {"description": "Delete bolo lexicon information | 删除BoloLexicon信息", "tags": ["bololexicon"], "summary": "Delete bolo lexicon information | 删除BoloLexicon信息", "operationId": "DeleteBoloLexicon", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDsReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/bolo_lexicon/import": {"post": {"description": "Batch import bolo lexicon | 批量导入违禁词", "tags": ["bololexicon"], "summary": "Batch import bolo lexicon | 批量导入违禁词", "operationId": "ImportBoloLexicon", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/BoloLexiconImportReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/bolo_lexicon/list": {"post": {"description": "Get bolo lexicon list | 获取BoloLexicon信息列表", "tags": ["bololexicon"], "summary": "Get bolo lexicon list | 获取BoloLexicon信息列表", "operationId": "GetBoloLexiconList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/BoloLexiconListReq"}}], "responses": {"200": {"description": "BoloLexiconListResp", "schema": {"$ref": "#/definitions/BoloLexiconListResp"}}}}}, "/bolo_lexicon/update": {"post": {"description": "Update bolo lexicon information | 更新BoloLexicon信息", "tags": ["bololexicon"], "summary": "Update bolo lexicon information | 更新BoloLexicon信息", "operationId": "UpdateBoloLexicon", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/BoloLexiconInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/city": {"post": {"description": "Get city by ID | 通过ID获取City", "tags": ["city"], "summary": "Get city by ID | 通过ID获取City", "operationId": "GetCityById", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDReq"}}], "responses": {"200": {"description": "CityInfoResp", "schema": {"$ref": "#/definitions/CityInfoResp"}}}}}, "/city/create": {"post": {"description": "Create city information | 创建City", "tags": ["city"], "summary": "Create city information | 创建City", "operationId": "CreateCity", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/CityInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/city/delete": {"post": {"description": "Delete city information | 删除City信息", "tags": ["city"], "summary": "Delete city information | 删除City信息", "operationId": "DeleteCity", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDsReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/city/list": {"post": {"description": "Get city list | 获取City列表", "tags": ["city"], "summary": "Get city list | 获取City列表", "operationId": "GetCityList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/CityListReq"}}], "responses": {"200": {"description": "CityListResp", "schema": {"$ref": "#/definitions/CityListResp"}}}}}, "/city/update": {"post": {"description": "Update city information | 更新City", "tags": ["city"], "summary": "Update city information | 更新City", "operationId": "UpdateCity", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/CityInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/group": {"post": {"description": "Get group by ID | 通过ID获取Group", "tags": ["group"], "summary": "Get group by ID | 通过ID获取Group", "operationId": "GetGroupById", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDReq"}}], "responses": {"200": {"description": "GroupInfoResp", "schema": {"$ref": "#/definitions/GroupInfoResp"}}}}}, "/group/create": {"post": {"description": "Create group information | 创建Group", "tags": ["group"], "summary": "Create group information | 创建Group", "operationId": "CreateGroup", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/GroupInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/group/delete": {"post": {"description": "Delete group information | 删除Group信息", "tags": ["group"], "summary": "Delete group information | 删除Group信息", "operationId": "DeleteGroup", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDsReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/group/list": {"post": {"description": "Get group list | 获取Group列表", "tags": ["group"], "summary": "Get group list | 获取Group列表", "operationId": "GetGroupList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/GroupListReq"}}], "responses": {"200": {"description": "GroupListResp", "schema": {"$ref": "#/definitions/GroupListResp"}}}}}, "/group/update": {"post": {"description": "Update group information | 更新Group", "tags": ["group"], "summary": "Update group information | 更新Group", "operationId": "UpdateGroup", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/GroupInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/group_follow": {"post": {"description": "Get group follow by ID | 通过ID获取GroupFollow", "tags": ["groupfollow"], "summary": "Get group follow by ID | 通过ID获取GroupFollow", "operationId": "GetGroupFollowById", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDReq"}}], "responses": {"200": {"description": "GroupFollowInfoResp", "schema": {"$ref": "#/definitions/GroupFollowInfoResp"}}}}}, "/group_follow/create": {"post": {"description": "Create group follow information | 创建GroupFollow", "tags": ["groupfollow"], "summary": "Create group follow information | 创建GroupFollow", "operationId": "CreateGroupFollow", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/GroupFollowInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/group_follow/delete": {"post": {"description": "Delete group follow information | 删除GroupFollow信息", "tags": ["groupfollow"], "summary": "Delete group follow information | 删除GroupFollow信息", "operationId": "DeleteGroupFollow", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDsReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/group_follow/list": {"post": {"description": "Get group follow list | 获取GroupFollow列表", "tags": ["groupfollow"], "summary": "Get group follow list | 获取GroupFollow列表", "operationId": "GetGroupFollowList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/GroupFollowListReq"}}], "responses": {"200": {"description": "GroupFollowListResp", "schema": {"$ref": "#/definitions/GroupFollowListResp"}}}}}, "/group_follow/update": {"post": {"description": "Update group follow information | 更新GroupFollow", "tags": ["groupfollow"], "summary": "Update group follow information | 更新GroupFollow", "operationId": "UpdateGroupFollow", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/GroupFollowInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/group_member": {"post": {"description": "Get group member by ID | 通过ID获取GroupMember", "tags": ["groupmember"], "summary": "Get group member by ID | 通过ID获取GroupMember", "operationId": "GetGroupMemberById", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDReq"}}], "responses": {"200": {"description": "GroupMemberInfoResp", "schema": {"$ref": "#/definitions/GroupMemberInfoResp"}}}}}, "/group_member/create": {"post": {"description": "Create group member information | 创建GroupMember", "tags": ["groupmember"], "summary": "Create group member information | 创建GroupMember", "operationId": "CreateGroupMember", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/GroupMemberInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/group_member/delete": {"post": {"description": "Delete group member information | 删除GroupMember信息", "tags": ["groupmember"], "summary": "Delete group member information | 删除GroupMember信息", "operationId": "DeleteGroupMember", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDsReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/group_member/list": {"post": {"description": "Get group member list | 获取GroupMember列表", "tags": ["groupmember"], "summary": "Get group member list | 获取GroupMember列表", "operationId": "GetGroupMemberList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/GroupMemberListReq"}}], "responses": {"200": {"description": "GroupMemberListResp", "schema": {"$ref": "#/definitions/GroupMemberListResp"}}}}}, "/group_member/update": {"post": {"description": "Update group member information | 更新GroupMember", "tags": ["groupmember"], "summary": "Update group member information | 更新GroupMember", "operationId": "UpdateGroupMember", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/GroupMemberInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/health": {"get": {"tags": ["health"], "operationId": "HealthCheck"}}, "/init/database": {"get": {"description": "Initialize database | 初始化数据库", "tags": ["base"], "summary": "Initialize database | 初始化数据库", "operationId": "InitDatabase", "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/mainten_info": {"post": {"description": "获取维修记录详情", "tags": ["mainteninfo"], "summary": "获取维修记录详情", "operationId": "GetMaintenInfoById", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDReq"}}], "responses": {"200": {"description": "MaintenInfoInfoResp", "schema": {"$ref": "#/definitions/MaintenInfoInfoResp"}}}}}, "/mainten_info/create": {"post": {"description": "创建维修记录", "tags": ["mainteninfo"], "summary": "创建维修记录", "operationId": "CreateMaintenInfo", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/MaintenInfoInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/mainten_info/delete": {"post": {"description": "删除维修记录", "tags": ["mainteninfo"], "summary": "删除维修记录", "operationId": "DeleteMaintenInfo", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDsReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/mainten_info/list": {"post": {"description": "获取维修记录列表", "tags": ["mainteninfo"], "summary": "获取维修记录列表", "operationId": "GetMaintenInfoList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/MaintenInfoListReq"}}], "responses": {"200": {"description": "MaintenInfoListResp", "schema": {"$ref": "#/definitions/MaintenInfoListResp"}}}}}, "/mainten_info/update": {"post": {"description": "更新维修记录", "tags": ["mainteninfo"], "summary": "更新维修记录", "operationId": "UpdateMaintenInfo", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/MaintenInfoInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/mainten_order": {"post": {"description": "Get mainten order by ID | 通过ID获取MaintenOrder", "tags": ["maintenorder"], "summary": "Get mainten order by ID | 通过ID获取MaintenOrder", "operationId": "GetMaintenOrderById", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDReq"}}], "responses": {"200": {"description": "MaintenOrderInfoResp", "schema": {"$ref": "#/definitions/MaintenOrderInfoResp"}}}}}, "/mainten_order/create": {"post": {"description": "Create mainten order information | 创建MaintenOrder", "tags": ["maintenorder"], "summary": "Create mainten order information | 创建MaintenOrder", "operationId": "CreateMaintenOrder", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/MaintenOrderInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/mainten_order/delete": {"post": {"description": "Delete mainten order information | 删除MaintenOrder信息", "tags": ["maintenorder"], "summary": "Delete mainten order information | 删除MaintenOrder信息", "operationId": "DeleteMaintenOrder", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDsReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/mainten_order/list": {"post": {"description": "Get mainten order list | 获取MaintenOrder列表", "tags": ["maintenorder"], "summary": "Get mainten order list | 获取MaintenOrder列表", "operationId": "GetMaintenOrderList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/MaintenOrderListReq"}}], "responses": {"200": {"description": "MaintenOrderListResp", "schema": {"$ref": "#/definitions/MaintenOrderListResp"}}}}}, "/mainten_order/update": {"post": {"description": "Update mainten order information | 更新MaintenOrder", "tags": ["maintenorder"], "summary": "Update mainten order information | 更新MaintenOrder", "operationId": "UpdateMaintenOrder", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/MaintenOrderInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/mall_application": {"post": {"description": "Get mall application by ID | 通过ID获取MallApplication信息", "tags": ["mallapplication"], "summary": "Get mall application by ID | 通过ID获取MallApplication信息", "operationId": "GetMallApplicationById", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDReq"}}], "responses": {"200": {"description": "MallApplicationInfoResp", "schema": {"$ref": "#/definitions/MallApplicationInfoResp"}}}}}, "/mall_application/create": {"post": {"description": "Create mall application information | 创建MallApplication信息", "tags": ["mallapplication"], "summary": "Create mall application information | 创建MallApplication信息", "operationId": "CreateMallApplication", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/MallApplicationInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/mall_application/delete": {"post": {"description": "Delete mall application information | 删除MallApplication信息", "tags": ["mallapplication"], "summary": "Delete mall application information | 删除MallApplication信息", "operationId": "DeleteMallApplication", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDsReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/mall_application/list": {"post": {"description": "Get mall application list | 获取MallApplication信息列表", "tags": ["mallapplication"], "summary": "Get mall application list | 获取MallApplication信息列表", "operationId": "GetMallApplicationList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/MallApplicationListReq"}}], "responses": {"200": {"description": "MallApplicationListResp", "schema": {"$ref": "#/definitions/MallApplicationListResp"}}}}}, "/mall_application/update": {"post": {"description": "Update mall application information | 更新MallApplication信息", "tags": ["mallapplication"], "summary": "Update mall application information | 更新MallApplication信息", "operationId": "UpdateMallApplication", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/MallApplicationInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/mall_delivery": {"post": {"description": "Get mall delivery by ID | 通过ID获取MallDelivery信息", "tags": ["malldelivery"], "summary": "Get mall delivery by ID | 通过ID获取MallDelivery信息", "operationId": "GetMallDeliveryById", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDReq"}}], "responses": {"200": {"description": "MallDeliveryInfoResp", "schema": {"$ref": "#/definitions/MallDeliveryInfoResp"}}}}}, "/mall_delivery/create": {"post": {"description": "Create mall delivery information | 创建MallDelivery信息", "tags": ["malldelivery"], "summary": "Create mall delivery information | 创建MallDelivery信息", "operationId": "CreateMallDelivery", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/MallDeliveryInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/mall_delivery/delete": {"post": {"description": "Delete mall delivery information | 删除MallDelivery信息", "tags": ["malldelivery"], "summary": "Delete mall delivery information | 删除MallDelivery信息", "operationId": "DeleteMallDelivery", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDsReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/mall_delivery/list": {"post": {"description": "Get mall delivery list | 获取MallDelivery信息列表", "tags": ["malldelivery"], "summary": "Get mall delivery list | 获取MallDelivery信息列表", "operationId": "GetMallDeliveryList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/MallDeliveryListReq"}}], "responses": {"200": {"description": "MallDeliveryListResp", "schema": {"$ref": "#/definitions/MallDeliveryListResp"}}}}}, "/mall_delivery/update": {"post": {"description": "Update mall delivery information | 更新MallDelivery信息", "tags": ["malldelivery"], "summary": "Update mall delivery information | 更新MallDelivery信息", "operationId": "UpdateMallDelivery", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/MallDeliveryInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/mall_license": {"post": {"description": "Get mall license by ID | 通过ID获取许可证信息", "tags": ["malllicense"], "summary": "Get mall license by ID | 通过ID获取许可证信息", "operationId": "GetMallLicenseById", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDReq"}}], "responses": {"200": {"description": "MallLicenseInfoResp", "schema": {"$ref": "#/definitions/MallLicenseInfoResp"}}}}}, "/mall_license/batch_create": {"post": {"description": "Batch create mall license | 批量添加许可证", "tags": ["malllicense"], "summary": "Batch create mall license | 批量添加许可证", "operationId": "BatchCreateMallLicense", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/BatchCreateMallLicenseReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/mall_license/list": {"post": {"description": "Get mall license list | 获取许可证信息列表", "tags": ["malllicense"], "summary": "Get mall license list | 获取许可证信息列表", "operationId": "GetMallLicenseList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/MallLicenseListReq"}}], "responses": {"200": {"description": "MallLicenseListResp", "schema": {"$ref": "#/definitions/MallLicenseListResp"}}}}}, "/mall_license_device": {"post": {"description": "Get mall license device by ID | 通过ID获取许可证设备信息", "tags": ["malllicensedevice"], "summary": "Get mall license device by ID | 通过ID获取许可证设备信息", "operationId": "GetMallLicenseDeviceById", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDReq"}}], "responses": {"200": {"description": "MallLicenseDeviceInfoResp", "schema": {"$ref": "#/definitions/MallLicenseDeviceInfoResp"}}}}}, "/mall_license_device/list": {"post": {"description": "Get mall license device list | 获取许可证设备信息列表", "tags": ["malllicensedevice"], "summary": "Get mall license device list | 获取许可证设备信息列表", "operationId": "GetMallLicenseDeviceList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/MallLicenseDeviceListReq"}}], "responses": {"200": {"description": "MallLicenseDeviceListResp", "schema": {"$ref": "#/definitions/MallLicenseDeviceListResp"}}}}}, "/mall_license_device/unbind": {"post": {"description": "Unbind mall license device | 解绑许可证设备", "tags": ["malllicensedevice"], "summary": "Unbind mall license device | 解绑许可证设备", "operationId": "UnbindMallLicenseDevice", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/UnbindMallLicenseDeviceReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/mall_order": {"post": {"description": "Get mall order by ID | 通过ID获取MallOrder信息", "tags": ["mallorder"], "summary": "Get mall order by ID | 通过ID获取MallOrder信息", "operationId": "GetMallOrderById", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDReq"}}], "responses": {"200": {"description": "MallOrderInfoResp", "schema": {"$ref": "#/definitions/MallOrderInfoResp"}}}}}, "/mall_order/create": {"post": {"description": "Create mall order information | 创建MallOrder信息", "tags": ["mallorder"], "summary": "Create mall order information | 创建MallOrder信息", "operationId": "CreateMallOrder", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/MallOrderInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/mall_order/delete": {"post": {"description": "Delete mall order information | 删除MallOrder信息", "tags": ["mallorder"], "summary": "Delete mall order information | 删除MallOrder信息", "operationId": "DeleteMallOrder", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDsReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/mall_order/list": {"post": {"description": "Get mall order list | 获取MallOrder信息列表", "tags": ["mallorder"], "summary": "Get mall order list | 获取MallOrder信息列表", "operationId": "GetMallOrderList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/MallOrderListReq"}}], "responses": {"200": {"description": "MallOrderListResp", "schema": {"$ref": "#/definitions/MallOrderListResp"}}}}}, "/mall_order/update": {"post": {"description": "Update mall order information | 更新MallOrder信息", "tags": ["mallorder"], "summary": "Update mall order information | 更新MallOrder信息", "operationId": "UpdateMallOrder", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/MallOrderInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/mall_order_item": {"post": {"description": "Get mall order item by ID | 通过ID获取MallOrderItem信息", "tags": ["mallorderitem"], "summary": "Get mall order item by ID | 通过ID获取MallOrderItem信息", "operationId": "GetMallOrderItemById", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDReq"}}], "responses": {"200": {"description": "MallOrderItemInfoResp", "schema": {"$ref": "#/definitions/MallOrderItemInfoResp"}}}}}, "/mall_order_item/create": {"post": {"description": "Create mall order item information | 创建MallOrderItem信息", "tags": ["mallorderitem"], "summary": "Create mall order item information | 创建MallOrderItem信息", "operationId": "CreateMallOrderItem", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/MallOrderItemInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/mall_order_item/delete": {"post": {"description": "Delete mall order item information | 删除MallOrderItem信息", "tags": ["mallorderitem"], "summary": "Delete mall order item information | 删除MallOrderItem信息", "operationId": "DeleteMallOrderItem", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDsReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/mall_order_item/list": {"post": {"description": "Get mall order item list | 获取MallOrderItem信息列表", "tags": ["mallorderitem"], "summary": "Get mall order item list | 获取MallOrderItem信息列表", "operationId": "GetMallOrderItemList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/MallOrderItemListReq"}}], "responses": {"200": {"description": "MallOrderItemListResp", "schema": {"$ref": "#/definitions/MallOrderItemListResp"}}}}}, "/mall_order_item/update": {"post": {"description": "Update mall order item information | 更新MallOrderItem信息", "tags": ["mallorderitem"], "summary": "Update mall order item information | 更新MallOrderItem信息", "operationId": "UpdateMallOrderItem", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/MallOrderItemInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/mall_order_log": {"post": {"description": "Get mall order log by ID | 通过ID获取MallOrderLog信息", "tags": ["mallorderlog"], "summary": "Get mall order log by ID | 通过ID获取MallOrderLog信息", "operationId": "GetMallOrderLogById", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDReq"}}], "responses": {"200": {"description": "MallOrderLogInfoResp", "schema": {"$ref": "#/definitions/MallOrderLogInfoResp"}}}}}, "/mall_order_log/create": {"post": {"description": "Create mall order log information | 创建MallOrderLog信息", "tags": ["mallorderlog"], "summary": "Create mall order log information | 创建MallOrderLog信息", "operationId": "CreateMallOrderLog", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/MallOrderLogInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/mall_order_log/delete": {"post": {"description": "Delete mall order log information | 删除MallOrderLog信息", "tags": ["mallorderlog"], "summary": "Delete mall order log information | 删除MallOrderLog信息", "operationId": "DeleteMallOrderLog", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDsReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/mall_order_log/list": {"post": {"description": "Get mall order log list | 获取MallOrderLog信息列表", "tags": ["mallorderlog"], "summary": "Get mall order log list | 获取MallOrderLog信息列表", "operationId": "GetMallOrderLogList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/MallOrderLogListReq"}}], "responses": {"200": {"description": "MallOrderLogListResp", "schema": {"$ref": "#/definitions/MallOrderLogListResp"}}}}}, "/mall_order_log/update": {"post": {"description": "Update mall order log information | 更新MallOrderLog信息", "tags": ["mallorderlog"], "summary": "Update mall order log information | 更新MallOrderLog信息", "operationId": "UpdateMallOrderLog", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/MallOrderLogInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/mall_payment": {"post": {"description": "Get mall payment by ID | 通过ID获取MallPayment信息", "tags": ["mallpayment"], "summary": "Get mall payment by ID | 通过ID获取MallPayment信息", "operationId": "GetMallPaymentById", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDReq"}}], "responses": {"200": {"description": "MallPaymentInfoResp", "schema": {"$ref": "#/definitions/MallPaymentInfoResp"}}}}}, "/mall_payment/create": {"post": {"description": "Create mall payment information | 创建MallPayment信息", "tags": ["mallpayment"], "summary": "Create mall payment information | 创建MallPayment信息", "operationId": "CreateMallPayment", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/MallPaymentInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/mall_payment/delete": {"post": {"description": "Delete mall payment information | 删除MallPayment信息", "tags": ["mallpayment"], "summary": "Delete mall payment information | 删除MallPayment信息", "operationId": "DeleteMallPayment", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDsReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/mall_payment/list": {"post": {"description": "Get mall payment list | 获取MallPayment信息列表", "tags": ["mallpayment"], "summary": "Get mall payment list | 获取MallPayment信息列表", "operationId": "GetMallPaymentList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/MallPaymentListReq"}}], "responses": {"200": {"description": "MallPaymentListResp", "schema": {"$ref": "#/definitions/MallPaymentListResp"}}}}}, "/mall_payment/update": {"post": {"description": "Update mall payment information | 更新MallPayment信息", "tags": ["mallpayment"], "summary": "Update mall payment information | 更新MallPayment信息", "operationId": "UpdateMallPayment", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/MallPaymentInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/mall_payment_log": {"post": {"description": "Get mall payment log by ID | 通过ID获取MallPaymentLog信息", "tags": ["mallpaymentlog"], "summary": "Get mall payment log by ID | 通过ID获取MallPaymentLog信息", "operationId": "GetMallPaymentLogById", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDReq"}}], "responses": {"200": {"description": "MallPaymentLogInfoResp", "schema": {"$ref": "#/definitions/MallPaymentLogInfoResp"}}}}}, "/mall_payment_log/create": {"post": {"description": "Create mall payment log information | 创建MallPaymentLog信息", "tags": ["mallpaymentlog"], "summary": "Create mall payment log information | 创建MallPaymentLog信息", "operationId": "CreateMallPaymentLog", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/MallPaymentLogInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/mall_payment_log/delete": {"post": {"description": "Delete mall payment log information | 删除MallPaymentLog信息", "tags": ["mallpaymentlog"], "summary": "Delete mall payment log information | 删除MallPaymentLog信息", "operationId": "DeleteMallPaymentLog", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDsReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/mall_payment_log/list": {"post": {"description": "Get mall payment log list | 获取MallPaymentLog信息列表", "tags": ["mallpaymentlog"], "summary": "Get mall payment log list | 获取MallPaymentLog信息列表", "operationId": "GetMallPaymentLogList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/MallPaymentLogListReq"}}], "responses": {"200": {"description": "MallPaymentLogListResp", "schema": {"$ref": "#/definitions/MallPaymentLogListResp"}}}}}, "/mall_payment_log/update": {"post": {"description": "Update mall payment log information | 更新MallPaymentLog信息", "tags": ["mallpaymentlog"], "summary": "Update mall payment log information | 更新MallPaymentLog信息", "operationId": "UpdateMallPaymentLog", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/MallPaymentLogInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/mall_product": {"post": {"description": "Get mall product by ID | 通过ID获取MallProduct信息", "tags": ["mallproduct"], "summary": "Get mall product by ID | 通过ID获取MallProduct信息", "operationId": "GetMallProductById", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDReq"}}], "responses": {"200": {"description": "MallProductInfoResp", "schema": {"$ref": "#/definitions/MallProductInfoResp"}}}}}, "/mall_product/create": {"post": {"description": "Create mall product information | 创建MallProduct信息", "tags": ["mallproduct"], "summary": "Create mall product information | 创建MallProduct信息", "operationId": "CreateMallProduct", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/MallProductInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/mall_product/delete": {"post": {"description": "Delete mall product information | 删除MallProduct信息", "tags": ["mallproduct"], "summary": "Delete mall product information | 删除MallProduct信息", "operationId": "DeleteMallProduct", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDsReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/mall_product/list": {"post": {"description": "Get mall product list | 获取MallProduct信息列表", "tags": ["mallproduct"], "summary": "Get mall product list | 获取MallProduct信息列表", "operationId": "GetMallProductList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/MallProductListReq"}}], "responses": {"200": {"description": "MallProductListResp", "schema": {"$ref": "#/definitions/MallProductListResp"}}}}}, "/mall_product/update": {"post": {"description": "Update mall product information | 更新MallProduct信息", "tags": ["mallproduct"], "summary": "Update mall product information | 更新MallProduct信息", "operationId": "UpdateMallProduct", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/MallProductInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/mall_product_category": {"post": {"description": "Get mall product category by ID | 通过ID获取MallProductCategory信息", "tags": ["mallproductcategory"], "summary": "Get mall product category by ID | 通过ID获取MallProductCategory信息", "operationId": "GetMallProductCategoryById", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDReq"}}], "responses": {"200": {"description": "MallProductCategoryInfoResp", "schema": {"$ref": "#/definitions/MallProductCategoryInfoResp"}}}}}, "/mall_product_category/create": {"post": {"description": "Create mall product category information | 创建MallProductCategory信息", "tags": ["mallproductcategory"], "summary": "Create mall product category information | 创建MallProductCategory信息", "operationId": "CreateMallProductCategory", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/MallProductCategoryInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/mall_product_category/delete": {"post": {"description": "Delete mall product category information | 删除MallProductCategory信息", "tags": ["mallproductcategory"], "summary": "Delete mall product category information | 删除MallProductCategory信息", "operationId": "DeleteMallProductCategory", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDsReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/mall_product_category/list": {"post": {"description": "Get mall product category list | 获取MallProductCategory信息列表", "tags": ["mallproductcategory"], "summary": "Get mall product category list | 获取MallProductCategory信息列表", "operationId": "GetMallProductCategoryList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/MallProductCategoryListReq"}}], "responses": {"200": {"description": "MallProductCategoryListResp", "schema": {"$ref": "#/definitions/MallProductCategoryListResp"}}}}}, "/mall_product_category/tree": {"post": {"description": "获取分类树", "tags": ["mallproductcategory"], "summary": "获取分类树", "operationId": "GetMallProductCategoryTree", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/GetMallProductCategoryTreeReq"}}], "responses": {"200": {"description": "GetMallProductCategoryTreeResp", "schema": {"$ref": "#/definitions/GetMallProductCategoryTreeResp"}}}}}, "/mall_product_category/update": {"post": {"description": "Update mall product category information | 更新MallProductCategory信息", "tags": ["mallproductcategory"], "summary": "Update mall product category information | 更新MallProductCategory信息", "operationId": "UpdateMallProductCategory", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/MallProductCategoryInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/mall_product_quota": {"post": {"description": "Get mall product quota by ID | 通过ID获取MallProductQuota信息", "tags": ["mallproductquota"], "summary": "Get mall product quota by ID | 通过ID获取MallProductQuota信息", "operationId": "GetMallProductQuotaById", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDReq"}}], "responses": {"200": {"description": "MallProductQuotaInfoResp", "schema": {"$ref": "#/definitions/MallProductQuotaInfoResp"}}}}}, "/mall_product_quota/create": {"post": {"description": "Create mall product quota information | 创建MallProductQuota信息", "tags": ["mallproductquota"], "summary": "Create mall product quota information | 创建MallProductQuota信息", "operationId": "CreateMallProductQuota", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/MallProductQuotaInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/mall_product_quota/delete": {"post": {"description": "Delete mall product quota information | 删除MallProductQuota信息", "tags": ["mallproductquota"], "summary": "Delete mall product quota information | 删除MallProductQuota信息", "operationId": "DeleteMallProductQuota", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDsReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/mall_product_quota/list": {"post": {"description": "Get mall product quota list | 获取MallProductQuota信息列表", "tags": ["mallproductquota"], "summary": "Get mall product quota list | 获取MallProductQuota信息列表", "operationId": "GetMallProductQuotaList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/MallProductQuotaListReq"}}], "responses": {"200": {"description": "MallProductQuotaListResp", "schema": {"$ref": "#/definitions/MallProductQuotaListResp"}}}}}, "/mall_product_quota/update": {"post": {"description": "Update mall product quota information | 更新MallProductQuota信息", "tags": ["mallproductquota"], "summary": "Update mall product quota information | 更新MallProductQuota信息", "operationId": "UpdateMallProductQuota", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/MallProductQuotaInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/mall_product_sku": {"post": {"description": "Get mall product sku by ID | 通过ID获取MallProductSku信息", "tags": ["mallproductsku"], "summary": "Get mall product sku by ID | 通过ID获取MallProductSku信息", "operationId": "GetMallProductSkuById", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDReq"}}], "responses": {"200": {"description": "MallProductSkuInfoResp", "schema": {"$ref": "#/definitions/MallProductSkuInfoResp"}}}}}, "/mall_product_sku/batch_create": {"post": {"description": "Batch create mall product sku information | 批量创建MallProductSku信息", "tags": ["mallproductsku"], "summary": "Batch create mall product sku information | 批量创建MallProductSku信息", "operationId": "BatchCreateMallProductSku", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/MallProductSkuBatchCreateReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/mall_product_sku/batch_process": {"post": {"description": "Batch process mall product sku | 批量处理商品SKU", "tags": ["mallproductsku"], "summary": "Batch process mall product sku | 批量处理商品SKU", "operationId": "BatchProcessMallProductSku", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/MallProductSkuBatchProcessReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/mall_product_sku/create": {"post": {"description": "Create mall product sku information | 创建MallProductSku信息", "tags": ["mallproductsku"], "summary": "Create mall product sku information | 创建MallProductSku信息", "operationId": "CreateMallProductSku", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/MallProductSkuInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/mall_product_sku/delete": {"post": {"description": "Delete mall product sku information | 删除MallProductSku信息", "tags": ["mallproductsku"], "summary": "Delete mall product sku information | 删除MallProductSku信息", "operationId": "DeleteMallProductSku", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDsReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/mall_product_sku/list": {"post": {"description": "Get mall product sku list | 获取MallProductSku信息列表", "tags": ["mallproductsku"], "summary": "Get mall product sku list | 获取MallProductSku信息列表", "operationId": "GetMallProductSkuList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/MallProductSkuListReq"}}], "responses": {"200": {"description": "MallProductSkuListResp", "schema": {"$ref": "#/definitions/MallProductSkuListResp"}}}}}, "/mall_product_sku/update": {"post": {"description": "Update mall product sku information | 更新MallProductSku信息", "tags": ["mallproductsku"], "summary": "Update mall product sku information | 更新MallProductSku信息", "operationId": "UpdateMallProductSku", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/MallProductSkuInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/mall_refund": {"post": {"description": "Get mall refund by ID | 通过ID获取MallRefund信息", "tags": ["mallrefund"], "summary": "Get mall refund by ID | 通过ID获取MallRefund信息", "operationId": "GetMallRefundById", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDReq"}}], "responses": {"200": {"description": "MallRefundInfoResp", "schema": {"$ref": "#/definitions/MallRefundInfoResp"}}}}}, "/mall_refund/create": {"post": {"description": "Create mall refund information | 创建MallRefund信息", "tags": ["mallrefund"], "summary": "Create mall refund information | 创建MallRefund信息", "operationId": "CreateMallRefund", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/MallRefundInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/mall_refund/delete": {"post": {"description": "Delete mall refund information | 删除MallRefund信息", "tags": ["mallrefund"], "summary": "Delete mall refund information | 删除MallRefund信息", "operationId": "DeleteMallRefund", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDsReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/mall_refund/list": {"post": {"description": "Get mall refund list | 获取MallRefund信息列表", "tags": ["mallrefund"], "summary": "Get mall refund list | 获取MallRefund信息列表", "operationId": "GetMallRefundList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/MallRefundListReq"}}], "responses": {"200": {"description": "MallRefundListResp", "schema": {"$ref": "#/definitions/MallRefundListResp"}}}}}, "/mall_refund/update": {"post": {"description": "Update mall refund information | 更新MallRefund信息", "tags": ["mallrefund"], "summary": "Update mall refund information | 更新MallRefund信息", "operationId": "UpdateMallRefund", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/MallRefundInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/mall_refund_express": {"post": {"description": "Get mall refund express by ID | 通过ID获取MallRefundExpress信息", "tags": ["mallrefundexpress"], "summary": "Get mall refund express by ID | 通过ID获取MallRefundExpress信息", "operationId": "GetMallRefundExpressById", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDReq"}}], "responses": {"200": {"description": "MallRefundExpressInfoResp", "schema": {"$ref": "#/definitions/MallRefundExpressInfoResp"}}}}}, "/mall_refund_express/create": {"post": {"description": "Create mall refund express information | 创建MallRefundExpress信息", "tags": ["mallrefundexpress"], "summary": "Create mall refund express information | 创建MallRefundExpress信息", "operationId": "CreateMallRefundExpress", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/MallRefundExpressInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/mall_refund_express/delete": {"post": {"description": "Delete mall refund express information | 删除MallRefundExpress信息", "tags": ["mallrefundexpress"], "summary": "Delete mall refund express information | 删除MallRefundExpress信息", "operationId": "DeleteMallRefundExpress", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDsReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/mall_refund_express/list": {"post": {"description": "Get mall refund express list | 获取MallRefundExpress信息列表", "tags": ["mallrefundexpress"], "summary": "Get mall refund express list | 获取MallRefundExpress信息列表", "operationId": "GetMallRefundExpressList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/MallRefundExpressListReq"}}], "responses": {"200": {"description": "MallRefundExpressListResp", "schema": {"$ref": "#/definitions/MallRefundExpressListResp"}}}}}, "/mall_refund_express/update": {"post": {"description": "Update mall refund express information | 更新MallRefundExpress信息", "tags": ["mallrefundexpress"], "summary": "Update mall refund express information | 更新MallRefundExpress信息", "operationId": "UpdateMallRefundExpress", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/MallRefundExpressInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/mall_subscription": {"post": {"description": "Get mall subscription by ID | 通过ID获取MallSubscription信息", "tags": ["mallsubscription"], "summary": "Get mall subscription by ID | 通过ID获取MallSubscription信息", "operationId": "GetMallSubscriptionById", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDReq"}}], "responses": {"200": {"description": "MallSubscriptionInfoResp", "schema": {"$ref": "#/definitions/MallSubscriptionInfoResp"}}}}}, "/mall_subscription/create": {"post": {"description": "Create mall subscription information | 创建MallSubscription信息", "tags": ["mallsubscription"], "summary": "Create mall subscription information | 创建MallSubscription信息", "operationId": "CreateMallSubscription", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/MallSubscriptionInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/mall_subscription/delete": {"post": {"description": "Delete mall subscription information | 删除MallSubscription信息", "tags": ["mallsubscription"], "summary": "Delete mall subscription information | 删除MallSubscription信息", "operationId": "DeleteMallSubscription", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDsReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/mall_subscription/list": {"post": {"description": "Get mall subscription list | 获取MallSubscription信息列表", "tags": ["mallsubscription"], "summary": "Get mall subscription list | 获取MallSubscription信息列表", "operationId": "GetMallSubscriptionList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/MallSubscriptionListReq"}}], "responses": {"200": {"description": "MallSubscriptionListResp", "schema": {"$ref": "#/definitions/MallSubscriptionListResp"}}}}}, "/mall_subscription/update": {"post": {"description": "Update mall subscription information | 更新MallSubscription信息", "tags": ["mallsubscription"], "summary": "Update mall subscription information | 更新MallSubscription信息", "operationId": "UpdateMallSubscription", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/MallSubscriptionInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/mall_supplier": {"post": {"description": "Get mall supplier by ID | 通过ID获取供应商信息", "tags": ["mallsupplier"], "summary": "Get mall supplier by ID | 通过ID获取供应商信息", "operationId": "GetMallSupplierById", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDReq"}}], "responses": {"200": {"description": "MallSupplierInfoResp", "schema": {"$ref": "#/definitions/MallSupplierInfoResp"}}}}}, "/mall_supplier/create": {"post": {"description": "Create mall supplier information | 创建供应商信息", "tags": ["mallsupplier"], "summary": "Create mall supplier information | 创建供应商信息", "operationId": "CreateMallSupplier", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/MallSupplierInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/mall_supplier/delete": {"post": {"description": "Delete mall supplier information | 删除供应商信息", "tags": ["mallsupplier"], "summary": "Delete mall supplier information | 删除供应商信息", "operationId": "DeleteMallSupplier", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDsReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/mall_supplier/list": {"post": {"description": "Get mall supplier list | 获取供应商信息列表", "tags": ["mallsupplier"], "summary": "Get mall supplier list | 获取供应商信息列表", "operationId": "GetMallSupplierList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/MallSupplierListReq"}}], "responses": {"200": {"description": "MallSupplierListResp", "schema": {"$ref": "#/definitions/MallSupplierListResp"}}}}}, "/mall_supplier/update": {"post": {"description": "Update mall supplier information | 更新供应商信息", "tags": ["mallsupplier"], "summary": "Update mall supplier information | 更新供应商信息", "operationId": "UpdateMallSupplier", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/MallSupplierInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/mall_supplier_commission": {"post": {"description": "Get mall supplier commission by ID | 通过ID获取供应商分成信息", "tags": ["mallsuppliercommission"], "summary": "Get mall supplier commission by ID | 通过ID获取供应商分成信息", "operationId": "GetMallSupplierCommissionById", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDReq"}}], "responses": {"200": {"description": "MallSupplierCommissionInfoResp", "schema": {"$ref": "#/definitions/MallSupplierCommissionInfoResp"}}}}}, "/mall_supplier_commission/list": {"post": {"description": "Get mall supplier commission list | 获取供应商分成信息列表", "tags": ["mallsuppliercommission"], "summary": "Get mall supplier commission list | 获取供应商分成信息列表", "operationId": "GetMallSupplierCommissionList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/MallSupplierCommissionListReq"}}], "responses": {"200": {"description": "MallSupplierCommissionListResp", "schema": {"$ref": "#/definitions/MallSupplierCommissionListResp"}}}}}, "/mall_usage_record": {"post": {"description": "Get mall usage record by ID | 通过ID获取MallUsageRecord信息", "tags": ["mallusagerecord"], "summary": "Get mall usage record by ID | 通过ID获取MallUsageRecord信息", "operationId": "GetMallUsageRecordById", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDReq"}}], "responses": {"200": {"description": "MallUsageRecordInfoResp", "schema": {"$ref": "#/definitions/MallUsageRecordInfoResp"}}}}}, "/mall_usage_record/create": {"post": {"description": "Create mall usage record information | 创建MallUsageRecord信息", "tags": ["mallusagerecord"], "summary": "Create mall usage record information | 创建MallUsageRecord信息", "operationId": "CreateMallUsageRecord", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/MallUsageRecordInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/mall_usage_record/delete": {"post": {"description": "Delete mall usage record information | 删除MallUsageRecord信息", "tags": ["mallusagerecord"], "summary": "Delete mall usage record information | 删除MallUsageRecord信息", "operationId": "DeleteMallUsageRecord", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDsReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/mall_usage_record/list": {"post": {"description": "Get mall usage record list | 获取MallUsageRecord信息列表", "tags": ["mallusagerecord"], "summary": "Get mall usage record list | 获取MallUsageRecord信息列表", "operationId": "GetMallUsageRecordList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/MallUsageRecordListReq"}}], "responses": {"200": {"description": "MallUsageRecordListResp", "schema": {"$ref": "#/definitions/MallUsageRecordListResp"}}}}}, "/mall_usage_record/update": {"post": {"description": "Update mall usage record information | 更新MallUsageRecord信息", "tags": ["mallusagerecord"], "summary": "Update mall usage record information | 更新MallUsageRecord信息", "operationId": "UpdateMallUsageRecord", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/MallUsageRecordInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/product": {"post": {"description": "获取产品详情", "tags": ["product"], "summary": "获取产品详情", "operationId": "GetProductById", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDReq"}}], "responses": {"200": {"description": "ProductInfoResp", "schema": {"$ref": "#/definitions/ProductInfoResp"}}}}}, "/product/create": {"post": {"description": "添加产品", "tags": ["product"], "summary": "添加产品", "operationId": "CreateProduct", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/ProductInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/product/delete": {"post": {"description": "删除产品", "tags": ["product"], "summary": "删除产品", "operationId": "DeleteProduct", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDsReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/product/list": {"post": {"description": "获取产品列表", "tags": ["product"], "summary": "获取产品列表", "operationId": "GetProductList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/ProductListReq"}}], "responses": {"200": {"description": "ProductListResp", "schema": {"$ref": "#/definitions/ProductListResp"}}}}}, "/product/update": {"post": {"description": "更新产品", "tags": ["product"], "summary": "更新产品", "operationId": "UpdateProduct", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/ProductInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/province": {"post": {"description": "Get province by ID | 通过ID获取Province", "tags": ["province"], "summary": "Get province by ID | 通过ID获取Province", "operationId": "GetProvinceById", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDReq"}}], "responses": {"200": {"description": "ProvinceInfoResp", "schema": {"$ref": "#/definitions/ProvinceInfoResp"}}}}}, "/province/create": {"post": {"description": "Create province information | 创建Province", "tags": ["province"], "summary": "Create province information | 创建Province", "operationId": "CreateProvince", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/ProvinceInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/province/delete": {"post": {"description": "Delete province information | 删除Province信息", "tags": ["province"], "summary": "Delete province information | 删除Province信息", "operationId": "DeleteProvince", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDsReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/province/list": {"post": {"description": "Get province list | 获取Province列表", "tags": ["province"], "summary": "Get province list | 获取Province列表", "operationId": "GetProvinceList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/ProvinceListReq"}}], "responses": {"200": {"description": "ProvinceListResp", "schema": {"$ref": "#/definitions/ProvinceListResp"}}}}}, "/province/update": {"post": {"description": "Update province information | 更新Province", "tags": ["province"], "summary": "Update province information | 更新Province", "operationId": "UpdateProvince", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/ProvinceInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/rental_device": {"post": {"description": "Get rental device by ID | 通过ID获取RentalDevice信息", "tags": ["rentaldevice"], "summary": "Get rental device by ID | 通过ID获取RentalDevice信息", "operationId": "GetRentalDeviceById", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDReq"}}], "responses": {"200": {"description": "RentalDeviceInfoResp", "schema": {"$ref": "#/definitions/RentalDeviceInfoResp"}}}}}, "/rental_device/create": {"post": {"description": "Create rental device information | 创建RentalDevice信息", "tags": ["rentaldevice"], "summary": "Create rental device information | 创建RentalDevice信息", "operationId": "CreateRentalDevice", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/RentalDeviceInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/rental_device/delete": {"post": {"description": "Delete rental device information | 删除RentalDevice信息", "tags": ["rentaldevice"], "summary": "Delete rental device information | 删除RentalDevice信息", "operationId": "DeleteRentalDevice", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDsReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/rental_device/list": {"post": {"description": "Get rental device list | 获取RentalDevice信息列表", "tags": ["rentaldevice"], "summary": "Get rental device list | 获取RentalDevice信息列表", "operationId": "GetRentalDeviceList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/RentalDeviceListReq"}}], "responses": {"200": {"description": "RentalDeviceListResp", "schema": {"$ref": "#/definitions/RentalDeviceListResp"}}}}}, "/rental_device/update": {"post": {"description": "Update rental device information | 更新RentalDevice信息", "tags": ["rentaldevice"], "summary": "Update rental device information | 更新RentalDevice信息", "operationId": "UpdateRentalDevice", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/RentalDeviceInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/rental_device_message": {"post": {"description": "Get rental device message by ID | 通过ID获取RentalDeviceMessage信息", "tags": ["rentaldevicemessage"], "summary": "Get rental device message by ID | 通过ID获取RentalDeviceMessage信息", "operationId": "GetRentalDeviceMessageById", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDReq"}}], "responses": {"200": {"description": "RentalDeviceMessageInfoResp", "schema": {"$ref": "#/definitions/RentalDeviceMessageInfoResp"}}}}}, "/rental_device_message/create": {"post": {"description": "Create rental device message information | 创建RentalDeviceMessage信息", "tags": ["rentaldevicemessage"], "summary": "Create rental device message information | 创建RentalDeviceMessage信息", "operationId": "CreateRentalDeviceMessage", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/RentalDeviceMessageInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/rental_device_message/delete": {"post": {"description": "Delete rental device message information | 删除RentalDeviceMessage信息", "tags": ["rentaldevicemessage"], "summary": "Delete rental device message information | 删除RentalDeviceMessage信息", "operationId": "DeleteRentalDeviceMessage", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDsReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/rental_device_message/list": {"post": {"description": "Get rental device message list | 获取RentalDeviceMessage信息列表", "tags": ["rentaldevicemessage"], "summary": "Get rental device message list | 获取RentalDeviceMessage信息列表", "operationId": "GetRentalDeviceMessageList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/RentalDeviceMessageListReq"}}], "responses": {"200": {"description": "RentalDeviceMessageListResp", "schema": {"$ref": "#/definitions/RentalDeviceMessageListResp"}}}}}, "/rental_device_message/update": {"post": {"description": "Update rental device message information | 更新RentalDeviceMessage信息", "tags": ["rentaldevicemessage"], "summary": "Update rental device message information | 更新RentalDeviceMessage信息", "operationId": "UpdateRentalDeviceMessage", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/RentalDeviceMessageInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/reply": {"post": {"description": "Get reply by ID | 通过ID获取Reply", "tags": ["reply"], "summary": "Get reply by ID | 通过ID获取Reply", "operationId": "GetReplyById", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDReq"}}], "responses": {"200": {"description": "ReplyInfoResp", "schema": {"$ref": "#/definitions/ReplyInfoResp"}}}}}, "/reply/create": {"post": {"description": "Create reply information | 创建Reply", "tags": ["reply"], "summary": "Create reply information | 创建Reply", "operationId": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/ReplyInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/reply/delete": {"post": {"description": "Delete reply information | 删除Reply信息", "tags": ["reply"], "summary": "Delete reply information | 删除Reply信息", "operationId": "DeleteReply", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDsReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/reply/list": {"post": {"description": "Get reply list | 获取Reply列表", "tags": ["reply"], "summary": "Get reply list | 获取Reply列表", "operationId": "GetReplyList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/ReplyListReq"}}], "responses": {"200": {"description": "ReplyListResp", "schema": {"$ref": "#/definitions/ReplyListResp"}}}}}, "/reply/update": {"post": {"description": "Update reply information | 更新Reply", "tags": ["reply"], "summary": "Update reply information | 更新Reply", "operationId": "UpdateReply", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/ReplyInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/segment": {"post": {"description": "Get segment by ID | 通过ID获取Segment", "tags": ["segment"], "summary": "Get segment by ID | 通过ID获取Segment", "operationId": "GetSegmentById", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDReq"}}], "responses": {"200": {"description": "SegmentInfoResp", "schema": {"$ref": "#/definitions/SegmentInfoResp"}}}}}, "/segment/create": {"post": {"description": "Create segment information | 创建Segment", "tags": ["segment"], "summary": "Create segment information | 创建Segment", "operationId": "CreateSegment", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/SegmentInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/segment/delete": {"post": {"description": "Delete segment information | 删除Segment信息", "tags": ["segment"], "summary": "Delete segment information | 删除Segment信息", "operationId": "DeleteSegment", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDsReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/segment/list": {"post": {"description": "Get segment list | 获取Segment列表", "tags": ["segment"], "summary": "Get segment list | 获取Segment列表", "operationId": "GetSegmentList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/SegmentListReq"}}], "responses": {"200": {"description": "SegmentListResp", "schema": {"$ref": "#/definitions/SegmentListResp"}}}}}, "/segment/update": {"post": {"description": "Update segment information | 更新Segment", "tags": ["segment"], "summary": "Update segment information | 更新Segment", "operationId": "UpdateSegment", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/SegmentInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/segment_article_record": {"post": {"description": "Get segment article record by ID | 通过ID获取SegmentArticleRecord", "tags": ["segmentarticlerecord"], "summary": "Get segment article record by ID | 通过ID获取SegmentArticleRecord", "operationId": "GetSegmentArticleRecordById", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDReq"}}], "responses": {"200": {"description": "SegmentArticleRecordInfoResp", "schema": {"$ref": "#/definitions/SegmentArticleRecordInfoResp"}}}}}, "/segment_article_record/create": {"post": {"description": "Create segment article record information | 创建SegmentArticleRecord", "tags": ["segmentarticlerecord"], "summary": "Create segment article record information | 创建SegmentArticleRecord", "operationId": "CreateSegmentArticleRecord", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/SegmentArticleRecordInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/segment_article_record/delete": {"post": {"description": "Delete segment article record information | 删除SegmentArticleRecord信息", "tags": ["segmentarticlerecord"], "summary": "Delete segment article record information | 删除SegmentArticleRecord信息", "operationId": "DeleteSegmentArticleRecord", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDsReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/segment_article_record/list": {"post": {"description": "Get segment article record list | 获取SegmentArticleRecord列表", "tags": ["segmentarticlerecord"], "summary": "Get segment article record list | 获取SegmentArticleRecord列表", "operationId": "GetSegmentArticleRecordList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/SegmentArticleRecordListReq"}}], "responses": {"200": {"description": "SegmentArticleRecordListResp", "schema": {"$ref": "#/definitions/SegmentArticleRecordListResp"}}}}}, "/segment_article_record/update": {"post": {"description": "Update segment article record information | 更新SegmentArticleRecord", "tags": ["segmentarticlerecord"], "summary": "Update segment article record information | 更新SegmentArticleRecord", "operationId": "UpdateSegmentArticleRecord", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/SegmentArticleRecordInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/street": {"post": {"description": "Get street by ID | 通过ID获取Street", "tags": ["street"], "summary": "Get street by ID | 通过ID获取Street", "operationId": "GetStreetById", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDReq"}}], "responses": {"200": {"description": "StreetInfoResp", "schema": {"$ref": "#/definitions/StreetInfoResp"}}}}}, "/street/create": {"post": {"description": "Create street information | 创建Street", "tags": ["street"], "summary": "Create street information | 创建Street", "operationId": "CreateStreet", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/StreetInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/street/delete": {"post": {"description": "Delete street information | 删除Street信息", "tags": ["street"], "summary": "Delete street information | 删除Street信息", "operationId": "DeleteStreet", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDsReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/street/list": {"post": {"description": "Get street list | 获取Street列表", "tags": ["street"], "summary": "Get street list | 获取Street列表", "operationId": "GetStreetList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/StreetListReq"}}], "responses": {"200": {"description": "StreetListResp", "schema": {"$ref": "#/definitions/StreetListResp"}}}}}, "/street/update": {"post": {"description": "Update street information | 更新Street", "tags": ["street"], "summary": "Update street information | 更新Street", "operationId": "UpdateStreet", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/StreetInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/sync/article/commentNum": {"post": {"description": "同步文章回复数", "tags": ["sync"], "summary": "同步文章回复数", "operationId": "SyncArticleCommentNum", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/EmptyReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/sync/article/data": {"post": {"description": "同步文章数据", "tags": ["sync"], "summary": "同步文章数据", "operationId": "SyncArticleData", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/EmptyReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/sync/article/likeNum": {"post": {"description": "同步文章点赞数", "tags": ["sync"], "summary": "同步文章点赞数", "operationId": "SyncArticleLikeNum", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/EmptyReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/sync/article/viewNum": {"post": {"description": "同步文章浏览数", "tags": ["sync"], "summary": "同步文章浏览数", "operationId": "SyncArticleViewNum", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/EmptyReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/sync/followAndFans/info": {"post": {"description": "同步关注、粉丝信息", "tags": ["sync"], "summary": "同步关注、粉丝信息", "operationId": "SyncFollowAndFansInfo", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/EmptyReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/sync/group/info": {"post": {"description": "同步群组信息", "tags": ["sync"], "summary": "同步群组信息", "operationId": "SyncGroupInfo", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/EmptyReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/sync/tag/info": {"post": {"description": "同步话题信息", "tags": ["sync"], "summary": "同步话题信息", "operationId": "SyncTagInfo", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/EmptyReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/sync/user/info": {"post": {"description": "同步用户信息", "tags": ["sync"], "summary": "同步用户信息", "operationId": "SyncUserInfo", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/EmptyReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/tag": {"post": {"description": "Get tag by ID | 通过ID获取Tag", "tags": ["tag"], "summary": "Get tag by ID | 通过ID获取Tag", "operationId": "GetTagById", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDReq"}}], "responses": {"200": {"description": "TagInfoResp", "schema": {"$ref": "#/definitions/TagInfoResp"}}}}}, "/tag/create": {"post": {"description": "Create tag information | 创建Tag", "tags": ["tag"], "summary": "Create tag information | 创建Tag", "operationId": "CreateTag", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/TagInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/tag/delete": {"post": {"description": "Delete tag information | 删除Tag信息", "tags": ["tag"], "summary": "Delete tag information | 删除Tag信息", "operationId": "DeleteTag", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDsReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/tag/group": {"post": {"description": "Get tag By Group ID | 通过Group ID获取Tag", "tags": ["tag"], "summary": "Get tag By Group ID | 通过Group ID获取Tag", "operationId": "GetTagListByGroupId", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/TagRecordListReq"}}], "responses": {"200": {"description": "TagListResp", "schema": {"$ref": "#/definitions/TagListResp"}}}}}, "/tag/list": {"post": {"description": "Get tag list | 获取Tag列表", "tags": ["tag"], "summary": "Get tag list | 获取Tag列表", "operationId": "GetTagList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/TagListReq"}}], "responses": {"200": {"description": "TagListResp", "schema": {"$ref": "#/definitions/TagListResp"}}}}}, "/tag/update": {"post": {"description": "Update tag information | 更新Tag", "tags": ["tag"], "summary": "Update tag information | 更新Tag", "operationId": "UpdateTag", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/TagInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/tag_record": {"post": {"description": "Get tag record by ID | 通过ID获取TagRecord", "tags": ["tagrecord"], "summary": "Get tag record by ID | 通过ID获取TagRecord", "operationId": "GetTagRecordById", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDReq"}}], "responses": {"200": {"description": "TagRecordInfoResp", "schema": {"$ref": "#/definitions/TagRecordInfoResp"}}}}}, "/tag_record/create": {"post": {"description": "Create tag record information | 创建TagRecord", "tags": ["tagrecord"], "summary": "Create tag record information | 创建TagRecord", "operationId": "CreateTagRecord", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/TagRecordInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/tag_record/delete": {"post": {"description": "Delete tag record information | 删除TagRecord信息", "tags": ["tagrecord"], "summary": "Delete tag record information | 删除TagRecord信息", "operationId": "DeleteTagRecord", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDsReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/tag_record/list": {"post": {"description": "Get tag record list | 获取TagRecord列表", "tags": ["tagrecord"], "summary": "Get tag record list | 获取TagRecord列表", "operationId": "GetTagRecordList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/TagRecordListReq"}}], "responses": {"200": {"description": "TagRecordListResp", "schema": {"$ref": "#/definitions/TagRecordListResp"}}}}}, "/tag_record/update": {"post": {"description": "Update tag record information | 更新TagRecord", "tags": ["tagrecord"], "summary": "Update tag record information | 更新TagRecord", "operationId": "UpdateTagRecord", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/TagRecordInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/user": {"post": {"description": "Get user by ID | 通过ID获取User", "tags": ["user"], "summary": "Get user by ID | 通过ID获取User", "operationId": "GetUserById", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDReq"}}], "responses": {"200": {"description": "UserInfoResp", "schema": {"$ref": "#/definitions/UserInfoResp"}}}}}, "/user/create": {"post": {"description": "Create user information | 创建User", "tags": ["user"], "summary": "Create user information | 创建User", "operationId": "CreateUser", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/UserInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/user/delete": {"post": {"description": "Delete user information | 删除User信息", "tags": ["user"], "summary": "Delete user information | 删除User信息", "operationId": "DeleteUser", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDsReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/user/list": {"post": {"description": "Get user list | 获取User列表", "tags": ["user"], "summary": "Get user list | 获取User列表", "operationId": "GetUserList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/UserListReq"}}], "responses": {"200": {"description": "UserListResp", "schema": {"$ref": "#/definitions/UserListResp"}}}}}, "/user/update": {"post": {"description": "Update user information | 更新User", "tags": ["user"], "summary": "Update user information | 更新User", "operationId": "UpdateUser", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/UserInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/user/update/forbidden": {"post": {"description": "Update user forbidden time | 更新用户封禁时间", "tags": ["user"], "summary": "Update user forbidden time | 更新用户封禁时间", "operationId": "UpdateUserForbiddenTime", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/UpdateUserForbiddenTimeReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/user/update/muted": {"post": {"description": "Update user muted time | 更新用户禁言时间", "tags": ["user"], "summary": "Update user muted time | 更新用户禁言时间", "operationId": "UpdateUserMutedTime", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/UpdateUserMutedTimeReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/user_addres": {"post": {"description": "Get user addres by ID | 通过ID获取UserAddres", "tags": ["useraddres"], "summary": "Get user addres by ID | 通过ID获取UserAddres", "operationId": "GetUserAddresById", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDReq"}}], "responses": {"200": {"description": "UserAddresInfoResp", "schema": {"$ref": "#/definitions/UserAddresInfoResp"}}}}}, "/user_addres/create": {"post": {"description": "Create user addres information | 创建UserAddres", "tags": ["useraddres"], "summary": "Create user addres information | 创建UserAddres", "operationId": "CreateUserAddres", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/UserAddresInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/user_addres/delete": {"post": {"description": "Delete user addres information | 删除UserAddres信息", "tags": ["useraddres"], "summary": "Delete user addres information | 删除UserAddres信息", "operationId": "DeleteUserAddres", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDsReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/user_addres/list": {"post": {"description": "Get user addres list | 获取UserAddres列表", "tags": ["useraddres"], "summary": "Get user addres list | 获取UserAddres列表", "operationId": "GetUserAddresList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/UserAddresListReq"}}], "responses": {"200": {"description": "UserAddresListResp", "schema": {"$ref": "#/definitions/UserAddresListResp"}}}}}, "/user_addres/update": {"post": {"description": "Update user addres information | 更新UserAddres", "tags": ["useraddres"], "summary": "Update user addres information | 更新UserAddres", "operationId": "UpdateUserAddres", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/UserAddresInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}}, "definitions": {"AnalyticsApplicationInfo": {"description": "The response data of analytics application information | AnalyticsApplication信息", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "AnalyticsApplicationInfoResp": {"description": "The analytics application information response | AnalyticsApplication信息返回体", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "AnalyticsApplicationListInfo": {"description": "The analytics application list data | AnalyticsApplication信息列表数据", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "AnalyticsApplicationListReq": {"description": "Get analytics application list request params | AnalyticsApplication列表请求参数", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "AnalyticsApplicationListResp": {"description": "The response data of analytics application list | AnalyticsApplication信息列表数据", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "AnalyticsCohortInfo": {"description": "The response data of analytics cohort information | AnalyticsCohort信息", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "AnalyticsCohortInfoResp": {"description": "The analytics cohort information response | AnalyticsCohort信息返回体", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "AnalyticsCohortListInfo": {"description": "The analytics cohort list data | AnalyticsCohort信息列表数据", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "AnalyticsCohortListReq": {"description": "Get analytics cohort list request params | AnalyticsCohort列表请求参数", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "AnalyticsCohortListResp": {"description": "The response data of analytics cohort list | AnalyticsCohort信息列表数据", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "AnalyticsDailySummaryInfo": {"description": "The response data of analytics daily summary information | AnalyticsDailySummary信息", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "AnalyticsDailySummaryInfoResp": {"description": "The analytics daily summary information response | AnalyticsDailySummary信息返回体", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "AnalyticsDailySummaryListInfo": {"description": "The analytics daily summary list data | AnalyticsDailySummary信息列表数据", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "AnalyticsDailySummaryListReq": {"description": "Get analytics daily summary list request params | AnalyticsDailySummary列表请求参数", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "AnalyticsDailySummaryListResp": {"description": "The response data of analytics daily summary list | AnalyticsDailySummary信息列表数据", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "AnalyticsDashboardReq": {"description": "仪表盘数据请求", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "AnalyticsDashboardResp": {"description": "仪表盘数据响应", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "AnalyticsDeviceInfo": {"description": "The response data of analytics device information | AnalyticsDevice信息", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "AnalyticsDeviceInfoResp": {"description": "The analytics device information response | AnalyticsDevice信息返回体", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "AnalyticsDeviceListInfo": {"description": "The analytics device list data | AnalyticsDevice信息列表数据", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "AnalyticsDeviceListReq": {"description": "Get analytics device list request params | AnalyticsDevice列表请求参数", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "AnalyticsDeviceListResp": {"description": "The response data of analytics device list | AnalyticsDevice信息列表数据", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "AnalyticsEventDefinitionInfo": {"description": "The response data of analytics event definition information | AnalyticsEventDefinition信息", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "AnalyticsEventDefinitionInfoResp": {"description": "The analytics event definition information response | AnalyticsEventDefinition信息返回体", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "AnalyticsEventDefinitionListInfo": {"description": "The analytics event definition list data | AnalyticsEventDefinition信息列表数据", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "AnalyticsEventDefinitionListReq": {"description": "Get analytics event definition list request params | AnalyticsEventDefinition列表请求参数", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "AnalyticsEventDefinitionListResp": {"description": "The response data of analytics event definition list | AnalyticsEventDefinition信息列表数据", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "AnalyticsEventInfo": {"description": "The response data of analytics event information | AnalyticsEvent信息", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "AnalyticsEventInfoResp": {"description": "The analytics event information response | AnalyticsEvent信息返回体", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "AnalyticsEventListInfo": {"description": "The analytics event list data | AnalyticsEvent信息列表数据", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "AnalyticsEventListReq": {"description": "Get analytics event list request params | AnalyticsEvent列表请求参数", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "AnalyticsEventListResp": {"description": "The response data of analytics event list | AnalyticsEvent信息列表数据", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "AnalyticsRealTimeMetricInfo": {"description": "The response data of analytics real time metric information | AnalyticsRealTimeMetric信息", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "AnalyticsRealTimeMetricInfoResp": {"description": "The analytics real time metric information response | AnalyticsRealTimeMetric信息返回体", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "AnalyticsRealTimeMetricListInfo": {"description": "The analytics real time metric list data | AnalyticsRealTimeMetric信息列表数据", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "AnalyticsRealTimeMetricListReq": {"description": "Get analytics real time metric list request params | AnalyticsRealTimeMetric列表请求参数", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "AnalyticsRealTimeMetricListResp": {"description": "The response data of analytics real time metric list | AnalyticsRealTimeMetric信息列表数据", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "AnalyticsUserInfo": {"description": "The response data of analytics user information | AnalyticsUser信息", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "AnalyticsUserInfoResp": {"description": "The analytics user information response | AnalyticsUser信息返回体", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "AnalyticsUserListInfo": {"description": "The analytics user list data | AnalyticsUser信息列表数据", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "AnalyticsUserListReq": {"description": "Get analytics user list request params | AnalyticsUser列表请求参数", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "AnalyticsUserListResp": {"description": "The response data of analytics user list | AnalyticsUser信息列表数据", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "ApiAuthorityInfo": {"description": "The response data of api authorization | API授权数据", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "ApiAuthorityListInfo": {"description": "The  data of api authorization list | API授权列表数据", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "ApiAuthorityListResp": {"description": "The response data of api authorization list | API授权列表返回数据", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "AppVersionInfo": {"description": "The response data of app version information | AppVersion信息", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "AppVersionInfoResp": {"description": "AppVersion information response | AppVersion信息返回体", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "AppVersionListInfo": {"description": "AppVersion list data | AppVersion列表数据", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "AppVersionListReq": {"description": "Get app version list request params | AppVersion列表请求参数", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "AppVersionListResp": {"description": "The response data of app version list | AppVersion列表数据", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "AppapiInfo": {"description": "The response data of appapi information | Appapi信息", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "AppapiInfoResp": {"description": "The appapi information response | Appapi信息返回体", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "AppapiListInfo": {"description": "The appapi list data | Appapi信息列表数据", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "AppapiListReq": {"description": "Get appapi list request params | Appapi列表请求参数", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "AppapiListResp": {"description": "The response data of appapi list | Appapi信息列表数据", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "ApproleInfo": {"description": "The response data of approle information | Approle信息", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "ApproleInfoResp": {"description": "The approle information response | Approle信息返回体", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "ApproleListInfo": {"description": "The approle list data | Approle信息列表数据", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "ApproleListReq": {"description": "Get approle list request params | Approle列表请求参数", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "ApproleListResp": {"description": "The response data of approle list | Approle信息列表数据", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "AreaInfo": {"description": "The response data of area information | Area信息", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "AreaInfoResp": {"description": "Area information response | Area信息返回体", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "AreaListInfo": {"description": "Area list data | Area列表数据", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "AreaListReq": {"description": "Get area list request params | Area列表请求参数", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "AreaListResp": {"description": "The response data of area list | Area列表数据", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "ArticleInfo": {"description": "The response data of article information | Article信息", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "ArticleInfoResp": {"description": "Article information response | Article信息返回体", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "ArticleListInfo": {"description": "Article list data | Article列表数据", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "ArticleListReq": {"description": "Get article list request params | Article列表请求参数", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "ArticleListResp": {"description": "The response data of article list | Article列表数据", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "BannerInfo": {"description": "The response data of banner information | Banner信息", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "BannerInfoResp": {"description": "Banner information response | Banner信息返回体", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "BannerListInfo": {"description": "Banner list data | Banner列表数据", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "BannerListReq": {"description": "Get banner list request params | Banner列表请求参数", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "BannerListResp": {"description": "The response data of banner list | Banner列表数据", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "BaseDataInfo": {"description": "The basic response with data | 基础带数据信息", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "BaseIDInfo": {"description": "The base ID response data | 基础ID信息", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "BaseIDInt32Info": {"description": "The base ID response data (int32) | 基础ID信息 (int32)", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "BaseIDInt64Info": {"description": "The base ID response data (int64) | 基础ID信息 (int64)", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "BaseIDUint32Info": {"description": "The base ID response data (uint32) | 基础ID信息 (uint32)", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "BaseListInfo": {"description": "The basic response with data | 基础带数据信息", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "BaseMsgResp": {"description": "The basic response without data | 基础不带数据信息", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "BaseUUIDInfo": {"description": "The base UUID response data | 基础UUID信息", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "BatchCreateMallLicenseReq": {"description": "The request data of batch create mall license | 批量创建许可证请求数据", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "BoloLexiconImportReq": {"x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "BoloLexiconInfo": {"description": "The response data of bolo lexicon information | BoloLexicon信息", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "BoloLexiconInfoResp": {"description": "The bolo lexicon information response | BoloLexicon信息返回体", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "BoloLexiconListInfo": {"description": "The bolo lexicon list data | BoloLexicon信息列表数据", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "BoloLexiconListReq": {"description": "Get bolo lexicon list request params | BoloLexicon列表请求参数", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "BoloLexiconListResp": {"description": "The response data of bolo lexicon list | BoloLexicon信息列表数据", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "CityInfo": {"description": "The response data of city information | City信息", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "CityInfoResp": {"description": "City information response | City信息返回体", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "CityListInfo": {"description": "City list data | City列表数据", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "CityListReq": {"description": "Get city list request params | City列表请求参数", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "CityListResp": {"description": "The response data of city list | City列表数据", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "CreateOrUpdateApiAuthorityReq": {"description": "Create or update api authorization information request | 创建或更新API授权信息", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "EmptyReq": {"description": "Empty request | 无参数请求", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "EventConversionsResp": {"description": "事件转换响应", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "EventTrendsResp": {"description": "事件趋势响应", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "ExportDataResp": {"description": "导出数据响应", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "FunnelAnalysisResp": {"description": "漏斗分析响应", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "FunnelConditionInfo": {"description": "漏斗条件信息", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "FunnelConditionInfoResp": {"description": "漏斗条件信息响应", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "FunnelConditionListInfo": {"description": "漏斗条件列表数据", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "FunnelConditionListReq": {"description": "漏斗条件列表请求", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "FunnelConditionListResp": {"description": "漏斗条件列表响应", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "FunnelInfo": {"description": "漏斗信息", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "FunnelInfoResp": {"description": "漏斗信息响应", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "FunnelListInfo": {"description": "漏斗列表数据", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "FunnelListReq": {"description": "漏斗列表请求", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "FunnelListResp": {"description": "漏斗列表响应", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "FunnelStepInfo": {"description": "漏斗步骤信息", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "FunnelStepInfoResp": {"description": "漏斗步骤信息响应", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "FunnelStepListInfo": {"description": "漏斗步骤列表数据", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "FunnelStepListReq": {"description": "漏斗步骤列表请求", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "FunnelStepListResp": {"description": "漏斗步骤列表响应", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "GenerateApiSecretReq": {"description": "生成API密钥请求", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "GeographicDataResp": {"description": "地理分布数据响应", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "GetMallProductCategoryTreeReq": {"description": "获取分类树请求", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "GetMallProductCategoryTreeResp": {"description": "获取分类树响应", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "GroupFollowInfo": {"description": "The response data of group follow information | GroupFollow信息", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "GroupFollowInfoResp": {"description": "GroupFollow information response | GroupFollow信息返回体", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "GroupFollowListInfo": {"description": "GroupFollow list data | GroupFollow列表数据", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "GroupFollowListReq": {"description": "Get group follow list request params | GroupFollow列表请求参数", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "GroupFollowListResp": {"description": "The response data of group follow list | GroupFollow列表数据", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "GroupInfo": {"description": "The response data of group information | Group信息", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "GroupInfoResp": {"description": "Group information response | Group信息返回体", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "GroupListInfo": {"description": "Group list data | Group列表数据", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "GroupListReq": {"description": "Get group list request params | Group列表请求参数", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "GroupListResp": {"description": "The response data of group list | Group列表数据", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "GroupMemberInfo": {"description": "The response data of group member information | GroupMember信息", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "GroupMemberInfoResp": {"description": "GroupMember information response | GroupMember信息返回体", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "GroupMemberListInfo": {"description": "GroupMember list data | GroupMember列表数据", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "GroupMemberListReq": {"description": "Get group member list request params | GroupMember列表请求参数", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "GroupMemberListResp": {"description": "The response data of group member list | GroupMember列表数据", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "HealthCheckResp": {"x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "IDInt32PathReq": {"description": "Basic ID request (int32) | 基础ID地址参数请求 (int32)", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "IDInt32Req": {"description": "Basic ID request (int32) | 基础ID参数请求 (int32)", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "IDInt64PathReq": {"description": "Basic ID request (int64) | 基础ID地址参数请求 (int64)", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "IDInt64Req": {"description": "Basic ID request (int64) | 基础ID参数请求 (int64)", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "IDPathReq": {"description": "Basic ID request | 基础ID地址参数请求", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "IDReq": {"description": "Basic ID request | 基础ID参数请求", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "IDUint32PathReq": {"description": "Basic ID request (uint32) | 基础ID地址参数请求 (uint32)", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "IDUint32Req": {"description": "Basic ID request (uint32) | 基础ID参数请求 (uint32)", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "IDsInt32Req": {"description": "Basic IDs request (int32) | 基础ID数组参数请求 (int32)", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "IDsInt64Req": {"description": "Basic IDs request (int64) | 基础ID数组参数请求 (int64)", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "IDsReq": {"description": "Basic IDs request | 基础ID数组参数请求", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "IDsUint32Req": {"description": "Basic IDs request (uint32) | 基础ID数组参数请求 (uint32)", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "MaintenInfoInfo": {"description": "The response data of mainten info information | MaintenInfo信息", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "MaintenInfoInfoResp": {"description": "MaintenInfo information response | MaintenInfo信息返回体", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "MaintenInfoListInfo": {"description": "MaintenInfo list data | MaintenInfo列表数据", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "MaintenInfoListReq": {"description": "Get mainten info list request params | MaintenInfo列表请求参数", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "MaintenInfoListResp": {"description": "The response data of mainten info list | MaintenInfo列表数据", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "MaintenOrderInfo": {"description": "The response data of mainten order information | MaintenOrder信息", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "MaintenOrderInfoResp": {"description": "MaintenOrder information response | MaintenOrder信息返回体", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "MaintenOrderListInfo": {"description": "MaintenOrder list data | MaintenOrder列表数据", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "MaintenOrderListReq": {"description": "Get mainten order list request params | MaintenOrder列表请求参数", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "MaintenOrderListResp": {"description": "The response data of mainten order list | MaintenOrder列表数据", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "MallApplicationInfo": {"description": "The response data of mall application information | MallApplication信息", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "MallApplicationInfoResp": {"description": "The mall application information response | MallApplication信息返回体", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "MallApplicationListInfo": {"description": "The mall application list data | MallApplication信息列表数据", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "MallApplicationListReq": {"description": "Get mall application list request params | MallApplication列表请求参数", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "MallApplicationListResp": {"description": "The response data of mall application list | MallApplication信息列表数据", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "MallDeliveryInfo": {"description": "The response data of mall delivery information | MallDelivery信息", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "MallDeliveryInfoResp": {"description": "The mall delivery information response | MallDelivery信息返回体", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "MallDeliveryListInfo": {"description": "The mall delivery list data | MallDelivery信息列表数据", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "MallDeliveryListReq": {"description": "Get mall delivery list request params | MallDelivery列表请求参数", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "MallDeliveryListResp": {"description": "The response data of mall delivery list | MallDelivery信息列表数据", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "MallLicenseDeviceInfo": {"description": "The response data of mall license device information | 许可证设备信息", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "MallLicenseDeviceInfoResp": {"description": "The mall license device information response | 许可证设备信息返回体", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "MallLicenseDeviceListInfo": {"description": "The mall license device list data | 许可证设备信息列表数据", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "MallLicenseDeviceListReq": {"description": "Get mall license device list request params | 许可证设备列表请求参数", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "MallLicenseDeviceListResp": {"description": "The response data of mall license device list | 许可证设备信息列表数据", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "MallLicenseInfo": {"description": "The response data of mall license information | 许可证信息", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "MallLicenseInfoResp": {"description": "The mall license information response | 许可证信息返回体", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "MallLicenseListInfo": {"description": "The mall license list data | 许可证信息列表数据", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "MallLicenseListReq": {"description": "Get mall license list request params | 许可证列表请求参数", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "MallLicenseListResp": {"description": "The response data of mall license list | 许可证信息列表数据", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "MallOrderInfo": {"description": "The response data of mall order information | MallOrder信息", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "MallOrderInfoResp": {"description": "The mall order information response | MallOrder信息返回体", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "MallOrderItemInfo": {"description": "The response data of mall order item information | MallOrderItem信息", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "MallOrderItemInfoResp": {"description": "The mall order item information response | MallOrderItem信息返回体", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "MallOrderItemListInfo": {"description": "The mall order item list data | MallOrderItem信息列表数据", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "MallOrderItemListReq": {"description": "Get mall order item list request params | MallOrderItem列表请求参数", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "MallOrderItemListResp": {"description": "The response data of mall order item list | MallOrderItem信息列表数据", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "MallOrderListInfo": {"description": "The mall order list data | MallOrder信息列表数据", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "MallOrderListReq": {"description": "Get mall order list request params | MallOrder列表请求参数", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "MallOrderListResp": {"description": "The response data of mall order list | MallOrder信息列表数据", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "MallOrderLogInfo": {"description": "The response data of mall order log information | MallOrderLog信息", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "MallOrderLogInfoResp": {"description": "The mall order log information response | MallOrderLog信息返回体", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "MallOrderLogListInfo": {"description": "The mall order log list data | MallOrderLog信息列表数据", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "MallOrderLogListReq": {"description": "Get mall order log list request params | MallOrderLog列表请求参数", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "MallOrderLogListResp": {"description": "The response data of mall order log list | MallOrderLog信息列表数据", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "MallPaymentInfo": {"description": "The response data of mall payment information | MallPayment信息", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "MallPaymentInfoResp": {"description": "The mall payment information response | MallPayment信息返回体", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "MallPaymentListInfo": {"description": "The mall payment list data | MallPayment信息列表数据", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "MallPaymentListReq": {"description": "Get mall payment list request params | MallPayment列表请求参数", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "MallPaymentListResp": {"description": "The response data of mall payment list | MallPayment信息列表数据", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "MallPaymentLogInfo": {"description": "The response data of mall payment log information | MallPaymentLog信息", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "MallPaymentLogInfoResp": {"description": "The mall payment log information response | MallPaymentLog信息返回体", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "MallPaymentLogListInfo": {"description": "The mall payment log list data | MallPaymentLog信息列表数据", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "MallPaymentLogListReq": {"description": "Get mall payment log list request params | MallPaymentLog列表请求参数", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "MallPaymentLogListResp": {"description": "The response data of mall payment log list | MallPaymentLog信息列表数据", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "MallProductCategoryInfo": {"description": "The response data of mall product category information | MallProductCategory信息", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "MallProductCategoryInfoResp": {"description": "The mall product category information response | MallProductCategory信息返回体", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "MallProductCategoryListInfo": {"description": "The mall product category list data | MallProductCategory信息列表数据", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "MallProductCategoryListReq": {"description": "Get mall product category list request params | MallProductCategory列表请求参数", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "MallProductCategoryListResp": {"description": "The response data of mall product category list | MallProductCategory信息列表数据", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "MallProductInfo": {"description": "The response data of mall product information | MallProduct信息", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "MallProductInfoResp": {"description": "The mall product information response | MallProduct信息返回体", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "MallProductListInfo": {"description": "The mall product list data | MallProduct信息列表数据", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "MallProductListReq": {"description": "Get mall product list request params | MallProduct列表请求参数", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "MallProductListResp": {"description": "The response data of mall product list | MallProduct信息列表数据", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "MallProductQuotaInfo": {"description": "The response data of mall product quota information | MallProductQuota信息", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "MallProductQuotaInfoResp": {"description": "The mall product quota information response | MallProductQuota信息返回体", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "MallProductQuotaListInfo": {"description": "The mall product quota list data | MallProductQuota信息列表数据", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "MallProductQuotaListReq": {"description": "Get mall product quota list request params | MallProductQuota列表请求参数", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "MallProductQuotaListResp": {"description": "The response data of mall product quota list | MallProductQuota信息列表数据", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "MallProductSkuBatchCreateReq": {"description": "The mall product sku batch create request | MallProductSku批量创建请求参数", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "MallProductSkuBatchProcessReq": {"description": "The batch process mall product sku request | 批量处理SKU请求", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "MallProductSkuInfo": {"description": "The response data of mall product sku information | MallProductSku信息", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "MallProductSkuInfoResp": {"description": "The mall product sku information response | MallProductSku信息返回体", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "MallProductSkuListInfo": {"description": "The mall product sku list data | MallProductSku信息列表数据", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "MallProductSkuListReq": {"description": "Get mall product sku list request params | MallProductSku列表请求参数", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "MallProductSkuListResp": {"description": "The response data of mall product sku list | MallProductSku信息列表数据", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "MallRefundExpressInfo": {"description": "The response data of mall refund express information | MallRefundExpress信息", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "MallRefundExpressInfoResp": {"description": "The mall refund express information response | MallRefundExpress信息返回体", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "MallRefundExpressListInfo": {"description": "The mall refund express list data | MallRefundExpress信息列表数据", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "MallRefundExpressListReq": {"description": "Get mall refund express list request params | MallRefundExpress列表请求参数", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "MallRefundExpressListResp": {"description": "The response data of mall refund express list | MallRefundExpress信息列表数据", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "MallRefundInfo": {"description": "The response data of mall refund information | MallRefund信息", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "MallRefundInfoResp": {"description": "The mall refund information response | MallRefund信息返回体", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "MallRefundListInfo": {"description": "The mall refund list data | MallRefund信息列表数据", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "MallRefundListReq": {"description": "Get mall refund list request params | MallRefund列表请求参数", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "MallRefundListResp": {"description": "The response data of mall refund list | MallRefund信息列表数据", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "MallSubscriptionInfo": {"description": "The response data of mall subscription information | MallSubscription信息", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "MallSubscriptionInfoResp": {"description": "The mall subscription information response | MallSubscription信息返回体", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "MallSubscriptionListInfo": {"description": "The mall subscription list data | MallSubscription信息列表数据", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "MallSubscriptionListReq": {"description": "Get mall subscription list request params | MallSubscription列表请求参数", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "MallSubscriptionListResp": {"description": "The response data of mall subscription list | MallSubscription信息列表数据", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "MallSupplierCommissionInfo": {"description": "The response data of mall supplier commission information | 供应商分成信息", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "MallSupplierCommissionInfoResp": {"description": "The mall supplier commission information response | 供应商分成信息返回体", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "MallSupplierCommissionListInfo": {"description": "The mall supplier commission list data | 供应商分成信息列表数据", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "MallSupplierCommissionListReq": {"description": "Get mall supplier commission list request params | 供应商分成列表请求参数", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "MallSupplierCommissionListResp": {"description": "The response data of mall supplier commission list | 供应商分成信息列表数据", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "MallSupplierInfo": {"description": "The response data of mall supplier information | 供应商信息", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "MallSupplierInfoResp": {"description": "The mall supplier information response | 供应商信息返回体", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "MallSupplierListInfo": {"description": "The mall supplier list data | 供应商信息列表数据", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "MallSupplierListReq": {"description": "Get mall supplier list request params | 供应商列表请求参数", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "MallSupplierListResp": {"description": "The response data of mall supplier list | 供应商信息列表数据", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "MallUsageRecordInfo": {"description": "The response data of mall usage record information | MallUsageRecord信息", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "MallUsageRecordInfoResp": {"description": "The mall usage record information response | MallUsageRecord信息返回体", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "MallUsageRecordListInfo": {"description": "The mall usage record list data | MallUsageRecord信息列表数据", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "MallUsageRecordListReq": {"description": "Get mall usage record list request params | MallUsageRecord列表请求参数", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "MallUsageRecordListResp": {"description": "The response data of mall usage record list | MallUsageRecord信息列表数据", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "MenuAuthorityInfoReq": {"description": "Create or update menu authorization information request params | 创建或更新菜单授权信息参数", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "MenuAuthorityInfoResp": {"description": "Menu authorization response data | 菜单授权信息数据", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "PageInfo": {"description": "The page request parameters | 列表请求参数", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "PathAnalysisReq": {"description": "路径分析请求", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "PathAnalysisResp": {"description": "路径分析响应", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "PathHeatmapResp": {"description": "路径热力图响应", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "PathStatsResp": {"description": "路径统计响应", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "ProductInfo": {"description": "The response data of product information | Product信息", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "ProductInfoResp": {"description": "Product information response | Product信息返回体", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "ProductListInfo": {"description": "Product list data | Product列表数据", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "ProductListReq": {"description": "Get product list request params | Product列表请求参数", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "ProductListResp": {"description": "The response data of product list | Product列表数据", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "ProvinceInfo": {"description": "The response data of province information | Province信息", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "ProvinceInfoResp": {"description": "Province information response | Province信息返回体", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "ProvinceListInfo": {"description": "Province list data | Province列表数据", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "ProvinceListReq": {"description": "Get province list request params | Province列表请求参数", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "ProvinceListResp": {"description": "The response data of province list | Province列表数据", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "RealtimeDataResp": {"description": "实时数据响应", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "RentalDeviceInfo": {"description": "The response data of rental device information | RentalDevice信息", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "RentalDeviceInfoResp": {"description": "The rental device information response | RentalDevice信息返回体", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "RentalDeviceListInfo": {"description": "The rental device list data | RentalDevice信息列表数据", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "RentalDeviceListReq": {"description": "Get rental device list request params | RentalDevice列表请求参数", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "RentalDeviceListResp": {"description": "The response data of rental device list | RentalDevice信息列表数据", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "RentalDeviceMessageInfo": {"description": "The response data of rental device message information | RentalDeviceMessage信息", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "RentalDeviceMessageInfoResp": {"description": "The rental device message information response | RentalDeviceMessage信息返回体", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "RentalDeviceMessageListInfo": {"description": "The rental device message list data | RentalDeviceMessage信息列表数据", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "RentalDeviceMessageListReq": {"description": "Get rental device message list request params | RentalDeviceMessage列表请求参数", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "RentalDeviceMessageListResp": {"description": "The response data of rental device message list | RentalDeviceMessage信息列表数据", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "ReplyInfo": {"description": "The response data of reply information | Reply信息", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "ReplyInfoResp": {"description": "Reply information response | Reply信息返回体", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "ReplyListInfo": {"description": "Reply list data | Reply列表数据", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "ReplyListReq": {"description": "Get reply list request params | Reply列表请求参数", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "ReplyListResp": {"description": "The response data of reply list | Reply列表数据", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "ResetApiSecretReq": {"description": "重置API密钥请求", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "RunFunnelAnalysisReq": {"description": "运行漏斗分析请求", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "SegmentArticleRecordInfo": {"description": "The response data of segment article record information | SegmentArticleRecord信息", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "SegmentArticleRecordInfoResp": {"description": "SegmentArticleRecord information response | SegmentArticleRecord信息返回体", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "SegmentArticleRecordListInfo": {"description": "SegmentArticleRecord list data | SegmentArticleRecord列表数据", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "SegmentArticleRecordListReq": {"description": "Get segment article record list request params | SegmentArticleRecord列表请求参数", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "SegmentArticleRecordListResp": {"description": "The response data of segment article record list | SegmentArticleRecord列表数据", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "SegmentInfo": {"description": "The response data of segment information | Segment信息", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "SegmentInfoResp": {"description": "Segment information response | Segment信息返回体", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "SegmentListInfo": {"description": "Segment list data | Segment列表数据", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "SegmentListReq": {"description": "Get segment list request params | Segment列表请求参数", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "SegmentListResp": {"description": "The response data of segment list | Segment列表数据", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "StreetInfo": {"description": "The response data of street information | Street信息", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "StreetInfoResp": {"description": "Street information response | Street信息返回体", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "StreetListInfo": {"description": "Street list data | Street列表数据", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "StreetListReq": {"description": "Get street list request params | Street列表请求参数", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "StreetListResp": {"description": "The response data of street list | Street列表数据", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "TagInfo": {"description": "The response data of tag information | Tag信息", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "TagInfoResp": {"description": "Tag information response | Tag信息返回体", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "TagListInfo": {"description": "Tag list data | Tag列表数据", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "TagListReq": {"description": "Get tag list request params | Tag列表请求参数", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "TagListResp": {"description": "The response data of tag list | Tag列表数据", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "TagRecordInfo": {"description": "The response data of tag record information | TagRecord信息", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "TagRecordInfoResp": {"description": "TagRecord information response | TagRecord信息返回体", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "TagRecordListInfo": {"description": "TagRecord list data | TagRecord列表数据", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "TagRecordListReq": {"description": "Get tag record list request params | TagRecord列表请求参数", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "TagRecordListResp": {"description": "The response data of tag record list | TagRecord列表数据", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "UUIDPathReq": {"description": "Basic UUID request in path | 基础UUID地址参数请求", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "UUIDReq": {"description": "Basic UUID request | 基础UUID参数请求", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "UUIDsReq": {"description": "Basic UUID array request | 基础UUID数组参数请求", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "UnbindMallLicenseDeviceReq": {"description": "Unbind mall license device request params | 解绑许可证设备请求参数", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "UniversalStatsReq": {"description": "通用统计查询请求", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "UniversalStatsResp": {"description": "通用统计响应", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "UpdateUserForbiddenTimeReq": {"description": "Update user forbidden time request | 更新用户封禁时间请求", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "UpdateUserMutedTimeReq": {"description": "Update user muted time request | 更新用户禁言时间请求", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "UserAddresInfo": {"description": "The response data of user addres information | UserAddres信息", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "UserAddresInfoResp": {"description": "UserAddres information response | UserAddres信息返回体", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "UserAddresListInfo": {"description": "UserAddres list data | UserAddres列表数据", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "UserAddresListReq": {"description": "Get user addres list request params | UserAddres列表请求参数", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "UserAddresListResp": {"description": "The response data of user addres list | UserAddres列表数据", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "UserBehaviorHeatmapResp": {"description": "用户行为热力图响应", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "UserInfo": {"description": "The response data of user information | User信息", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "UserInfoResp": {"description": "User information response | User信息返回体", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "UserListInfo": {"description": "User list data | User列表数据", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "UserListReq": {"description": "Get user list request params | User列表请求参数", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "UserListResp": {"description": "The response data of user list | User列表数据", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}, "UserTrendsResp": {"description": "用户趋势响应", "x-go-package": "seevision.cn/server/meet-admin-api/internal/types"}}, "securityDefinitions": {"Token": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header"}}, "security": [{"Token": ["[]"]}]}