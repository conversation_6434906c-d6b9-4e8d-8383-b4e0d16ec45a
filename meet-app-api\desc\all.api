import "base.api"
import "./configuration/configuration.api"
import "./applet/applet.api"
import "./meetgrouprpc/group.api"
import "./meettagrpc/tag.api"
import "./meetgrouprpc/group_member.api"
import "./meetgrouprpc/group_follow.api"
import "./meetadrpc/banner.api"
import "./meetgeorpc/city.api"
import "./meetgeorpc/street.api"
import "./meetgeorpc/area.api"
import "./meetgeorpc/province.api"
import "./meetsaleservicerpc/mainten_info.api"
import "./meetarticlerpc/segment.api"
import "./meetarticlerpc/article.api"
import "./meetarticlerpc/segment_article_record.api"
import "./meetusercenterrpc/user_addres.api"
import "./meetsaleservicerpc/product.api"
import "./meetappversionrpc/app_version.api"
import "./meetusercenterrpc/user.api"
import "./meettagrpc/tag_follow.api"
import "./meetsaleservicerpc/mall_product.api"
import "./meetsaleservicerpc/mall_order.api"
import "./meetsaleservicerpc/mall_payment_notify.api"
import "./meetsaleservicerpc/mall_product_sku.api"
import "./meetsaleservicerpc/mall_order_item.api"
import "./meetsaleservicerpc/mall_license.api"
import "./meetgeorpc/bolo_lexicon.api"
import "./meetusercenterrpc/user_agreement.api"
import "./meetsaleservicerpc/rental_device.api"