{"consumes": ["application/json"], "produces": ["application/json"], "schemes": ["http", "https"], "swagger": "2.0", "info": {"description": "This is simple admin api doc", "title": "Simple Admin", "contact": {"email": "<EMAIL>"}, "version": "1.6.6"}, "host": "localhost:9100", "basePath": "/", "paths": {"/api": {"post": {"description": "Get API by ID | 通过ID获取API", "tags": ["api"], "summary": "Get API by ID | 通过ID获取API", "operationId": "GetApiById", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDReq"}}], "responses": {"200": {"description": "ApiInfoResp", "schema": {"$ref": "#/definitions/ApiInfoResp"}}}}}, "/api/create": {"post": {"description": "Create API information | 创建API", "tags": ["api"], "summary": "Create API information | 创建API", "operationId": "CreateApi", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/ApiInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/api/delete": {"post": {"description": "Delete API information | 删除API信息", "tags": ["api"], "summary": "Delete API information | 删除API信息", "operationId": "DeleteApi", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDsReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/api/list": {"post": {"description": "Get API list | 获取API列表", "tags": ["api"], "summary": "Get API list | 获取API列表", "operationId": "GetApiList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/ApiListReq"}}], "responses": {"200": {"description": "ApiListResp", "schema": {"$ref": "#/definitions/ApiListResp"}}}}}, "/api/update": {"post": {"description": "Update API information | 创建API", "tags": ["api"], "summary": "Update API information | 创建API", "operationId": "UpdateA<PERSON>", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/ApiInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/authority/api/create_or_update": {"post": {"description": "Create or update API authorization information | 创建或更新API权限", "tags": ["authority"], "summary": "Create or update API authorization information | 创建或更新API权限", "operationId": "CreateOrUpdateApiAuthority", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/CreateOrUpdateApiAuthorityReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/authority/api/role": {"post": {"description": "Get role's API authorization list | 获取角色api权限列表", "tags": ["authority"], "summary": "Get role's API authorization list | 获取角色api权限列表", "operationId": "GetApiAuthority", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDReq"}}], "responses": {"200": {"description": "ApiAuthorityListResp", "schema": {"$ref": "#/definitions/ApiAuthorityListResp"}}}}}, "/authority/menu/create_or_update": {"post": {"description": "Create or update menu authorization information | 创建或更新菜单权限", "tags": ["authority"], "summary": "Create or update menu authorization information | 创建或更新菜单权限", "operationId": "CreateOrUpdateMenuAuthority", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/MenuAuthorityInfoReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/authority/menu/role": {"post": {"description": "Get role's menu authorization list | 获取角色菜单权限列表", "tags": ["authority"], "summary": "Get role's menu authorization list | 获取角色菜单权限列表", "operationId": "GetMenuAuthority", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDReq"}}], "responses": {"200": {"description": "MenuAuthorityInfoResp", "schema": {"$ref": "#/definitions/MenuAuthorityInfoResp"}}}}}, "/captcha": {"get": {"description": "Get captcha | 获取验证码", "tags": ["<PERSON><PERSON>a"], "summary": "Get captcha | 获取验证码", "operationId": "GetCaptcha", "responses": {"200": {"description": "CaptchaResp", "schema": {"$ref": "#/definitions/CaptchaResp"}}}}}, "/captcha/email": {"post": {"description": "Get Email Captcha | 获取邮箱验证码", "tags": ["<PERSON><PERSON>a"], "summary": "Get Email Captcha | 获取邮箱验证码", "operationId": "GetEmailCaptcha", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/EmailCaptchaReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/captcha/sms": {"post": {"description": "Get SMS Captcha | 获取短信验证码", "tags": ["<PERSON><PERSON>a"], "summary": "Get SMS Captcha | 获取短信验证码", "operationId": "GetSmsCaptcha", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/SmsCaptchaReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/configuration": {"post": {"description": "Get configuration by ID | 通过ID获取Configuration", "tags": ["configuration"], "summary": "Get configuration by ID | 通过ID获取Configuration", "operationId": "GetConfigurationById", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDReq"}}], "responses": {"200": {"description": "ConfigurationInfoResp", "schema": {"$ref": "#/definitions/ConfigurationInfoResp"}}}}}, "/configuration/create": {"post": {"description": "Create configuration information | 创建Configuration", "tags": ["configuration"], "summary": "Create configuration information | 创建Configuration", "operationId": "CreateConfiguration", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/ConfigurationInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/configuration/delete": {"post": {"description": "Delete configuration information | 删除Configuration信息", "tags": ["configuration"], "summary": "Delete configuration information | 删除Configuration信息", "operationId": "DeleteConfiguration", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDsReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/configuration/list": {"post": {"description": "Get configuration list | 获取Configuration列表", "tags": ["configuration"], "summary": "Get configuration list | 获取Configuration列表", "operationId": "GetConfigurationList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/ConfigurationListReq"}}], "responses": {"200": {"description": "ConfigurationListResp", "schema": {"$ref": "#/definitions/ConfigurationListResp"}}}}}, "/configuration/system/list": {"get": {"description": "Get public system configuration list | 获取公开系统参数列表", "tags": ["publicapi"], "summary": "Get public system configuration list | 获取公开系统参数列表", "operationId": "GetPublicSystemConfigurationList", "responses": {"200": {"description": "ConfigurationListResp", "schema": {"$ref": "#/definitions/ConfigurationListResp"}}}}}, "/configuration/update": {"post": {"description": "Update configuration information | 更新Configuration", "tags": ["configuration"], "summary": "Update configuration information | 更新Configuration", "operationId": "UpdateConfiguration", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/ConfigurationInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/core/init/database": {"get": {"description": "Initialize database | 初始化数据库", "tags": ["base"], "summary": "Initialize database | 初始化数据库", "operationId": "InitDatabase", "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/core/init/job_database": {"get": {"description": "Initialize job database | 初始化定时任务数据库", "tags": ["base"], "summary": "Initialize job database | 初始化定时任务数据库", "operationId": "InitJobDatabase", "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/core/init/mcms_database": {"get": {"description": "Initialize Message Center database | 初始化消息中心数据库", "tags": ["base"], "summary": "Initialize Message Center database | 初始化消息中心数据库", "operationId": "InitMcmsDatabase", "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/department": {"post": {"description": "Get Department by ID | 通过ID获取部门", "tags": ["department"], "summary": "Get Department by ID | 通过ID获取部门", "operationId": "GetDepartmentById", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDReq"}}], "responses": {"200": {"description": "DepartmentInfoResp", "schema": {"$ref": "#/definitions/DepartmentInfoResp"}}}}}, "/department/create": {"post": {"description": "Create department information | 创建部门", "tags": ["department"], "summary": "Create department information | 创建部门", "operationId": "CreateDepartment", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/DepartmentInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/department/delete": {"post": {"description": "Delete department information | 删除部门信息", "tags": ["department"], "summary": "Delete department information | 删除部门信息", "operationId": "DeleteDepartment", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDsReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/department/list": {"post": {"description": "Get department list | 获取部门列表", "tags": ["department"], "summary": "Get department list | 获取部门列表", "operationId": "GetDepartmentList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/DepartmentListReq"}}], "responses": {"200": {"description": "DepartmentListResp", "schema": {"$ref": "#/definitions/DepartmentListResp"}}}}}, "/department/update": {"post": {"description": "Update department information | 更新部门", "tags": ["department"], "summary": "Update department information | 更新部门", "operationId": "UpdateDepartment", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/DepartmentInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/dict/{name}": {"get": {"description": "Get dictionary detail by dictionary name | 通过字典名称获取字典内容", "tags": ["dictionarydetail"], "summary": "Get dictionary detail by dictionary name | 通过字典名称获取字典内容", "operationId": "GetDictionaryDetailByDictionaryName", "parameters": [{"type": "string", "x-go-name": "Name", "name": "name", "in": "path", "required": true}], "responses": {"200": {"description": "DictionaryDetailListResp", "schema": {"$ref": "#/definitions/DictionaryDetailListResp"}}}}}, "/dictionary": {"post": {"description": "Get Dictionary by ID | 通过ID获取字典", "tags": ["dictionary"], "summary": "Get Dictionary by ID | 通过ID获取字典", "operationId": "GetDictionaryById", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDReq"}}], "responses": {"200": {"description": "DictionaryInfoResp", "schema": {"$ref": "#/definitions/DictionaryInfoResp"}}}}}, "/dictionary/create": {"post": {"description": "Create dictionary information | 创建字典", "tags": ["dictionary"], "summary": "Create dictionary information | 创建字典", "operationId": "CreateDictionary", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/DictionaryInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/dictionary/delete": {"post": {"description": "Delete dictionary information | 删除字典信息", "tags": ["dictionary"], "summary": "Delete dictionary information | 删除字典信息", "operationId": "DeleteDictionary", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDsReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/dictionary/list": {"post": {"description": "Get dictionary list | 获取字典列表", "tags": ["dictionary"], "summary": "Get dictionary list | 获取字典列表", "operationId": "GetDictionaryList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/DictionaryListReq"}}], "responses": {"200": {"description": "DictionaryListResp", "schema": {"$ref": "#/definitions/DictionaryListResp"}}}}}, "/dictionary/update": {"post": {"description": "Update dictionary information | 更新字典", "tags": ["dictionary"], "summary": "Update dictionary information | 更新字典", "operationId": "UpdateDictionary", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/DictionaryInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/dictionary_detail": {"post": {"description": "Get DictionaryDetail by ID | 通过ID获取字典键值", "tags": ["dictionarydetail"], "summary": "Get DictionaryDetail by ID | 通过ID获取字典键值", "operationId": "GetDictionaryDetailById", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDReq"}}], "responses": {"200": {"description": "DictionaryDetailInfoResp", "schema": {"$ref": "#/definitions/DictionaryDetailInfoResp"}}}}}, "/dictionary_detail/create": {"post": {"description": "Create dictionary detail information | 创建字典键值", "tags": ["dictionarydetail"], "summary": "Create dictionary detail information | 创建字典键值", "operationId": "CreateDictionaryDetail", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/DictionaryDetailInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/dictionary_detail/delete": {"post": {"description": "Delete dictionary detail information | 删除字典键值信息", "tags": ["dictionarydetail"], "summary": "Delete dictionary detail information | 删除字典键值信息", "operationId": "DeleteDictionaryDetail", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDsReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/dictionary_detail/list": {"post": {"description": "Get dictionary detail list | 获取字典键值列表", "tags": ["dictionarydetail"], "summary": "Get dictionary detail list | 获取字典键值列表", "operationId": "GetDictionaryDetailList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/DictionaryDetailListReq"}}], "responses": {"200": {"description": "DictionaryDetailListResp", "schema": {"$ref": "#/definitions/DictionaryDetailListResp"}}}}}, "/dictionary_detail/update": {"post": {"description": "Update dictionary detail information | 更新字典键值", "tags": ["dictionarydetail"], "summary": "Update dictionary detail information | 更新字典键值", "operationId": "UpdateDictionaryDetail", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/DictionaryDetailInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/email/send": {"post": {"description": "Send email message | 发送电子邮件", "tags": ["messagesender"], "summary": "Send email message | 发送电子邮件", "operationId": "SendEmail", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/SendEmailReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/email_log": {"post": {"description": "Get email log by ID | 通过ID获取电子邮件日志", "tags": ["emaillog"], "summary": "Get email log by ID | 通过ID获取电子邮件日志", "operationId": "GetEmailLogById", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/UUIDReq"}}], "responses": {"200": {"description": "EmailLogInfoResp", "schema": {"$ref": "#/definitions/EmailLogInfoResp"}}}}}, "/email_log/create": {"post": {"description": "Create email log information | 创建电子邮件日志", "tags": ["emaillog"], "summary": "Create email log information | 创建电子邮件日志", "operationId": "CreateEmailLog", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/EmailLogInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/email_log/delete": {"post": {"description": "Delete email log information | 删除电子邮件日志信息", "tags": ["emaillog"], "summary": "Delete email log information | 删除电子邮件日志信息", "operationId": "DeleteEmailLog", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/UUIDsReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/email_log/list": {"post": {"description": "Get email log list | 获取电子邮件日志列表", "tags": ["emaillog"], "summary": "Get email log list | 获取电子邮件日志列表", "operationId": "GetEmailLogList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/EmailLogListReq"}}], "responses": {"200": {"description": "EmailLogListResp", "schema": {"$ref": "#/definitions/EmailLogListResp"}}}}}, "/email_log/update": {"post": {"description": "Update email log information | 更新电子邮件日志", "tags": ["emaillog"], "summary": "Update email log information | 更新电子邮件日志", "operationId": "UpdateEmailLog", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/EmailLogInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/email_provider": {"post": {"description": "Get email provider by ID | 通过ID获取邮箱服务配置", "tags": ["emailprovider"], "summary": "Get email provider by ID | 通过ID获取邮箱服务配置", "operationId": "GetEmailProviderById", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDReq"}}], "responses": {"200": {"description": "EmailProviderInfoResp", "schema": {"$ref": "#/definitions/EmailProviderInfoResp"}}}}}, "/email_provider/create": {"post": {"description": "Create email provider information | 创建邮箱服务配置", "tags": ["emailprovider"], "summary": "Create email provider information | 创建邮箱服务配置", "operationId": "<PERSON><PERSON><PERSON><PERSON>", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/EmailProviderInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/email_provider/delete": {"post": {"description": "Delete email provider information | 删除邮箱服务配置信息", "tags": ["emailprovider"], "summary": "Delete email provider information | 删除邮箱服务配置信息", "operationId": "DeleteEmailProvider", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDsReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/email_provider/list": {"post": {"description": "Get email provider list | 获取邮箱服务配置列表", "tags": ["emailprovider"], "summary": "Get email provider list | 获取邮箱服务配置列表", "operationId": "GetEmailProviderList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/EmailProviderListReq"}}], "responses": {"200": {"description": "EmailProviderListResp", "schema": {"$ref": "#/definitions/EmailProviderListResp"}}}}}, "/email_provider/update": {"post": {"description": "Update email provider information | 更新邮箱服务配置", "tags": ["emailprovider"], "summary": "Update email provider information | 更新邮箱服务配置", "operationId": "UpdateEmail<PERSON>rovider", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/EmailProviderInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/health": {"get": {"description": "健康检查接口 | Health Check for Load Balancer", "summary": "健康检查接口 | Health Check for Load Balancer", "operationId": "healthCheck", "responses": {"200": {"description": "HealthCheckResp", "schema": {"$ref": "#/definitions/HealthCheckResp"}}}}}, "/menu/create": {"post": {"description": "Create menu information | 创建菜单", "tags": ["menu"], "summary": "Create menu information | 创建菜单", "operationId": "CreateMenu", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/MenuPlainInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/menu/delete": {"post": {"description": "Delete menu information | 删除菜单信息", "tags": ["menu"], "summary": "Delete menu information | 删除菜单信息", "operationId": "DeleteMenu", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/menu/list": {"get": {"description": "Get menu list | 获取菜单列表", "tags": ["menu"], "summary": "Get menu list | 获取菜单列表", "operationId": "GetMenuList", "responses": {"200": {"description": "MenuPlainInfoListResp", "schema": {"$ref": "#/definitions/MenuPlainInfoListResp"}}}}}, "/menu/role/list": {"get": {"description": "Get menu list by role | 获取菜单列表", "tags": ["menu"], "summary": "Get menu list by role | 获取菜单列表", "operationId": "GetMenuListByRole", "responses": {"200": {"description": "MenuListResp", "schema": {"$ref": "#/definitions/MenuListResp"}}}}}, "/menu/update": {"post": {"description": "Update menu information | 更新菜单", "tags": ["menu"], "summary": "Update menu information | 更新菜单", "operationId": "UpdateMenu", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/MenuPlainInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/oauth/login": {"post": {"description": "<PERSON><PERSON><PERSON> log in | Oauth 登录", "tags": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON><PERSON> log in | Oauth 登录", "operationId": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/OauthLoginReq"}}], "responses": {"200": {"description": "RedirectResp", "schema": {"$ref": "#/definitions/RedirectResp"}}}}}, "/oauth/login/callback": {"get": {"description": "Oauth log in callback route | Oauth 登录返回调接口", "tags": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Oauth log in callback route | Oauth 登录返回调接口", "operationId": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "responses": {"200": {"description": "CallbackResp", "schema": {"$ref": "#/definitions/CallbackResp"}}}}}, "/oauth_provider": {"post": {"description": "Get oauth provider by ID | 通过ID获取第三方", "tags": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Get oauth provider by ID | 通过ID获取第三方", "operationId": "GetOauthProviderById", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDReq"}}], "responses": {"200": {"description": "OauthProviderInfoResp", "schema": {"$ref": "#/definitions/OauthProviderInfoResp"}}}}}, "/oauth_provider/create": {"post": {"description": "Create oauth provider information | 创建第三方", "tags": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Create oauth provider information | 创建第三方", "operationId": "Create<PERSON><PERSON><PERSON>", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/OauthProviderInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/oauth_provider/delete": {"post": {"description": "Delete oauth provider information | 删除第三方信息", "tags": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Delete oauth provider information | 删除第三方信息", "operationId": "DeleteOauth<PERSON>rovider", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDsReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/oauth_provider/list": {"post": {"description": "Get oauth provider list | 获取第三方列表", "tags": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Get oauth provider list | 获取第三方列表", "operationId": "GetOauthProviderList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/OauthProviderListReq"}}], "responses": {"200": {"description": "OauthProviderListResp", "schema": {"$ref": "#/definitions/OauthProviderListResp"}}}}}, "/oauth_provider/update": {"post": {"description": "Update oauth provider information | 更新第三方", "tags": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Update oauth provider information | 更新第三方", "operationId": "Update<PERSON><PERSON><PERSON><PERSON>", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/OauthProviderInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/position": {"post": {"description": "Get position by ID | 通过ID获取职位", "tags": ["position"], "summary": "Get position by ID | 通过ID获取职位", "operationId": "GetPositionById", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDReq"}}], "responses": {"200": {"description": "PositionInfoResp", "schema": {"$ref": "#/definitions/PositionInfoResp"}}}}}, "/position/create": {"post": {"description": "Create position information | 创建职位", "tags": ["position"], "summary": "Create position information | 创建职位", "operationId": "CreatePosition", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/PositionInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/position/delete": {"post": {"description": "Delete position information | 删除职位信息", "tags": ["position"], "summary": "Delete position information | 删除职位信息", "operationId": "DeletePosition", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDsReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/position/list": {"post": {"description": "Get position list | 获取职位列表", "tags": ["position"], "summary": "Get position list | 获取职位列表", "operationId": "GetPositionList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/PositionListReq"}}], "responses": {"200": {"description": "PositionListResp", "schema": {"$ref": "#/definitions/PositionListResp"}}}}}, "/position/update": {"post": {"description": "Update position information | 更新职位", "tags": ["position"], "summary": "Update position information | 更新职位", "operationId": "UpdatePosition", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/PositionInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/role": {"post": {"description": "Get Role by ID | 通过ID获取角色", "tags": ["role"], "summary": "Get Role by ID | 通过ID获取角色", "operationId": "GetRoleById", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDReq"}}], "responses": {"200": {"description": "RoleInfoResp", "schema": {"$ref": "#/definitions/RoleInfoResp"}}}}}, "/role/create": {"post": {"description": "Create role information | 创建角色", "tags": ["role"], "summary": "Create role information | 创建角色", "operationId": "CreateRole", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/RoleInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/role/delete": {"post": {"description": "Delete role information | 删除角色信息", "tags": ["role"], "summary": "Delete role information | 删除角色信息", "operationId": "DeleteRole", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDsReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/role/list": {"post": {"description": "Get role list | 获取角色列表", "tags": ["role"], "summary": "Get role list | 获取角色列表", "operationId": "GetRoleList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/RoleListReq"}}], "responses": {"200": {"description": "RoleListResp", "schema": {"$ref": "#/definitions/RoleListResp"}}}}}, "/role/update": {"post": {"description": "Update role information | 更新角色", "tags": ["role"], "summary": "Update role information | 更新角色", "operationId": "UpdateRole", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/RoleInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/sms/send": {"post": {"description": "Send sms message | 发送短信", "tags": ["messagesender"], "summary": "Send sms message | 发送短信", "operationId": "SendSms", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/SendSmsReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/sms_log": {"post": {"description": "Get sms log by ID | 通过ID获取短信日志", "tags": ["smslog"], "summary": "Get sms log by ID | 通过ID获取短信日志", "operationId": "GetSmsLogById", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/UUIDReq"}}], "responses": {"200": {"description": "SmsLogInfoResp", "schema": {"$ref": "#/definitions/SmsLogInfoResp"}}}}}, "/sms_log/create": {"post": {"description": "Create sms log information | 创建短信日志", "tags": ["smslog"], "summary": "Create sms log information | 创建短信日志", "operationId": "CreateSmsLog", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/SmsLogInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/sms_log/delete": {"post": {"description": "Delete sms log information | 删除短信日志信息", "tags": ["smslog"], "summary": "Delete sms log information | 删除短信日志信息", "operationId": "DeleteSmsLog", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/UUIDsReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/sms_log/list": {"post": {"description": "Get sms log list | 获取短信日志列表", "tags": ["smslog"], "summary": "Get sms log list | 获取短信日志列表", "operationId": "GetSmsLogList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/SmsLogListReq"}}], "responses": {"200": {"description": "SmsLogListResp", "schema": {"$ref": "#/definitions/SmsLogListResp"}}}}}, "/sms_log/update": {"post": {"description": "Update sms log information | 更新短信日志", "tags": ["smslog"], "summary": "Update sms log information | 更新短信日志", "operationId": "UpdateSmsLog", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/SmsLogInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/sms_provider": {"post": {"description": "Get sms provider by ID | 通过ID获取短信配置", "tags": ["smsprovider"], "summary": "Get sms provider by ID | 通过ID获取短信配置", "operationId": "GetSmsProviderById", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDReq"}}], "responses": {"200": {"description": "SmsProviderInfoResp", "schema": {"$ref": "#/definitions/SmsProviderInfoResp"}}}}}, "/sms_provider/create": {"post": {"description": "Create sms provider information | 创建短信配置", "tags": ["smsprovider"], "summary": "Create sms provider information | 创建短信配置", "operationId": "CreateSmsProvider", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/SmsProviderInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/sms_provider/delete": {"post": {"description": "Delete sms provider information | 删除短信配置信息", "tags": ["smsprovider"], "summary": "Delete sms provider information | 删除短信配置信息", "operationId": "DeleteSmsProvider", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDsReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/sms_provider/list": {"post": {"description": "Get sms provider list | 获取短信配置列表", "tags": ["smsprovider"], "summary": "Get sms provider list | 获取短信配置列表", "operationId": "GetSmsProviderList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/SmsProviderListReq"}}], "responses": {"200": {"description": "SmsProviderListResp", "schema": {"$ref": "#/definitions/SmsProviderListResp"}}}}}, "/sms_provider/update": {"post": {"description": "Update sms provider information | 更新短信配置", "tags": ["smsprovider"], "summary": "Update sms provider information | 更新短信配置", "operationId": "UpdateSmsProvider", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/SmsProviderInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/task": {"post": {"description": "Get task by ID | 通过ID获取Task", "tags": ["task"], "summary": "Get task by ID | 通过ID获取Task", "operationId": "GetTaskById", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDReq"}}], "responses": {"200": {"description": "TaskInfoResp", "schema": {"$ref": "#/definitions/TaskInfoResp"}}}}}, "/task/create": {"post": {"description": "Create task information | 创建Task", "tags": ["task"], "summary": "Create task information | 创建Task", "operationId": "CreateTask", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/TaskInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/task/delete": {"post": {"description": "Delete task information | 删除Task信息", "tags": ["task"], "summary": "Delete task information | 删除Task信息", "operationId": "DeleteTask", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDsReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/task/list": {"post": {"description": "Get task list | 获取Task列表", "tags": ["task"], "summary": "Get task list | 获取Task列表", "operationId": "GetTaskList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/TaskListReq"}}], "responses": {"200": {"description": "TaskListResp", "schema": {"$ref": "#/definitions/TaskListResp"}}}}}, "/task/update": {"post": {"description": "Update task information | 更新Task", "tags": ["task"], "summary": "Update task information | 更新Task", "operationId": "UpdateTask", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/TaskInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/task_log": {"post": {"description": "Get task log by ID | 通过ID获取任务日志", "tags": ["tasklog"], "summary": "Get task log by ID | 通过ID获取任务日志", "operationId": "GetTaskLogById", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDReq"}}], "responses": {"200": {"description": "TaskLogInfoResp", "schema": {"$ref": "#/definitions/TaskLogInfoResp"}}}}}, "/task_log/create": {"post": {"description": "Create task log information | 创建任务日志", "tags": ["tasklog"], "summary": "Create task log information | 创建任务日志", "operationId": "CreateTaskLog", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/TaskLogInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/task_log/delete": {"post": {"description": "Delete task log information | 删除任务日志信息", "tags": ["tasklog"], "summary": "Delete task log information | 删除任务日志信息", "operationId": "DeleteTaskLog", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/IDsReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/task_log/list": {"post": {"description": "Get task log list | 获取任务日志列表", "tags": ["tasklog"], "summary": "Get task log list | 获取任务日志列表", "operationId": "GetTaskLogList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/TaskLogListReq"}}], "responses": {"200": {"description": "TaskLogListResp", "schema": {"$ref": "#/definitions/TaskLogListResp"}}}}}, "/task_log/update": {"post": {"description": "Update task log information | 更新任务日志", "tags": ["tasklog"], "summary": "Update task log information | 更新任务日志", "operationId": "UpdateTaskLog", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/TaskLogInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/token": {"post": {"description": "Get Token by ID | 通过ID获取Token", "tags": ["token"], "summary": "Get Token by ID | 通过ID获取Token", "operationId": "GetTokenById", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/UUIDReq"}}], "responses": {"200": {"description": "TokenInfoResp", "schema": {"$ref": "#/definitions/TokenInfoResp"}}}}}, "/token/create": {"post": {"description": "Create token information | 创建Token", "tags": ["token"], "summary": "Create token information | 创建Token", "operationId": "CreateToken", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/TokenInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/token/delete": {"post": {"description": "Delete token information | 删除Token信息", "tags": ["token"], "summary": "Delete token information | 删除Token信息", "operationId": "DeleteToken", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/UUIDsReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/token/list": {"post": {"description": "Get token list | 获取Token列表", "tags": ["token"], "summary": "Get token list | 获取Token列表", "operationId": "GetTokenList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/TokenListReq"}}], "responses": {"200": {"description": "TokenListResp", "schema": {"$ref": "#/definitions/TokenListResp"}}}}}, "/token/logout": {"post": {"description": "Force logging out by user UUID | 根据UUID强制用户退出", "tags": ["token"], "summary": "Force logging out by user UUID | 根据UUID强制用户退出", "operationId": "Logout", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/UUIDReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/token/update": {"post": {"description": "Update token information | 更新 Token", "tags": ["token"], "summary": "Update token information | 更新 Token", "operationId": "UpdateToken", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/TokenInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/user": {"post": {"description": "Get User by ID | 通过ID获取用户", "tags": ["user"], "summary": "Get User by ID | 通过ID获取用户", "operationId": "GetUserById", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/UUIDReq"}}], "responses": {"200": {"description": "UserInfoResp", "schema": {"$ref": "#/definitions/UserInfoResp"}}}}}, "/user/access_token": {"get": {"description": "Access token | 获取短期 token", "tags": ["user"], "summary": "Access token | 获取短期 token", "operationId": "AccessToken", "responses": {"200": {"description": "RefreshTokenResp", "schema": {"$ref": "#/definitions/RefreshTokenResp"}}}}}, "/user/change_password": {"post": {"description": "Change Password | 修改密码", "tags": ["user"], "summary": "Change Password | 修改密码", "operationId": "ChangePassword", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/ChangePasswordReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/user/create": {"post": {"description": "Create user information | 创建用户", "tags": ["user"], "summary": "Create user information | 创建用户", "operationId": "CreateUser", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/UserInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/user/delete": {"post": {"description": "Delete user information | 删除用户信息", "tags": ["user"], "summary": "Delete user information | 删除用户信息", "operationId": "DeleteUser", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/UUIDsReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/user/info": {"get": {"description": "Get user basic information | 获取用户基本信息", "tags": ["user"], "summary": "Get user basic information | 获取用户基本信息", "operationId": "GetUserInfo", "responses": {"200": {"description": "UserBaseIDInfoResp", "schema": {"$ref": "#/definitions/UserBaseIDInfoResp"}}}}}, "/user/list": {"post": {"description": "Get user list | 获取用户列表", "tags": ["user"], "summary": "Get user list | 获取用户列表", "operationId": "GetUserList", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/UserListReq"}}], "responses": {"200": {"description": "UserListResp", "schema": {"$ref": "#/definitions/UserListResp"}}}}}, "/user/login": {"post": {"description": "Log in | 登录", "tags": ["publicuser"], "summary": "Log in | 登录", "operationId": "<PERSON><PERSON>", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/LoginReq"}}], "responses": {"200": {"description": "LoginResp", "schema": {"$ref": "#/definitions/LoginResp"}}}}}, "/user/login_by_email": {"post": {"description": "Log in by email | 邮箱登录", "tags": ["publicuser"], "summary": "Log in by email | 邮箱登录", "operationId": "LoginByEmail", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/LoginByEmailReq"}}], "responses": {"200": {"description": "LoginResp", "schema": {"$ref": "#/definitions/LoginResp"}}}}}, "/user/login_by_sms": {"post": {"description": "Log in by SMS | 短信登录", "tags": ["publicuser"], "summary": "Log in by SMS | 短信登录", "operationId": "LoginBySms", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/LoginBySmsReq"}}], "responses": {"200": {"description": "LoginResp", "schema": {"$ref": "#/definitions/LoginResp"}}}}}, "/user/logout": {"get": {"description": "Log out | 退出登陆", "tags": ["user"], "summary": "Log out | 退出登陆", "operationId": "Logout", "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/user/perm": {"get": {"description": "Get user's permission code | 获取用户权限码", "tags": ["user"], "summary": "Get user's permission code | 获取用户权限码", "operationId": "GetUserPermCode", "responses": {"200": {"description": "PermCodeResp", "schema": {"$ref": "#/definitions/PermCodeResp"}}}}}, "/user/profile": {"get": {"description": "Get user's profile | 获取用户个人信息", "tags": ["user"], "summary": "Get user's profile | 获取用户个人信息", "operationId": "GetUserProfile", "responses": {"200": {"description": "ProfileResp", "schema": {"$ref": "#/definitions/ProfileResp"}}}}, "post": {"description": "Update user's profile | 更新用户个人信息", "tags": ["user"], "summary": "Update user's profile | 更新用户个人信息", "operationId": "UpdateUserProfile", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/ProfileInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/user/refresh_token": {"get": {"description": "Refresh token | 获取刷新 token", "tags": ["user"], "summary": "Refresh token | 获取刷新 token", "operationId": "RefreshToken", "responses": {"200": {"description": "RefreshTokenResp", "schema": {"$ref": "#/definitions/RefreshTokenResp"}}}}}, "/user/register": {"post": {"description": "Register | 注册", "tags": ["publicuser"], "summary": "Register | 注册", "operationId": "Register", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/RegisterReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/user/register_by_email": {"post": {"description": "Register by Email | 邮箱注册", "tags": ["publicuser"], "summary": "Register by Email | 邮箱注册", "operationId": "RegisterByEmail", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/RegisterByEmailReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/user/register_by_sms": {"post": {"description": "Register by SMS | 短信注册", "tags": ["publicuser"], "summary": "Register by SMS | 短信注册", "operationId": "RegisterBySms", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/RegisterBySmsReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/user/reset_password_by_email": {"post": {"description": "Reset password by <PERSON><PERSON> | 通过邮箱重置密码", "tags": ["publicuser"], "summary": "Reset password by <PERSON><PERSON> | 通过邮箱重置密码", "operationId": "ResetPasswordByEmail", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/ResetPasswordByEmailReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/user/reset_password_by_sms": {"post": {"description": "Reset password by Sms | 通过短信重置密码", "tags": ["publicuser"], "summary": "Reset password by Sms | 通过短信重置密码", "operationId": "ResetPasswordBySms", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/ResetPasswordBySmsReq"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}, "/user/update": {"post": {"description": "Update user information | 更新用户", "tags": ["user"], "summary": "Update user information | 更新用户", "operationId": "UpdateUser", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "$ref": "#/definitions/UserInfo"}}], "responses": {"200": {"description": "BaseMsgResp", "schema": {"$ref": "#/definitions/BaseMsgResp"}}}}}}, "definitions": {"ApiAuthorityInfo": {"description": "The response data of api authorization | API授权数据", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "ApiAuthorityListInfo": {"description": "The  data of api authorization list | API授权列表数据", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "ApiAuthorityListResp": {"description": "The response data of api authorization list | API授权列表返回数据", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "ApiInfo": {"description": "The API information | API信息", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "ApiInfoResp": {"description": "API information response | API信息返回体", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "ApiListInfo": {"description": "API list data | API 列表数据", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "ApiListReq": {"description": "Get API list request params | API列表请求参数", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "ApiListResp": {"description": "The response data of API list | API列表数据", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "BaseDataInfo": {"description": "The basic response with data | 基础带数据信息", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "BaseIDInfo": {"description": "The base ID response data | 基础ID信息", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "BaseListInfo": {"description": "The basic response with data | 基础带数据信息", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "BaseMsgResp": {"description": "The basic response without data | 基础不带数据信息", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "BaseUUIDInfo": {"description": "The base UUID response data | 基础UUID信息", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "CallbackResp": {"description": "The oauth callback response data | Oauth回调数据", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "CaptchaInfo": {"description": "The information of <PERSON><PERSON><PERSON> | 验证码数据", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "CaptchaResp": {"description": "The response data of captcha | 验证码返回数据", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "ChangePasswordReq": {"description": "change user's password request | 修改密码请求参数", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "ConfigurationInfo": {"description": "The response data of configuration information | 参数配置信息", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "ConfigurationInfoResp": {"description": "Configuration information response | 参数配置信息返回体", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "ConfigurationListInfo": {"description": "Configuration list data | 参数配置列表数据", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "ConfigurationListReq": {"description": "Get configuration list request params | 参数配置列表请求参数", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "ConfigurationListResp": {"description": "The response data of configuration list | 参数配置列表数据", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "CreateOrUpdateApiAuthorityReq": {"description": "Create or update api authorization information request | 创建或更新API授权信息", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "DepartmentInfo": {"description": "The response data of department information | 部门信息", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "DepartmentInfoResp": {"description": "Department information response | 部门信息返回体", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "DepartmentListInfo": {"description": "Department list data | 部门列表数据", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "DepartmentListReq": {"description": "Get department list request params | 部门列表请求参数", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "DepartmentListResp": {"description": "The response data of department list | 部门列表数据", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "DictionaryDetailInfo": {"description": "The response data of dictionary detail information | 字典键值信息", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "DictionaryDetailInfoResp": {"description": "DictionaryDetail information response | 字典键值信息返回体", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "DictionaryDetailListInfo": {"description": "DictionaryDetail list data | 字典键值列表数据", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "DictionaryDetailListReq": {"description": "Get dictionary detail list request params | 字典键值列表请求参数", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "DictionaryDetailListResp": {"description": "The response data of dictionary detail list | 字典键值列表数据", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "DictionaryInfo": {"description": "The response data of dictionary information | 字典信息", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "DictionaryInfoResp": {"description": "Dictionary information response | 字典信息返回体", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "DictionaryListInfo": {"description": "Dictionary list data | 字典列表数据", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "DictionaryListReq": {"description": "Get dictionary list request params | 字典列表请求参数", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "DictionaryListResp": {"description": "The response data of dictionary list | 字典列表数据", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "EmailCaptchaReq": {"description": "The email captcha request | 邮箱验证码请求参数", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "EmailLogInfo": {"description": "The response data of email log information | 电子邮件日志信息", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "EmailLogInfoResp": {"description": "EmailLog information response | 电子邮件日志信息返回体", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "EmailLogListInfo": {"description": "EmailLog list data | 电子邮件日志列表数据", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "EmailLogListReq": {"description": "Get email log list request params | 电子邮件日志列表请求参数", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "EmailLogListResp": {"description": "The response data of email log list | 电子邮件日志列表数据", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "EmailProviderInfo": {"description": "The response data of email provider information | 邮箱服务配置信息", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "EmailProviderInfoResp": {"description": "EmailProvider information response | 邮箱服务配置信息返回体", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "EmailProviderListInfo": {"description": "EmailProvider list data | 邮箱服务配置列表数据", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "EmailProviderListReq": {"description": "Get email provider list request params | 邮箱服务配置列表请求参数", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "EmailProviderListResp": {"description": "The response data of email provider list | 邮箱服务配置列表数据", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "HealthCheckResp": {"x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "IDPathReq": {"description": "Basic ID request | 基础ID地址参数请求", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "IDReq": {"description": "Basic ID request | 基础ID参数请求", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "IDsReq": {"description": "Basic IDs request | 基础ID数组参数请求", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "LoginByEmailReq": {"description": "Log in by email request | 邮箱登录参数", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "LoginBySmsReq": {"description": "Log in by SMS request | 短信登录参数", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "LoginInfo": {"description": "The log in information | 登陆返回的数据信息", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "LoginReq": {"description": "Login request | 登录参数", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "LoginResp": {"description": "The log in response data | 登录返回数据", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "MenuAuthorityInfoReq": {"description": "Create or update menu authorization information request params | 创建或更新菜单授权信息参数", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "MenuAuthorityInfoResp": {"description": "Menu authorization response data | 菜单授权信息数据", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "MenuInfo": {"description": "The response data of menu information | 菜单信息", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "MenuInfoResp": {"description": "Menu information response | 菜单信息返回体", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "MenuListInfo": {"description": "Menu list data | Menu列表数据", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "MenuListResp": {"description": "The response data of menu list | 菜单列表返回数据", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "MenuPlainInfo": {"description": "Menu information plain | 菜单信息无嵌套", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "MenuPlainInfoListResp": {"description": "Menu list data response | 菜单列表数据返回体", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "Meta": {"description": "The meta data of menu | 菜单的meta数据", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "OauthLoginReq": {"description": "Oauth log in request | Oauth 登录请求", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "OauthProviderInfo": {"description": "The response data of oauth provider information | 第三方信息", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "OauthProviderInfoResp": {"description": "Oauth provider information response | 第三方信息返回体", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "OauthProviderListInfo": {"description": "OauthProvider list data | 第三方列表数据", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "OauthProviderListReq": {"description": "Get oauth provider list request params | 第三方列表请求参数", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "OauthProviderListResp": {"description": "The response data of oauth provider list | 第三方列表数据", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "PageInfo": {"description": "The page request parameters | 列表请求参数", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "PermCodeResp": {"description": "The permission code for front end permission control | 权限码： 用于前端权限控制", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "PositionInfo": {"description": "The response data of position information | 职位信息", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "PositionInfoResp": {"description": "Position information response | 职位信息返回体", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "PositionListInfo": {"description": "Position list data | 职位列表数据", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "PositionListReq": {"description": "Get position list request params | 职位列表请求参数", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "PositionListResp": {"description": "The response data of position list | 职位列表数据", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "ProfileInfo": {"description": "The profile information | 个人信息", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "ProfileResp": {"description": "The profile response data | 个人信息返回数据", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "RedirectInfo": {"description": "Redirect information | 跳转网址", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "RedirectResp": {"description": "Redirect response | 跳转网址返回信息", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "RefreshTokenInfo": {"description": "Refresh token information | 刷新令牌信息", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "RefreshTokenResp": {"description": "Refresh token response data | 刷新令牌响应数据", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "RegisterByEmailReq": {"description": "Register by email request | 邮箱注册参数", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "RegisterBySmsReq": {"description": "Register by SMS request | 短信注册参数", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "RegisterReq": {"description": "register request | 注册参数", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "ResetPasswordByEmailReq": {"description": "Reset password by email request | 通过邮箱重置密码请求", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "ResetPasswordBySmsReq": {"description": "Reset password by SMS request | 通过短信重置密码请求", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "RoleInfo": {"description": "The response data of role information | 角色信息", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "RoleInfoResp": {"description": "Role information response | 角色信息返回体", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "RoleInfoSimple": {"description": "The simple role data | 简单的角色数据", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "RoleListInfo": {"description": "Role list data | 角色列表数据", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "RoleListReq": {"description": "Get role list request params | 角色列表请求参数", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "RoleListResp": {"description": "The response data of role list | 角色列表数据", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "SendEmailReq": {"x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "SendSmsReq": {"x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "SmsCaptchaReq": {"description": "The sms captcha request | 短信验证码请求参数", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "SmsLogInfo": {"description": "The response data of sms log information | 短信日志信息", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "SmsLogInfoResp": {"description": "SmsLog information response | 短信日志信息返回体", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "SmsLogListInfo": {"description": "SmsLog list data | 短信日志列表数据", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "SmsLogListReq": {"description": "Get sms log list request params | 短信日志列表请求参数", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "SmsLogListResp": {"description": "The response data of sms log list | 短信日志列表数据", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "SmsProviderInfo": {"description": "The response data of sms provider information | 短信配置信息", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "SmsProviderInfoResp": {"description": "SmsProvider information response | 短信配置信息返回体", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "SmsProviderListInfo": {"description": "SmsProvider list data | 短信配置列表数据", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "SmsProviderListReq": {"description": "Get sms provider list request params | 短信配置列表请求参数", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "SmsProviderListResp": {"description": "The response data of sms provider list | 短信配置列表数据", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "TaskInfo": {"description": "The response data of task information | 定时任务信息", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "TaskInfoResp": {"description": "Task information response | 定时任务信息返回体", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "TaskListInfo": {"description": "Task list data | 定时任务列表数据", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "TaskListReq": {"description": "Get task list request params | 定时任务列表请求参数", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "TaskListResp": {"description": "The response data of task list | 定时任务列表数据", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "TaskLogInfo": {"description": "The response data of task log information | 任务日志信息", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "TaskLogInfoResp": {"description": "TaskLog information response | 任务日志信息返回体", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "TaskLogListInfo": {"description": "TaskLog list data | 任务日志列表数据", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "TaskLogListReq": {"description": "Get task log list request params | 任务日志列表请求参数", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "TaskLogListResp": {"description": "The response data of task log list | 任务日志列表数据", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "TokenInfo": {"description": "The response data of token information | 令牌信息", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "TokenInfoResp": {"description": "Token information response | Token信息返回体", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "TokenListInfo": {"description": "Token list data | Token列表数据", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "TokenListReq": {"description": "Get token list request params | 令牌列表请求参数", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "TokenListResp": {"description": "The response data of token list | 令牌列表数据", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "UUIDReq": {"description": "Basic UUID request | 基础UUID参数请求", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "UUIDsReq": {"description": "Basic UUID array request | 基础UUID数组参数请求", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "UserBaseIDInfo": {"description": "The  data of user's basic information | 用户基本信息", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "UserBaseIDInfoResp": {"description": "The response data of user's basic information | 用户基本信息返回数据", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "UserInfo": {"description": "The response data of user information | 用户信息", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "UserInfoResp": {"description": "User information response | 用户信息返回体", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "UserListInfo": {"description": "User list data | 用户列表数据", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "UserListReq": {"description": "Get user list request params | 用户列表请求参数", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}, "UserListResp": {"description": "The response data of user list | 用户列表数据", "x-go-package": "github.com/suyuan32/simple-admin-core/api/internal/types"}}, "securityDefinitions": {"Token": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header"}}, "security": [{"Token": []}]}