// Code generated by goctl. DO NOT EDIT.
package handler

import (
	"net/http"

	analytics "seevision.cn/server/meet-analytics-api/internal/handler/analytics"
	base "seevision.cn/server/meet-analytics-api/internal/handler/base"
	health "seevision.cn/server/meet-analytics-api/internal/handler/health"
	"seevision.cn/server/meet-analytics-api/internal/svc"

	"github.com/zeromicro/go-zero/rest"
)

func RegisterHandlers(server *rest.Server, serverCtx *svc.ServiceContext) {
	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodGet,
				Path:    "/init/database",
				Handler: base.InitDatabaseHandler(serverCtx),
			},
		},
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.SignMiddleware},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/report",
					Handler: analytics.UniversalReportHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/app/v1/analytics"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodGet,
				Path:    "/health",
				Handler: health.HealthCheckHandler(serverCtx),
			},
		},
	)
}
