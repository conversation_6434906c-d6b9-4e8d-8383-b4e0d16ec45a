// Code generated by goctl. DO NOT EDIT.
// goctls v1.11.4

package handler

import (
	"net/http"

	analytics "seevision.cn/server/meet-admin-api/internal/handler/analytics"
	analyticsapplication "seevision.cn/server/meet-admin-api/internal/handler/analyticsapplication"
	analyticscohort "seevision.cn/server/meet-admin-api/internal/handler/analyticscohort"
	analyticsdailysummary "seevision.cn/server/meet-admin-api/internal/handler/analyticsdailysummary"
	analyticsdevice "seevision.cn/server/meet-admin-api/internal/handler/analyticsdevice"
	analyticsevent "seevision.cn/server/meet-admin-api/internal/handler/analyticsevent"
	analyticseventdefinition "seevision.cn/server/meet-admin-api/internal/handler/analyticseventdefinition"
	analyticsrealtimemetric "seevision.cn/server/meet-admin-api/internal/handler/analyticsrealtimemetric"
	analyticsuser "seevision.cn/server/meet-admin-api/internal/handler/analyticsuser"
	appapi "seevision.cn/server/meet-admin-api/internal/handler/appapi"
	approle "seevision.cn/server/meet-admin-api/internal/handler/approle"
	appversion "seevision.cn/server/meet-admin-api/internal/handler/appversion"
	area "seevision.cn/server/meet-admin-api/internal/handler/area"
	article "seevision.cn/server/meet-admin-api/internal/handler/article"
	authority "seevision.cn/server/meet-admin-api/internal/handler/authority"
	banner "seevision.cn/server/meet-admin-api/internal/handler/banner"
	base "seevision.cn/server/meet-admin-api/internal/handler/base"
	bololexicon "seevision.cn/server/meet-admin-api/internal/handler/bololexicon"
	city "seevision.cn/server/meet-admin-api/internal/handler/city"
	funnel "seevision.cn/server/meet-admin-api/internal/handler/funnel"
	group "seevision.cn/server/meet-admin-api/internal/handler/group"
	groupfollow "seevision.cn/server/meet-admin-api/internal/handler/groupfollow"
	groupmember "seevision.cn/server/meet-admin-api/internal/handler/groupmember"
	mainteninfo "seevision.cn/server/meet-admin-api/internal/handler/mainteninfo"
	maintenorder "seevision.cn/server/meet-admin-api/internal/handler/maintenorder"
	mallapplication "seevision.cn/server/meet-admin-api/internal/handler/mallapplication"
	malldelivery "seevision.cn/server/meet-admin-api/internal/handler/malldelivery"
	malllicense "seevision.cn/server/meet-admin-api/internal/handler/malllicense"
	malllicensedevice "seevision.cn/server/meet-admin-api/internal/handler/malllicensedevice"
	mallorder "seevision.cn/server/meet-admin-api/internal/handler/mallorder"
	mallorderitem "seevision.cn/server/meet-admin-api/internal/handler/mallorderitem"
	mallorderlog "seevision.cn/server/meet-admin-api/internal/handler/mallorderlog"
	mallpayment "seevision.cn/server/meet-admin-api/internal/handler/mallpayment"
	mallpaymentlog "seevision.cn/server/meet-admin-api/internal/handler/mallpaymentlog"
	mallproduct "seevision.cn/server/meet-admin-api/internal/handler/mallproduct"
	mallproductcategory "seevision.cn/server/meet-admin-api/internal/handler/mallproductcategory"
	mallproductquota "seevision.cn/server/meet-admin-api/internal/handler/mallproductquota"
	mallproductsku "seevision.cn/server/meet-admin-api/internal/handler/mallproductsku"
	mallrefund "seevision.cn/server/meet-admin-api/internal/handler/mallrefund"
	mallrefundexpress "seevision.cn/server/meet-admin-api/internal/handler/mallrefundexpress"
	mallsubscription "seevision.cn/server/meet-admin-api/internal/handler/mallsubscription"
	mallsupplier "seevision.cn/server/meet-admin-api/internal/handler/mallsupplier"
	mallsuppliercommission "seevision.cn/server/meet-admin-api/internal/handler/mallsuppliercommission"
	mallusagerecord "seevision.cn/server/meet-admin-api/internal/handler/mallusagerecord"
	product "seevision.cn/server/meet-admin-api/internal/handler/product"
	province "seevision.cn/server/meet-admin-api/internal/handler/province"
	rentaldevice "seevision.cn/server/meet-admin-api/internal/handler/rentaldevice"
	rentaldevicemessage "seevision.cn/server/meet-admin-api/internal/handler/rentaldevicemessage"
	reply "seevision.cn/server/meet-admin-api/internal/handler/reply"
	segment "seevision.cn/server/meet-admin-api/internal/handler/segment"
	segmentarticlerecord "seevision.cn/server/meet-admin-api/internal/handler/segmentarticlerecord"
	street "seevision.cn/server/meet-admin-api/internal/handler/street"
	sync "seevision.cn/server/meet-admin-api/internal/handler/sync"
	tag "seevision.cn/server/meet-admin-api/internal/handler/tag"
	tagrecord "seevision.cn/server/meet-admin-api/internal/handler/tagrecord"
	user "seevision.cn/server/meet-admin-api/internal/handler/user"
	useraddres "seevision.cn/server/meet-admin-api/internal/handler/useraddres"
	"seevision.cn/server/meet-admin-api/internal/svc"

	"github.com/zeromicro/go-zero/rest"
)

func RegisterHandlers(server *rest.Server, serverCtx *svc.ServiceContext) {
	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodGet,
				Path:    "/init/database",
				Handler: base.InitDatabaseHandler(serverCtx),
			},
		},
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Authority},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/sync/article/likeNum",
					Handler: sync.SyncArticleLikeNumHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/sync/article/commentNum",
					Handler: sync.SyncArticleCommentNumHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/sync/article/viewNum",
					Handler: sync.SyncArticleViewNumHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/sync/group/info",
					Handler: sync.SyncGroupInfoHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/sync/tag/info",
					Handler: sync.SyncTagInfoHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/sync/followAndFans/info",
					Handler: sync.SyncFollowAndFansInfoHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/sync/user/info",
					Handler: sync.SyncUserInfoHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/sync/article/data",
					Handler: sync.SyncArticleDataHandler(serverCtx),
				},
			}...,
		),
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Authority},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/group_follow/create",
					Handler: groupfollow.CreateGroupFollowHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/group_follow/update",
					Handler: groupfollow.UpdateGroupFollowHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/group_follow/delete",
					Handler: groupfollow.DeleteGroupFollowHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/group_follow/list",
					Handler: groupfollow.GetGroupFollowListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/group_follow",
					Handler: groupfollow.GetGroupFollowByIdHandler(serverCtx),
				},
			}...,
		),
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Authority},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/group_member/create",
					Handler: groupmember.CreateGroupMemberHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/group_member/update",
					Handler: groupmember.UpdateGroupMemberHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/group_member/delete",
					Handler: groupmember.DeleteGroupMemberHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/group_member/list",
					Handler: groupmember.GetGroupMemberListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/group_member",
					Handler: groupmember.GetGroupMemberByIdHandler(serverCtx),
				},
			}...,
		),
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Authority},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/group/create",
					Handler: group.CreateGroupHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/group/update",
					Handler: group.UpdateGroupHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/group/delete",
					Handler: group.DeleteGroupHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/group/list",
					Handler: group.GetGroupListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/group",
					Handler: group.GetGroupByIdHandler(serverCtx),
				},
			}...,
		),
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Authority},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/tag/create",
					Handler: tag.CreateTagHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/tag/update",
					Handler: tag.UpdateTagHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/tag/delete",
					Handler: tag.DeleteTagHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/tag/list",
					Handler: tag.GetTagListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/tag",
					Handler: tag.GetTagByIdHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/tag/group",
					Handler: tag.GetTagListByGroupIdHandler(serverCtx),
				},
			}...,
		),
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Authority},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/tag_record/create",
					Handler: tagrecord.CreateTagRecordHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/tag_record/update",
					Handler: tagrecord.UpdateTagRecordHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/tag_record/delete",
					Handler: tagrecord.DeleteTagRecordHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/tag_record/list",
					Handler: tagrecord.GetTagRecordListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/tag_record",
					Handler: tagrecord.GetTagRecordByIdHandler(serverCtx),
				},
			}...,
		),
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Authority},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/product/create",
					Handler: product.CreateProductHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/product/update",
					Handler: product.UpdateProductHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/product/delete",
					Handler: product.DeleteProductHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/product/list",
					Handler: product.GetProductListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/product",
					Handler: product.GetProductByIdHandler(serverCtx),
				},
			}...,
		),
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Authority},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/mainten_info/create",
					Handler: mainteninfo.CreateMaintenInfoHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/mainten_info/update",
					Handler: mainteninfo.UpdateMaintenInfoHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/mainten_info/delete",
					Handler: mainteninfo.DeleteMaintenInfoHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/mainten_info/list",
					Handler: mainteninfo.GetMaintenInfoListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/mainten_info",
					Handler: mainteninfo.GetMaintenInfoByIdHandler(serverCtx),
				},
			}...,
		),
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Authority},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/mainten_order/create",
					Handler: maintenorder.CreateMaintenOrderHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/mainten_order/update",
					Handler: maintenorder.UpdateMaintenOrderHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/mainten_order/delete",
					Handler: maintenorder.DeleteMaintenOrderHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/mainten_order/list",
					Handler: maintenorder.GetMaintenOrderListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/mainten_order",
					Handler: maintenorder.GetMaintenOrderByIdHandler(serverCtx),
				},
			}...,
		),
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Authority},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/province/create",
					Handler: province.CreateProvinceHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/province/update",
					Handler: province.UpdateProvinceHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/province/delete",
					Handler: province.DeleteProvinceHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/province/list",
					Handler: province.GetProvinceListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/province",
					Handler: province.GetProvinceByIdHandler(serverCtx),
				},
			}...,
		),
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Authority},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/city/create",
					Handler: city.CreateCityHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/city/update",
					Handler: city.UpdateCityHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/city/delete",
					Handler: city.DeleteCityHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/city/list",
					Handler: city.GetCityListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/city",
					Handler: city.GetCityByIdHandler(serverCtx),
				},
			}...,
		),
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Authority},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/area/create",
					Handler: area.CreateAreaHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/area/update",
					Handler: area.UpdateAreaHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/area/delete",
					Handler: area.DeleteAreaHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/area/list",
					Handler: area.GetAreaListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/area",
					Handler: area.GetAreaByIdHandler(serverCtx),
				},
			}...,
		),
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Authority},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/street/create",
					Handler: street.CreateStreetHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/street/update",
					Handler: street.UpdateStreetHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/street/delete",
					Handler: street.DeleteStreetHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/street/list",
					Handler: street.GetStreetListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/street",
					Handler: street.GetStreetByIdHandler(serverCtx),
				},
			}...,
		),
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Authority},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/user/create",
					Handler: user.CreateUserHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/user/update",
					Handler: user.UpdateUserHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/user/delete",
					Handler: user.DeleteUserHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/user/list",
					Handler: user.GetUserListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/user",
					Handler: user.GetUserByIdHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/user/update/forbidden",
					Handler: user.UpdateUserForbiddenTimeHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/user/update/muted",
					Handler: user.UpdateUserMutedTimeHandler(serverCtx),
				},
			}...,
		),
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Authority},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/banner/create",
					Handler: banner.CreateBannerHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/banner/update",
					Handler: banner.UpdateBannerHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/banner/delete",
					Handler: banner.DeleteBannerHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/banner/list",
					Handler: banner.GetBannerListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/banner",
					Handler: banner.GetBannerByIdHandler(serverCtx),
				},
			}...,
		),
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Authority},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/article/create",
					Handler: article.CreateArticleHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/article/update",
					Handler: article.UpdateArticleHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/article/delete",
					Handler: article.DeleteArticleHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/article/list",
					Handler: article.GetArticleListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/article",
					Handler: article.GetArticleByIdHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/article/top",
					Handler: article.TopArticleHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/article/cancelTop",
					Handler: article.CancelTopArticleHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/article/audit",
					Handler: article.AuditArticleHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/article/getArticleListWithoutDraft",
					Handler: article.GetArticleListWithoutDraftHandler(serverCtx),
				},
			}...,
		),
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Authority},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/segment/create",
					Handler: segment.CreateSegmentHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/segment/update",
					Handler: segment.UpdateSegmentHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/segment/delete",
					Handler: segment.DeleteSegmentHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/segment/list",
					Handler: segment.GetSegmentListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/segment",
					Handler: segment.GetSegmentByIdHandler(serverCtx),
				},
			}...,
		),
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Authority},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/user_addres/create",
					Handler: useraddres.CreateUserAddresHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/user_addres/update",
					Handler: useraddres.UpdateUserAddresHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/user_addres/delete",
					Handler: useraddres.DeleteUserAddresHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/user_addres/list",
					Handler: useraddres.GetUserAddresListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/user_addres",
					Handler: useraddres.GetUserAddresByIdHandler(serverCtx),
				},
			}...,
		),
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Authority},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/segment_article_record/create",
					Handler: segmentarticlerecord.CreateSegmentArticleRecordHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/segment_article_record/update",
					Handler: segmentarticlerecord.UpdateSegmentArticleRecordHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/segment_article_record/delete",
					Handler: segmentarticlerecord.DeleteSegmentArticleRecordHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/segment_article_record/list",
					Handler: segmentarticlerecord.GetSegmentArticleRecordListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/segment_article_record",
					Handler: segmentarticlerecord.GetSegmentArticleRecordByIdHandler(serverCtx),
				},
			}...,
		),
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Authority},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/reply/create",
					Handler: reply.CreateReplyHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/reply/update",
					Handler: reply.UpdateReplyHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/reply/delete",
					Handler: reply.DeleteReplyHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/reply/list",
					Handler: reply.GetReplyListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/reply",
					Handler: reply.GetReplyByIdHandler(serverCtx),
				},
			}...,
		),
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Authority},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/app_version/create",
					Handler: appversion.CreateAppVersionHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/app_version/update",
					Handler: appversion.UpdateAppVersionHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/app_version/delete",
					Handler: appversion.DeleteAppVersionHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/app_version/list",
					Handler: appversion.GetAppVersionListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/app_version",
					Handler: appversion.GetAppVersionByIdHandler(serverCtx),
				},
			}...,
		),
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Authority},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/approle/create",
					Handler: approle.CreateApproleHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/approle/update",
					Handler: approle.UpdateApproleHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/approle/delete",
					Handler: approle.DeleteApproleHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/approle/list",
					Handler: approle.GetApproleListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/approle",
					Handler: approle.GetApproleByIdHandler(serverCtx),
				},
			}...,
		),
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Authority},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/appapi/create",
					Handler: appapi.CreateAppapiHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/appapi/update",
					Handler: appapi.UpdateAppapiHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/appapi/delete",
					Handler: appapi.DeleteAppapiHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/appapi/list",
					Handler: appapi.GetAppapiListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/appapi",
					Handler: appapi.GetAppapiByIdHandler(serverCtx),
				},
			}...,
		),
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Authority},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/authority/api/create_or_update",
					Handler: authority.CreateOrUpdateApiAuthorityHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/authority/api/role",
					Handler: authority.GetApiAuthorityHandler(serverCtx),
				},
			}...,
		),
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Authority},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/mall_order/create",
					Handler: mallorder.CreateMallOrderHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/mall_order/update",
					Handler: mallorder.UpdateMallOrderHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/mall_order/delete",
					Handler: mallorder.DeleteMallOrderHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/mall_order/list",
					Handler: mallorder.GetMallOrderListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/mall_order",
					Handler: mallorder.GetMallOrderByIdHandler(serverCtx),
				},
			}...,
		),
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Authority},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/mall_order_item/create",
					Handler: mallorderitem.CreateMallOrderItemHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/mall_order_item/update",
					Handler: mallorderitem.UpdateMallOrderItemHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/mall_order_item/delete",
					Handler: mallorderitem.DeleteMallOrderItemHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/mall_order_item/list",
					Handler: mallorderitem.GetMallOrderItemListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/mall_order_item",
					Handler: mallorderitem.GetMallOrderItemByIdHandler(serverCtx),
				},
			}...,
		),
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Authority},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/mall_order_log/create",
					Handler: mallorderlog.CreateMallOrderLogHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/mall_order_log/update",
					Handler: mallorderlog.UpdateMallOrderLogHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/mall_order_log/delete",
					Handler: mallorderlog.DeleteMallOrderLogHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/mall_order_log/list",
					Handler: mallorderlog.GetMallOrderLogListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/mall_order_log",
					Handler: mallorderlog.GetMallOrderLogByIdHandler(serverCtx),
				},
			}...,
		),
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Authority},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/mall_product/create",
					Handler: mallproduct.CreateMallProductHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/mall_product/update",
					Handler: mallproduct.UpdateMallProductHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/mall_product/delete",
					Handler: mallproduct.DeleteMallProductHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/mall_product/list",
					Handler: mallproduct.GetMallProductListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/mall_product",
					Handler: mallproduct.GetMallProductByIdHandler(serverCtx),
				},
			}...,
		),
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Authority},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/mall_product_category/create",
					Handler: mallproductcategory.CreateMallProductCategoryHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/mall_product_category/update",
					Handler: mallproductcategory.UpdateMallProductCategoryHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/mall_product_category/delete",
					Handler: mallproductcategory.DeleteMallProductCategoryHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/mall_product_category/list",
					Handler: mallproductcategory.GetMallProductCategoryListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/mall_product_category",
					Handler: mallproductcategory.GetMallProductCategoryByIdHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/mall_product_category/tree",
					Handler: mallproductcategory.GetMallProductCategoryTreeHandler(serverCtx),
				},
			}...,
		),
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Authority},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/mall_product_quota/create",
					Handler: mallproductquota.CreateMallProductQuotaHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/mall_product_quota/update",
					Handler: mallproductquota.UpdateMallProductQuotaHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/mall_product_quota/delete",
					Handler: mallproductquota.DeleteMallProductQuotaHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/mall_product_quota/list",
					Handler: mallproductquota.GetMallProductQuotaListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/mall_product_quota",
					Handler: mallproductquota.GetMallProductQuotaByIdHandler(serverCtx),
				},
			}...,
		),
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Authority},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/mall_product_sku/create",
					Handler: mallproductsku.CreateMallProductSkuHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/mall_product_sku/update",
					Handler: mallproductsku.UpdateMallProductSkuHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/mall_product_sku/delete",
					Handler: mallproductsku.DeleteMallProductSkuHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/mall_product_sku/list",
					Handler: mallproductsku.GetMallProductSkuListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/mall_product_sku",
					Handler: mallproductsku.GetMallProductSkuByIdHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/mall_product_sku/batch_create",
					Handler: mallproductsku.BatchCreateMallProductSkuHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/mall_product_sku/batch_process",
					Handler: mallproductsku.BatchProcessMallProductSkuHandler(serverCtx),
				},
			}...,
		),
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Authority},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/mall_subscription/create",
					Handler: mallsubscription.CreateMallSubscriptionHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/mall_subscription/update",
					Handler: mallsubscription.UpdateMallSubscriptionHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/mall_subscription/delete",
					Handler: mallsubscription.DeleteMallSubscriptionHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/mall_subscription/list",
					Handler: mallsubscription.GetMallSubscriptionListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/mall_subscription",
					Handler: mallsubscription.GetMallSubscriptionByIdHandler(serverCtx),
				},
			}...,
		),
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Authority},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/mall_usage_record/create",
					Handler: mallusagerecord.CreateMallUsageRecordHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/mall_usage_record/update",
					Handler: mallusagerecord.UpdateMallUsageRecordHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/mall_usage_record/delete",
					Handler: mallusagerecord.DeleteMallUsageRecordHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/mall_usage_record/list",
					Handler: mallusagerecord.GetMallUsageRecordListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/mall_usage_record",
					Handler: mallusagerecord.GetMallUsageRecordByIdHandler(serverCtx),
				},
			}...,
		),
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Authority},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/mall_refund_express/create",
					Handler: mallrefundexpress.CreateMallRefundExpressHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/mall_refund_express/update",
					Handler: mallrefundexpress.UpdateMallRefundExpressHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/mall_refund_express/delete",
					Handler: mallrefundexpress.DeleteMallRefundExpressHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/mall_refund_express/list",
					Handler: mallrefundexpress.GetMallRefundExpressListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/mall_refund_express",
					Handler: mallrefundexpress.GetMallRefundExpressByIdHandler(serverCtx),
				},
			}...,
		),
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Authority},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/mall_delivery/create",
					Handler: malldelivery.CreateMallDeliveryHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/mall_delivery/update",
					Handler: malldelivery.UpdateMallDeliveryHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/mall_delivery/delete",
					Handler: malldelivery.DeleteMallDeliveryHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/mall_delivery/list",
					Handler: malldelivery.GetMallDeliveryListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/mall_delivery",
					Handler: malldelivery.GetMallDeliveryByIdHandler(serverCtx),
				},
			}...,
		),
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Authority},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/mall_payment/create",
					Handler: mallpayment.CreateMallPaymentHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/mall_payment/update",
					Handler: mallpayment.UpdateMallPaymentHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/mall_payment/delete",
					Handler: mallpayment.DeleteMallPaymentHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/mall_payment/list",
					Handler: mallpayment.GetMallPaymentListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/mall_payment",
					Handler: mallpayment.GetMallPaymentByIdHandler(serverCtx),
				},
			}...,
		),
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Authority},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/mall_payment_log/create",
					Handler: mallpaymentlog.CreateMallPaymentLogHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/mall_payment_log/update",
					Handler: mallpaymentlog.UpdateMallPaymentLogHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/mall_payment_log/delete",
					Handler: mallpaymentlog.DeleteMallPaymentLogHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/mall_payment_log/list",
					Handler: mallpaymentlog.GetMallPaymentLogListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/mall_payment_log",
					Handler: mallpaymentlog.GetMallPaymentLogByIdHandler(serverCtx),
				},
			}...,
		),
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Authority},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/mall_refund/create",
					Handler: mallrefund.CreateMallRefundHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/mall_refund/update",
					Handler: mallrefund.UpdateMallRefundHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/mall_refund/delete",
					Handler: mallrefund.DeleteMallRefundHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/mall_refund/list",
					Handler: mallrefund.GetMallRefundListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/mall_refund",
					Handler: mallrefund.GetMallRefundByIdHandler(serverCtx),
				},
			}...,
		),
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Authority},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/mall_license/list",
					Handler: malllicense.GetMallLicenseListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/mall_license",
					Handler: malllicense.GetMallLicenseByIdHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/mall_license/batch_create",
					Handler: malllicense.BatchCreateMallLicenseHandler(serverCtx),
				},
			}...,
		),
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Authority},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/mall_license_device/list",
					Handler: malllicensedevice.GetMallLicenseDeviceListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/mall_license_device",
					Handler: malllicensedevice.GetMallLicenseDeviceByIdHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/mall_license_device/unbind",
					Handler: malllicensedevice.UnbindMallLicenseDeviceHandler(serverCtx),
				},
			}...,
		),
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Authority},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/mall_supplier/create",
					Handler: mallsupplier.CreateMallSupplierHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/mall_supplier/update",
					Handler: mallsupplier.UpdateMallSupplierHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/mall_supplier/delete",
					Handler: mallsupplier.DeleteMallSupplierHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/mall_supplier/list",
					Handler: mallsupplier.GetMallSupplierListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/mall_supplier",
					Handler: mallsupplier.GetMallSupplierByIdHandler(serverCtx),
				},
			}...,
		),
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Authority},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/mall_supplier_commission/list",
					Handler: mallsuppliercommission.GetMallSupplierCommissionListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/mall_supplier_commission",
					Handler: mallsuppliercommission.GetMallSupplierCommissionByIdHandler(serverCtx),
				},
			}...,
		),
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Authority},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/mall_application/create",
					Handler: mallapplication.CreateMallApplicationHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/mall_application/update",
					Handler: mallapplication.UpdateMallApplicationHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/mall_application/delete",
					Handler: mallapplication.DeleteMallApplicationHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/mall_application/list",
					Handler: mallapplication.GetMallApplicationListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/mall_application",
					Handler: mallapplication.GetMallApplicationByIdHandler(serverCtx),
				},
			}...,
		),
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Authority},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/bolo_lexicon/create",
					Handler: bololexicon.CreateBoloLexiconHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/bolo_lexicon/update",
					Handler: bololexicon.UpdateBoloLexiconHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/bolo_lexicon/delete",
					Handler: bololexicon.DeleteBoloLexiconHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/bolo_lexicon",
					Handler: bololexicon.GetBoloLexiconByIdHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/bolo_lexicon/list",
					Handler: bololexicon.GetBoloLexiconListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/bolo_lexicon/import",
					Handler: bololexicon.ImportBoloLexiconHandler(serverCtx),
				},
			}...,
		),
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Authority},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/dashboard",
					Handler: analytics.GetAnalyticsDashboardHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/universalStats",
					Handler: analytics.GetUniversalStatsHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/dashboard/userTrends",
					Handler: analytics.GetUserTrendsHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/dashboard/eventTrends",
					Handler: analytics.GetEventTrendsHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/dashboard/geographic",
					Handler: analytics.GetGeographicDataHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/dashboard/userBehaviorHeatmap",
					Handler: analytics.GetUserBehaviorHeatmapHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/dashboard/realtime",
					Handler: analytics.GetRealtimeDataHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/dashboard/export",
					Handler: analytics.ExportDashboardDataHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/pathAnalysis",
					Handler: analytics.GetPathAnalysisHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/pathAnalysis/stats",
					Handler: analytics.GetPathStatsHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/pathAnalysis/conversions",
					Handler: analytics.GetEventConversionsHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/pathAnalysis/heatmap",
					Handler: analytics.GetPathHeatmapHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/pathAnalysis/export",
					Handler: analytics.ExportPathAnalysisHandler(serverCtx),
				},
			}...,
		),
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
		rest.WithPrefix("/analytics"),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Authority},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/funnel/list",
					Handler: funnel.GetFunnelListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/funnel/create",
					Handler: funnel.CreateFunnelHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/funnel/update",
					Handler: funnel.UpdateFunnelHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/funnel",
					Handler: funnel.GetFunnelByIdHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/funnel/delete",
					Handler: funnel.DeleteFunnelHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/funnel/analysis/run",
					Handler: funnel.RunFunnelAnalysisHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/funnel/step/list",
					Handler: funnel.GetFunnelStepListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/funnel/step/create",
					Handler: funnel.CreateFunnelStepHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/funnel/step/update",
					Handler: funnel.UpdateFunnelStepHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/funnel/step",
					Handler: funnel.GetFunnelStepByIdHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/funnel/step/delete",
					Handler: funnel.DeleteFunnelStepHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/funnel/condition/list",
					Handler: funnel.GetFunnelConditionListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/funnel/condition/create",
					Handler: funnel.CreateFunnelConditionHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/funnel/condition/update",
					Handler: funnel.UpdateFunnelConditionHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/funnel/condition",
					Handler: funnel.GetFunnelConditionByIdHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/funnel/condition/delete",
					Handler: funnel.DeleteFunnelConditionHandler(serverCtx),
				},
			}...,
		),
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
		rest.WithPrefix("/analytics"),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Authority},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/analytics_application/create",
					Handler: analyticsapplication.CreateAnalyticsApplicationHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/analytics_application/update",
					Handler: analyticsapplication.UpdateAnalyticsApplicationHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/analytics_application/delete",
					Handler: analyticsapplication.DeleteAnalyticsApplicationHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/analytics_application/list",
					Handler: analyticsapplication.GetAnalyticsApplicationListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/analytics_application",
					Handler: analyticsapplication.GetAnalyticsApplicationByIdHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/analytics_application/generate_api_secret",
					Handler: analyticsapplication.GenerateApiSecretHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/analytics_application/reset_api_secret",
					Handler: analyticsapplication.ResetApiSecretHandler(serverCtx),
				},
			}...,
		),
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Authority},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/analytics_cohort/create",
					Handler: analyticscohort.CreateAnalyticsCohortHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/analytics_cohort/update",
					Handler: analyticscohort.UpdateAnalyticsCohortHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/analytics_cohort/delete",
					Handler: analyticscohort.DeleteAnalyticsCohortHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/analytics_cohort/list",
					Handler: analyticscohort.GetAnalyticsCohortListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/analytics_cohort",
					Handler: analyticscohort.GetAnalyticsCohortByIdHandler(serverCtx),
				},
			}...,
		),
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Authority},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/analytics_daily_summary/create",
					Handler: analyticsdailysummary.CreateAnalyticsDailySummaryHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/analytics_daily_summary/update",
					Handler: analyticsdailysummary.UpdateAnalyticsDailySummaryHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/analytics_daily_summary/delete",
					Handler: analyticsdailysummary.DeleteAnalyticsDailySummaryHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/analytics_daily_summary/list",
					Handler: analyticsdailysummary.GetAnalyticsDailySummaryListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/analytics_daily_summary",
					Handler: analyticsdailysummary.GetAnalyticsDailySummaryByIdHandler(serverCtx),
				},
			}...,
		),
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Authority},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/analytics_device/create",
					Handler: analyticsdevice.CreateAnalyticsDeviceHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/analytics_device/update",
					Handler: analyticsdevice.UpdateAnalyticsDeviceHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/analytics_device/delete",
					Handler: analyticsdevice.DeleteAnalyticsDeviceHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/analytics_device/list",
					Handler: analyticsdevice.GetAnalyticsDeviceListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/analytics_device",
					Handler: analyticsdevice.GetAnalyticsDeviceByIdHandler(serverCtx),
				},
			}...,
		),
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Authority},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/analytics_event/create",
					Handler: analyticsevent.CreateAnalyticsEventHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/analytics_event/update",
					Handler: analyticsevent.UpdateAnalyticsEventHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/analytics_event/delete",
					Handler: analyticsevent.DeleteAnalyticsEventHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/analytics_event/list",
					Handler: analyticsevent.GetAnalyticsEventListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/analytics_event",
					Handler: analyticsevent.GetAnalyticsEventByIdHandler(serverCtx),
				},
			}...,
		),
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Authority},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/analytics_event_definition/create",
					Handler: analyticseventdefinition.CreateAnalyticsEventDefinitionHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/analytics_event_definition/update",
					Handler: analyticseventdefinition.UpdateAnalyticsEventDefinitionHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/analytics_event_definition/delete",
					Handler: analyticseventdefinition.DeleteAnalyticsEventDefinitionHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/analytics_event_definition/list",
					Handler: analyticseventdefinition.GetAnalyticsEventDefinitionListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/analytics_event_definition",
					Handler: analyticseventdefinition.GetAnalyticsEventDefinitionByIdHandler(serverCtx),
				},
			}...,
		),
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Authority},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/analytics_real_time_metric/create",
					Handler: analyticsrealtimemetric.CreateAnalyticsRealTimeMetricHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/analytics_real_time_metric/update",
					Handler: analyticsrealtimemetric.UpdateAnalyticsRealTimeMetricHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/analytics_real_time_metric/delete",
					Handler: analyticsrealtimemetric.DeleteAnalyticsRealTimeMetricHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/analytics_real_time_metric/list",
					Handler: analyticsrealtimemetric.GetAnalyticsRealTimeMetricListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/analytics_real_time_metric",
					Handler: analyticsrealtimemetric.GetAnalyticsRealTimeMetricByIdHandler(serverCtx),
				},
			}...,
		),
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Authority},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/analytics_user/create",
					Handler: analyticsuser.CreateAnalyticsUserHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/analytics_user/update",
					Handler: analyticsuser.UpdateAnalyticsUserHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/analytics_user/delete",
					Handler: analyticsuser.DeleteAnalyticsUserHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/analytics_user/list",
					Handler: analyticsuser.GetAnalyticsUserListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/analytics_user",
					Handler: analyticsuser.GetAnalyticsUserByIdHandler(serverCtx),
				},
			}...,
		),
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Authority},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/rental_device/create",
					Handler: rentaldevice.CreateRentalDeviceHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/rental_device/update",
					Handler: rentaldevice.UpdateRentalDeviceHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/rental_device/delete",
					Handler: rentaldevice.DeleteRentalDeviceHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/rental_device/list",
					Handler: rentaldevice.GetRentalDeviceListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/rental_device",
					Handler: rentaldevice.GetRentalDeviceByIdHandler(serverCtx),
				},
			}...,
		),
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Authority},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/rental_device_message/create",
					Handler: rentaldevicemessage.CreateRentalDeviceMessageHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/rental_device_message/update",
					Handler: rentaldevicemessage.UpdateRentalDeviceMessageHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/rental_device_message/delete",
					Handler: rentaldevicemessage.DeleteRentalDeviceMessageHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/rental_device_message/list",
					Handler: rentaldevicemessage.GetRentalDeviceMessageListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/rental_device_message",
					Handler: rentaldevicemessage.GetRentalDeviceMessageByIdHandler(serverCtx),
				},
			}...,
		),
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
	)
}
